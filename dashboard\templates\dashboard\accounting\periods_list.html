{% extends 'dashboard/base.html' %}

{% block title %}الفترات المحاسبية - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item active">الفترات المحاسبية</li>
{% endblock %}

{% block extra_css %}
<style>
.period-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.period-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.period-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1.5rem;
    position: relative;
}

.period-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-open { background: rgba(40, 167, 69, 0.9); }
.status-closed { background: rgba(255, 193, 7, 0.9); }
.status-finalized { background: rgba(108, 117, 125, 0.9); }

.period-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.period-actions {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.quick-stats {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-calendar-alt me-2"></i>
            الفترات المحاسبية
        </h1>
        <p class="text-muted">إدارة وتنظيم الفترات المحاسبية لحساب مستحقات المتاجر</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'accounting_period_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إنشاء فترة جديدة
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if periods %}
<div class="quick-stats">
    <div class="row text-center">
        <div class="col-md-3">
            <h3 class="text-primary mb-0">{{ periods|length }}</h3>
            <p class="text-muted mb-0">إجمالي الفترات</p>
        </div>
        <div class="col-md-3">
            <h3 class="text-success mb-0">
                {% for period in periods %}
                    {% if period.status == 'open' %}{{ forloop.counter0|add:1 }}{% endif %}
                {% empty %}0{% endfor %}
            </h3>
            <p class="text-muted mb-0">فترات مفتوحة</p>
        </div>
        <div class="col-md-3">
            <h3 class="text-warning mb-0">
                {% for period in periods %}
                    {% if period.status == 'closed' %}{{ forloop.counter0|add:1 }}{% endif %}
                {% empty %}0{% endfor %}
            </h3>
            <p class="text-muted mb-0">فترات مغلقة</p>
        </div>
        <div class="col-md-3">
            <h3 class="text-secondary mb-0">
                {% for period in periods %}
                    {% if period.status == 'finalized' %}{{ forloop.counter0|add:1 }}{% endif %}
                {% empty %}0{% endfor %}
            </h3>
            <p class="text-muted mb-0">فترات نهائية</p>
        </div>
    </div>
</div>
{% endif %}

<!-- قائمة الفترات -->
<div class="row">
    {% if periods %}
        {% for period in periods %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="period-card">
                <div class="period-header">
                    <span class="period-status status-{{ period.status }}">
                        {{ period.get_status_display }}
                    </span>
                    
                    <h5 class="mb-2 mt-3">{{ period.name }}</h5>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-calendar me-1"></i>
                        {{ period.start_date|date:"Y/m/d" }} - {{ period.end_date|date:"Y/m/d" }}
                    </p>
                    <small class="opacity-75">
                        <i class="fas fa-tag me-1"></i>
                        {{ period.get_period_type_display }}
                    </small>
                    
                    <div class="period-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ period.total_orders }}</span>
                            <span class="stat-label">طلب</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ period.total_revenue|floatformat:0 }}</span>
                            <span class="stat-label">د.ع إيرادات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ period.total_commission|floatformat:0 }}</span>
                            <span class="stat-label">د.ع عمولات</span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">تاريخ الإنشاء</small>
                            <br>
                            <strong>{{ period.created_date|date:"Y/m/d" }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">المدة</small>
                            <br>
                            <strong>
                                {% with period.end_date|timeuntil:period.start_date as duration %}
                                    {{ duration|slice:":10" }}
                                {% endwith %}
                            </strong>
                        </div>
                    </div>
                    
                    {% if period.notes %}
                    <div class="mt-3">
                        <small class="text-muted">ملاحظات:</small>
                        <p class="small mb-0">{{ period.notes|truncatechars:100 }}</p>
                    </div>
                    {% endif %}
                </div>
                
                <div class="period-actions">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group btn-group-sm">
                            <a href="{% url 'accounting_period_detail' period.pk %}" 
                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if period.status == 'open' %}
                            <a href="{% url 'calculate_period_accounts' period.pk %}" 
                               class="btn btn-outline-success" title="حساب المستحقات"
                               onclick="return confirm('هل تريد إعادة حساب مستحقات هذه الفترة؟')">
                                <i class="fas fa-calculator"></i>
                            </a>
                            {% endif %}
                            <button type="button" class="btn btn-outline-info" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#periodModal{{ period.pk }}" 
                                    title="معلومات مفصلة">
                                <i class="fas fa-info"></i>
                            </button>
                        </div>
                        
                        <div>
                            {% if period.status == 'open' %}
                                <span class="badge bg-success">نشطة</span>
                            {% elif period.status == 'closed' %}
                                <span class="badge bg-warning">مغلقة</span>
                            {% else %}
                                <span class="badge bg-secondary">نهائية</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تفاصيل الفترة -->
        <div class="modal fade" id="periodModal{{ period.pk }}" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل الفترة: {{ period.name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>اسم الفترة:</strong>
                                <br>{{ period.name }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>نوع الفترة:</strong>
                                <br>{{ period.get_period_type_display }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>تاريخ البداية:</strong>
                                <br>{{ period.start_date|date:"Y/m/d" }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>تاريخ النهاية:</strong>
                                <br>{{ period.end_date|date:"Y/m/d" }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>الحالة:</strong>
                                <br>
                                <span class="badge bg-{% if period.status == 'open' %}success{% elif period.status == 'closed' %}warning{% else %}secondary{% endif %}">
                                    {{ period.get_status_display }}
                                </span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>تاريخ الإنشاء:</strong>
                                <br>{{ period.created_date|date:"Y/m/d H:i" }}
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="bg-light p-3 rounded">
                                    <h4 class="text-primary mb-0">{{ period.total_orders }}</h4>
                                    <small class="text-muted">إجمالي الطلبات</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="bg-light p-3 rounded">
                                    <h4 class="text-success mb-0">{{ period.total_revenue|floatformat:0 }}</h4>
                                    <small class="text-muted">الإيرادات (د.ع)</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="bg-light p-3 rounded">
                                    <h4 class="text-info mb-0">{{ period.total_commission|floatformat:0 }}</h4>
                                    <small class="text-muted">العمولات (د.ع)</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="bg-light p-3 rounded">
                                    <h4 class="text-warning mb-0">
                                        {% if period.total_revenue > 0 %}
                                            {% if period.total_revenue > 0 %}
                                                {% widthratio period.total_commission period.total_revenue 100 %}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </h4>
                                    <small class="text-muted">نسبة العمولة</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if period.notes %}
                        <hr>
                        <div>
                            <strong>الملاحظات:</strong>
                            <p class="mt-2">{{ period.notes }}</p>
                        </div>
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <a href="{% url 'accounting_period_detail' period.pk %}" class="btn btn-primary">
                            عرض التفاصيل الكاملة
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <!-- حالة عدم وجود فترات -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-calendar-plus"></i>
                        <h5>لا توجد فترات محاسبية</h5>
                        <p class="mb-4">ابدأ بإنشاء فترة محاسبية جديدة لحساب مستحقات المتاجر</p>
                        <a href="{% url 'accounting_period_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء فترة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- معلومات إضافية -->
{% if periods %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    حالات الفترات
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-success me-2">مفتوحة</span>
                        يمكن إضافة وتعديل الطلبات
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">مغلقة</span>
                        لا يمكن تعديل الطلبات
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-secondary me-2">نهائية</span>
                        مؤرشفة ولا يمكن تعديلها
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        احسب المستحقات بعد انتهاء الفترة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        راجع البيانات قبل إغلاق الفترة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم فترات منتظمة للمتابعة الدورية
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
