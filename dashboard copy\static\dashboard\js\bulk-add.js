/**
 * نظام الإضافة المتعددة الشامل
 * يدعم إضافة عدة عناصر في نفس الوقت لجميع الشاشات
 */

class BulkAddManager {
    constructor() {
        this.currentForm = null;
        this.fieldCount = 1;
        this.maxFields = 10;
        this.init();
    }

    init() {
        this.createBulkAddModal();
        this.attachEventListeners();
    }

    /**
     * إنشاء نافذة الإضافة المتعددة
     */
    createBulkAddModal() {
        const modalHTML = `
            <div class="modal fade" id="bulkAddModal" tabindex="-1" aria-labelledby="bulkAddModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="bulkAddModalLabel">
                                <i class="fas fa-plus-circle me-2"></i>
                                إضافة متعددة
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                يمكنك إضافة عدة عناصر في نفس الوقت. اضغط على "إضافة حقل جديد" لإضافة المزيد من الحقول.
                            </div>
                            <form id="bulkAddForm">
                                <div id="bulkFieldsContainer">
                                    <!-- سيتم إضافة الحقول هنا ديناميكياً -->
                                </div>
                                <div class="d-flex justify-content-between mt-3">
                                    <button type="button" class="btn btn-outline-primary" id="addFieldBtn">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة حقل جديد
                                    </button>
                                    <span class="text-muted small">
                                        <i class="fas fa-info-circle me-1"></i>
                                        الحد الأقصى: ${this.maxFields} عناصر
                                    </span>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-success" id="saveBulkBtn">
                                <i class="fas fa-save me-2"></i>
                                حفظ الكل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة النافذة إلى الصفحة إذا لم تكن موجودة
        if (!document.getElementById('bulkAddModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }
    }

    /**
     * ربط أحداث الأزرار
     */
    attachEventListeners() {
        // زر إضافة متعددة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('bulk-add-btn') || e.target.closest('.bulk-add-btn')) {
                const btn = e.target.classList.contains('bulk-add-btn') ? e.target : e.target.closest('.bulk-add-btn');
                this.openBulkAddModal(btn);
            }
        });

        // زر إضافة حقل جديد
        document.addEventListener('click', (e) => {
            if (e.target.id === 'addFieldBtn') {
                this.addNewField();
            }
        });

        // زر حفظ الكل
        document.addEventListener('click', (e) => {
            if (e.target.id === 'saveBulkBtn') {
                this.saveBulkData();
            }
        });

        // زر حذف حقل
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-field-btn')) {
                this.removeField(e.target);
            }
        });
    }

    /**
     * فتح نافذة الإضافة المتعددة
     */
    openBulkAddModal(button) {
        const entityType = button.dataset.entity;
        const fields = JSON.parse(button.dataset.fields);
        
        this.currentForm = {
            entity: entityType,
            fields: fields,
            url: button.dataset.url
        };

        // تحديث عنوان النافذة
        const modalTitle = document.getElementById('bulkAddModalLabel');
        modalTitle.innerHTML = `<i class="fas fa-plus-circle me-2"></i>إضافة ${this.getEntityDisplayName(entityType)} متعددة`;

        // إعادة تعيين العداد
        this.fieldCount = 1;

        // مسح الحقول السابقة
        const container = document.getElementById('bulkFieldsContainer');
        container.innerHTML = '';

        // إضافة أول حقل
        this.addNewField();

        // فتح النافذة
        const modal = new bootstrap.Modal(document.getElementById('bulkAddModal'));
        modal.show();
    }

    /**
     * إضافة حقل جديد
     */
    addNewField() {
        if (this.fieldCount >= this.maxFields) {
            this.showAlert('تم الوصول للحد الأقصى من الحقول', 'warning');
            return;
        }

        const container = document.getElementById('bulkFieldsContainer');
        const fieldHTML = this.generateFieldHTML(this.fieldCount);
        
        container.insertAdjacentHTML('beforeend', fieldHTML);
        this.fieldCount++;

        // تحديث حالة زر الإضافة
        const addBtn = document.getElementById('addFieldBtn');
        if (this.fieldCount >= this.maxFields) {
            addBtn.disabled = true;
            addBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>تم الوصول للحد الأقصى';
        }
    }

    /**
     * إنشاء HTML للحقل
     */
    generateFieldHTML(index) {
        let fieldsHTML = '';
        
        this.currentForm.fields.forEach(field => {
            fieldsHTML += this.generateSingleFieldHTML(field, index);
        });

        return `
            <div class="card mb-3 field-group" data-index="${index}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        ${this.getEntityDisplayName(this.currentForm.entity)} رقم ${index}
                    </h6>
                    ${index > 1 ? `
                        <button type="button" class="btn btn-sm btn-outline-danger remove-field-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
                <div class="card-body">
                    <div class="row">
                        ${fieldsHTML}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء HTML لحقل واحد
     */
    generateSingleFieldHTML(field, index) {
        const fieldId = `${field.name}_${index}`;
        const isRequired = field.required ? 'required' : '';
        const colClass = field.type === 'textarea' ? 'col-12' : 'col-md-6';

        let inputHTML = '';

        switch (field.type) {
            case 'text':
            case 'email':
            case 'tel':
            case 'number':
                inputHTML = `
                    <input type="${field.type}" 
                           class="form-control" 
                           id="${fieldId}" 
                           name="${field.name}_${index}" 
                           placeholder="${field.placeholder || ''}"
                           ${isRequired}>
                `;
                break;
            
            case 'textarea':
                inputHTML = `
                    <textarea class="form-control" 
                              id="${fieldId}" 
                              name="${field.name}_${index}" 
                              rows="3" 
                              placeholder="${field.placeholder || ''}"
                              ${isRequired}></textarea>
                `;
                break;
            
            case 'select':
                let optionsHTML = '<option value="">اختر...</option>';
                if (field.options) {
                    field.options.forEach(option => {
                        optionsHTML += `<option value="${option.value}">${option.label}</option>`;
                    });
                }
                inputHTML = `
                    <select class="form-select" 
                            id="${fieldId}" 
                            name="${field.name}_${index}" 
                            ${isRequired}>
                        ${optionsHTML}
                    </select>
                `;
                break;
            
            case 'file':
                inputHTML = `
                    <input type="file" 
                           class="form-control" 
                           id="${fieldId}" 
                           name="${field.name}_${index}" 
                           accept="${field.accept || ''}"
                           ${isRequired}>
                `;
                break;
        }

        return `
            <div class="${colClass} mb-3">
                <label for="${fieldId}" class="form-label">
                    ${field.label}
                    ${field.required ? '<span class="text-danger">*</span>' : ''}
                </label>
                ${inputHTML}
                ${field.help ? `<div class="form-text">${field.help}</div>` : ''}
            </div>
        `;
    }

    /**
     * حذف حقل
     */
    removeField(button) {
        const fieldGroup = button.closest('.field-group');
        fieldGroup.remove();
        this.fieldCount--;

        // إعادة تفعيل زر الإضافة
        const addBtn = document.getElementById('addFieldBtn');
        addBtn.disabled = false;
        addBtn.innerHTML = '<i class="fas fa-plus me-2"></i>إضافة حقل جديد';

        // إعادة ترقيم الحقول
        this.renumberFields();
    }

    /**
     * إعادة ترقيم الحقول
     */
    renumberFields() {
        const fieldGroups = document.querySelectorAll('.field-group');
        fieldGroups.forEach((group, index) => {
            const newIndex = index + 1;
            group.dataset.index = newIndex;
            
            // تحديث العنوان
            const title = group.querySelector('.card-header h6');
            title.innerHTML = `<i class="fas fa-edit me-2"></i>${this.getEntityDisplayName(this.currentForm.entity)} رقم ${newIndex}`;
            
            // تحديث أسماء الحقول
            const inputs = group.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                const baseName = input.name.split('_')[0];
                input.name = `${baseName}_${newIndex}`;
                input.id = `${baseName}_${newIndex}`;
            });
            
            // تحديث labels
            const labels = group.querySelectorAll('label');
            labels.forEach(label => {
                const baseName = label.getAttribute('for').split('_')[0];
                label.setAttribute('for', `${baseName}_${newIndex}`);
            });
        });
        
        this.fieldCount = fieldGroups.length + 1;
    }

    /**
     * حفظ البيانات المتعددة
     */
    async saveBulkData() {
        const formData = new FormData();
        const fieldGroups = document.querySelectorAll('.field-group');
        
        if (fieldGroups.length === 0) {
            this.showAlert('لا توجد بيانات للحفظ', 'warning');
            return;
        }

        // التحقق من صحة البيانات
        let isValid = true;
        const items = [];

        fieldGroups.forEach((group, index) => {
            const item = {};
            const inputs = group.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                const fieldName = input.name.split('_')[0];
                
                // التحقق من الحقول المطلوبة
                if (input.hasAttribute('required') && !input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                    item[fieldName] = input.value;
                }
            });
            
            if (Object.keys(item).length > 0) {
                items.push(item);
            }
        });

        if (!isValid) {
            this.showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
            return;
        }

        // إرسال البيانات
        try {
            const saveBtn = document.getElementById('saveBulkBtn');
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';

            const response = await fetch(this.currentForm.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    items: items,
                    bulk_add: true
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(`تم حفظ ${result.saved_count} عنصر بنجاح`, 'success');
                
                // إغلاق النافذة وإعادة تحميل الصفحة
                setTimeout(() => {
                    bootstrap.Modal.getInstance(document.getElementById('bulkAddModal')).hide();
                    location.reload();
                }, 1500);
            } else {
                this.showAlert(result.message || 'حدث خطأ أثناء الحفظ', 'danger');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showAlert('حدث خطأ في الاتصال', 'danger');
        } finally {
            const saveBtn = document.getElementById('saveBulkBtn');
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الكل';
        }
    }

    /**
     * عرض رسالة تنبيه
     */
    showAlert(message, type = 'info') {
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.modal-body');
        container.insertAdjacentHTML('afterbegin', alertHTML);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    /**
     * الحصول على أيقونة التنبيه
     */
    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * الحصول على اسم العرض للكيان
     */
    getEntityDisplayName(entity) {
        const names = {
            areas: 'منطقة',
            stores: 'متجر',
            categories: 'فئة',
            products: 'منتج',
            customers: 'عميل',
            companies: 'شركة',
            store_types: 'نوع متجر'
        };
        return names[entity] || 'عنصر';
    }

    /**
     * الحصول على CSRF Token
     */
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new BulkAddManager();
});
