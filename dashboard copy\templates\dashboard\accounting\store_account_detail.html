{% extends 'dashboard/base.html' %}

{% block title %}حساب {{ store_account.store.store_name }} - {{ store_account.period.name }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounting_periods_list' %}">الفترات المحاسبية</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounting_period_detail' store_account.period.pk %}">{{ store_account.period.name }}</a></li>
<li class="breadcrumb-item active">{{ store_account.store.store_name }}</li>
{% endblock %}

{% block extra_css %}
<style>
.account-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
}

.settlement-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.settled { background: rgba(40, 167, 69, 0.9); }
.unsettled { background: rgba(255, 193, 7, 0.9); }

.account-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
}

.stat-card.orders { border-left-color: #007bff; }
.stat-card.revenue { border-left-color: #28a745; }
.stat-card.commission { border-left-color: #ffc107; }
.stat-card.paid { border-left-color: #17a2b8; }
.stat-card.remaining { border-left-color: #dc3545; }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.progress-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.progress-bar-large {
    height: 20px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
    position: relative;
}

.progress-fill-large {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.5s ease;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.payment-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.payment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.payment-header {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.payment-method-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.method-cash { background: #d4edda; color: #155724; }
.method-bank { background: #cce5ff; color: #004085; }
.method-check { background: #fff3cd; color: #856404; }
.method-online { background: #e2e3e5; color: #383d41; }

.add-payment-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.add-payment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.commission-info {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.empty-payments {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الحساب -->
<div class="account-header">
    <span class="settlement-status {% if store_account.is_settled %}settled{% else %}unsettled{% endif %}">
        {% if store_account.is_settled %}مسدد بالكامل{% else %}غير مسدد{% endif %}
    </span>
    
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">
                <i class="fas fa-store me-2"></i>
                {{ store_account.store.store_name }}
            </h2>
            <p class="mb-1 opacity-75">
                <i class="fas fa-calendar me-2"></i>
                الفترة المحاسبية: {{ store_account.period.name }}
            </p>
            <small class="opacity-75">
                <i class="fas fa-tag me-1"></i>
                {{ store_account.store.store_type.type_name }}
            </small>
        </div>
        <div class="col-md-4 text-end">
            {% if not store_account.is_settled %}
            <a href="{% url 'add_payment' store_account.pk %}" class="add-payment-btn">
                <i class="fas fa-plus me-2"></i>
                إضافة دفعة
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<div class="account-stats">
    <div class="stat-card orders">
        <div class="stat-number text-primary">{{ store_account.orders_count }}</div>
        <div class="stat-label">عدد الطلبات</div>
    </div>
    <div class="stat-card revenue">
        <div class="stat-number text-success">{{ store_account.total_orders_value|floatformat:0 }}</div>
        <div class="stat-label">قيمة الطلبات (د.ع)</div>
    </div>
    <div class="stat-card commission">
        <div class="stat-number text-warning">{{ store_account.commission_amount|floatformat:0 }}</div>
        <div class="stat-label">إجمالي العمولة (د.ع)</div>
    </div>
    <div class="stat-card paid">
        <div class="stat-number text-info">{{ store_account.paid_amount|floatformat:0 }}</div>
        <div class="stat-label">المبلغ المدفوع (د.ع)</div>
    </div>
    <div class="stat-card remaining">
        <div class="stat-number text-danger">{{ store_account.remaining_amount|floatformat:0 }}</div>
        <div class="stat-label">المبلغ المتبقي (د.ع)</div>
    </div>
</div>

<!-- شريط التقدم -->
<div class="progress-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">
            <i class="fas fa-chart-line me-2"></i>
            حالة السداد
        </h6>
        <span class="badge bg-primary">
            {% if store_account.commission_amount > 0 %}
                {% widthratio store_account.paid_amount store_account.commission_amount 100 %}%
            {% else %}
                0%
            {% endif %}
        </span>
    </div>
    
    <div class="progress-bar-large">
        <div class="progress-fill-large" 
             style="width: {% if store_account.commission_amount > 0 %}{% widthratio store_account.paid_amount store_account.commission_amount 100 %}%{% else %}0%{% endif %}">
            <div class="progress-text">
                {{ store_account.paid_amount|floatformat:0 }} من {{ store_account.commission_amount|floatformat:0 }} د.ع
            </div>
        </div>
    </div>
    
    <div class="row mt-3 text-center">
        <div class="col-4">
            <small class="text-muted">المدفوع</small>
            <br>
            <strong class="text-success">{{ store_account.paid_amount|floatformat:0 }} د.ع</strong>
        </div>
        <div class="col-4">
            <small class="text-muted">المتبقي</small>
            <br>
            <strong class="text-danger">{{ store_account.remaining_amount|floatformat:0 }} د.ع</strong>
        </div>
        <div class="col-4">
            <small class="text-muted">الإجمالي</small>
            <br>
            <strong class="text-primary">{{ store_account.commission_amount|floatformat:0 }} د.ع</strong>
        </div>
    </div>
</div>

<!-- معلومات العمولة -->
{% if store_account.store.commission_settings %}
<div class="commission-info">
    <h6 class="mb-3">
        <i class="fas fa-cogs me-2"></i>
        إعدادات العمولة المطبقة
    </h6>
    <div class="row">
        <div class="col-md-6">
            <strong>نوع العمولة:</strong>
            <br>
            <span class="badge bg-{% if store_account.store.commission_settings.commission_type == 'fixed' %}primary{% elif store_account.store.commission_settings.commission_type == 'percentage' %}success{% else %}info{% endif %}">
                {{ store_account.store.commission_settings.get_commission_type_display }}
            </span>
        </div>
        <div class="col-md-6">
            <strong>القيمة:</strong>
            <br>
            {% if store_account.store.commission_settings.commission_type == 'fixed' %}
                {{ store_account.store.commission_settings.fixed_amount|floatformat:0 }} د.ع لكل طلب
            {% elif store_account.store.commission_settings.commission_type == 'percentage' %}
                {{ store_account.store.commission_settings.percentage_rate }}% من قيمة الطلب
            {% else %}
                نظام متدرج حسب عدد الطلبات
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- سجل الدفعات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        سجل الدفعات
                    </h5>
                    <span class="badge bg-success">{{ payments|length }} دفعة</span>
                </div>
            </div>
            <div class="card-body">
                {% if payments %}
                    {% for payment in payments %}
                    <div class="payment-card">
                        <div class="payment-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1">
                                        <i class="fas fa-money-bill-wave text-success me-2"></i>
                                        {{ payment.amount|floatformat:0 }} د.ع
                                    </h6>
                                    <small class="text-muted">{{ payment.payment_date|date:"Y/m/d H:i" }}</small>
                                </div>
                                <div class="col-md-6 text-end">
                                    <span class="payment-method-badge method-{{ payment.payment_method }}">
                                        {{ payment.get_payment_method_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="row">
                                {% if payment.reference_number %}
                                <div class="col-md-6 mb-2">
                                    <small class="text-muted">رقم المرجع:</small>
                                    <br>
                                    <strong>{{ payment.reference_number }}</strong>
                                </div>
                                {% endif %}
                                <div class="col-md-6 mb-2">
                                    <small class="text-muted">أنشأ بواسطة:</small>
                                    <br>
                                    <strong>{{ payment.created_by.username }}</strong>
                                </div>
                                {% if payment.notes %}
                                <div class="col-12 mt-2">
                                    <small class="text-muted">ملاحظات:</small>
                                    <p class="mb-0">{{ payment.notes }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-payments">
                        <i class="fas fa-receipt fa-4x mb-3"></i>
                        <h5>لا توجد دفعات</h5>
                        <p class="mb-4">لم يتم تسجيل أي دفعات لهذا الحساب بعد</p>
                        {% if not store_account.is_settled %}
                        <a href="{% url 'add_payment' store_account.pk %}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول دفعة
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
{% if store_account.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات الحساب
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ store_account.notes }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if not store_account.is_settled %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'add_payment' store_account.pk %}" 
                           class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-plus fa-2x mb-2"></i>
                            <br>
                            إضافة دفعة
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_period_detail' store_account.period.pk %}" 
                           class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-calendar fa-2x mb-2"></i>
                            <br>
                            تفاصيل الفترة
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_reports' %}?store={{ store_account.store.pk }}&period={{ store_account.period.pk }}" 
                           class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <br>
                            تقرير المتجر
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'commission_settings_edit' store_account.store.commission_settings.pk %}" 
                           class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <br>
                            إعدادات العمولة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
