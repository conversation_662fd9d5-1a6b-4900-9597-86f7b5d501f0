{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - المتاجر{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'stores_list' %}">المتاجر</a></li>
<li class="breadcrumb-item active">
    {% if store %}تعديل{% else %}إضافة جديد{% endif %}
</li>
{% endblock %}

{% block extra_css %}
<style>
.store-form-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #28a745;
}

.form-section h6 {
    color: #28a745;
    font-weight: 600;
    margin-bottom: 1rem;
}

.btn-submit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    margin-top: 1rem;
}

.help-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.required-field {
    color: #dc3545;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.coordinates-help {
    background: #fff3cd;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.5rem;
    border-left: 4px solid #ffc107;
}

.map-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.map-controls {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.search-container {
    position: relative;
}

.search-container .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-container .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.location-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    border: none;
}

.map-button {
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.map-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تخصيص أيقونة العلامة */
.custom-div-icon {
    background: transparent !important;
    border: none !important;
}

/* تحسين مظهر الخريطة */
.leaflet-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.leaflet-popup-content-wrapper {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-tip {
    background: white;
}

/* تحسين أزرار التحكم */
.leaflet-control-zoom a {
    border-radius: 6px;
    font-size: 16px;
}

/* تحسين مظهر النافذة المنبثقة */
.leaflet-popup-content {
    margin: 0;
    padding: 0;
}

/* تأثيرات تفاعلية للعلامة */
.custom-div-icon:hover {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="store-form-card">
            <!-- رأس النموذج -->
            <div class="form-header">
                <div class="mb-3">
                    <i class="fas fa-store fa-3x"></i>
                </div>
                <h3 class="mb-0">{{ title }}</h3>
                <p class="mb-0 mt-2 opacity-75">
                    {% if store %}
                        تعديل بيانات المتجر وإعداداته
                    {% else %}
                        إضافة متجر جديد إلى النظام
                    {% endif %}
                </p>
            </div>

            <!-- النموذج -->
            <div class="card-body p-4">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- معلومات الحساب -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-user-circle me-2"></i>
                            معلومات الحساب
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">الاسم الأول</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small">{{ form.first_name.errors }}</div>
                                {% endif %}
                                <div class="form-text">اختياري - لعرض اسم المسؤول</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">اسم العائلة</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small">{{ form.last_name.errors }}</div>
                                {% endif %}
                                <div class="form-text">اختياري - لعرض اسم المسؤول</div>
                            </div>
                        </div>

                        {% if not store %}
                        <div class="alert alert-success">
                            <i class="fas fa-magic me-2"></i>
                            <strong>توليد تلقائي:</strong> سيتم إنشاء اسم المستخدم وكلمة المرور ومعرف المتجر (id_store) تلقائياً عند حفظ المتجر.
                            <br>
                            <small class="mt-2 d-block">
                                • <strong>اسم المستخدم:</strong> سيتم توليده من اسم المتجر<br>
                                • <strong>كلمة المرور:</strong> ستكون قوية ومُولدة تلقائياً<br>
                                • <strong>معرف المتجر:</strong> فريد للتعامل مع Firebase<br>
                                • <strong>البريد الإلكتروني:</strong> سيتم إنشاؤه تلقائياً
                            </small>
                        </div>
                        {% endif %}
                    </div>

                    <!-- معلومات المتجر -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-store me-2"></i>
                            معلومات المتجر
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.store_name.id_for_label }}" class="form-label">
                                    اسم المتجر <span class="required-field">*</span>
                                </label>
                                {{ form.store_name }}
                                {% if form.store_name.errors %}
                                    <div class="text-danger small">{{ form.store_name.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.store_type.id_for_label }}" class="form-label">
                                    نوع المتجر <span class="required-field">*</span>
                                </label>
                                {{ form.store_type }}
                                {% if form.store_type.errors %}
                                    <div class="text-danger small">{{ form.store_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.area.id_for_label }}" class="form-label">
                                    المنطقة <span class="required-field">*</span>
                                </label>
                                {{ form.area }}
                                {% if form.area.errors %}
                                    <div class="text-danger small">{{ form.area.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.price_stor.id_for_label }}" class="form-label">إيجار المتجر (د.ع)</label>
                                {{ form.price_stor }}
                                {% if form.price_stor.errors %}
                                    <div class="text-danger small">{{ form.price_stor.errors }}</div>
                                {% endif %}
                                <div class="form-text">0 = مجاني</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">وصف المتجر</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.note.id_for_label }}" class="form-label">ملاحظات</label>
                            {{ form.note }}
                            {% if form.note.errors %}
                                <div class="text-danger small">{{ form.note.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- معلومات المسؤول -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-user-tie me-2"></i>
                            معلومات مسؤول المتجر
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.store_person_name.id_for_label }}" class="form-label">
                                    اسم المسؤول <span class="required-field">*</span>
                                </label>
                                {{ form.store_person_name }}
                                {% if form.store_person_name.errors %}
                                    <div class="text-danger small">{{ form.store_person_name.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.store_person_phone_number.id_for_label }}" class="form-label">
                                    رقم الهاتف <span class="required-field">*</span>
                                </label>
                                {{ form.store_person_phone_number }}
                                {% if form.store_person_phone_number.errors %}
                                    <div class="text-danger small">{{ form.store_person_phone_number.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- الموقع الجغرافي -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-map-marker-alt me-2"></i>
                            الموقع الجغرافي
                        </h6>

                        <!-- الخريطة التفاعلية -->
                        <div class="mb-4">
                            <label class="form-label">تحديد الموقع على الخريطة</label>
                            <div class="map-container">
                                <div id="map" style="height: 400px;"></div>
                            </div>
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle me-1"></i>
                                انقر على الخريطة لتحديد موقع المتجر، أو اسحب العلامة لتغيير الموقع
                            </div>
                        </div>

                        <!-- أزرار التحكم في الخريطة -->
                        <div class="map-controls mb-3">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary map-button" onclick="getCurrentLocation()">
                                    <i class="fas fa-crosshairs me-2"></i>
                                    موقعي الحالي
                                </button>
                                <button type="button" class="btn btn-outline-success map-button" onclick="searchLocation()">
                                    <i class="fas fa-search me-2"></i>
                                    البحث عن عنوان
                                </button>
                                <button type="button" class="btn btn-outline-info map-button" onclick="centerOnSanaa()">
                                    <i class="fas fa-city me-2"></i>
                                    وسط صنعاء
                                </button>
                            </div>
                        </div>

                        <!-- حقل البحث -->
                        <div class="search-container mb-3">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="ابحث عن عنوان أو منطقة...">
                                <button class="btn btn-success map-button" type="button" onclick="performSearch()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.lat.id_for_label }}" class="form-label">خط العرض (Latitude)</label>
                                {{ form.lat }}
                                {% if form.lat.errors %}
                                    <div class="text-danger small">{{ form.lat.errors }}</div>
                                {% endif %}
                                <div class="form-text">سيتم تحديثه تلقائياً عند اختيار الموقع على الخريطة</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.long.id_for_label }}" class="form-label">خط الطول (Longitude)</label>
                                {{ form.long }}
                                {% if form.long.errors %}
                                    <div class="text-danger small">{{ form.long.errors }}</div>
                                {% endif %}
                                <div class="form-text">سيتم تحديثه تلقائياً عند اختيار الموقع على الخريطة</div>
                            </div>
                        </div>

                        <!-- معلومات الموقع المحدد -->
                        <div id="locationInfo" class="location-info alert" style="display: none;">
                            <h6 class="mb-2">
                                <i class="fas fa-map-pin me-2"></i>
                                الموقع المحدد
                            </h6>
                            <div id="locationDetails"></div>
                        </div>
                    </div>

                    <!-- صورة المتجر -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-image me-2"></i>
                            صورة المتجر
                        </h6>
                        
                        <div class="mb-3">
                            <label for="{{ form.store_picture.id_for_label }}" class="form-label">صورة المتجر</label>
                            {{ form.store_picture }}
                            {% if form.store_picture.errors %}
                                <div class="text-danger small">{{ form.store_picture.errors }}</div>
                            {% endif %}
                            <div class="form-text">يُفضل صورة بحجم 400x300 بكسل</div>
                            
                            {% if store and store.store_picture %}
                                <img src="{{ store.store_picture.url }}" class="image-preview" alt="صورة المتجر الحالية">
                                <div class="form-text text-muted">الصورة الحالية</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الإعدادات -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-cogs me-2"></i>
                            الإعدادات
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.enable }}
                                    <label class="form-check-label" for="{{ form.enable.id_for_label }}">
                                        تفعيل المتجر
                                    </label>
                                </div>
                                <div class="form-text">المتاجر المعطلة لن تظهر للعملاء</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.notification }}
                                    <label class="form-check-label" for="{{ form.notification.id_for_label }}">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                                <div class="form-text">إرسال إشعارات الطلبات الجديدة</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_static_in_main_screen }}
                                    <label class="form-check-label" for="{{ form.is_static_in_main_screen.id_for_label }}">
                                        تثبيت في الشاشة الرئيسية
                                    </label>
                                </div>
                                <div class="form-text">سيظهر المتجر في أعلى القائمة</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_found_24_for_food_stor }}
                                    <label class="form-check-label" for="{{ form.is_found_24_for_food_stor.id_for_label }}">
                                        متوفر 24 ساعة
                                    </label>
                                </div>
                                <div class="form-text">المتجر يعمل على مدار الساعة</div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'stores_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-save me-2"></i>
                                {% if store %}تحديث المتجر{% else %}إضافة المتجر{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قسم المساعدة -->
        <div class="help-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>
                نصائح مهمة
            </h6>
            <div class="row">
                <div class="col-md-4">
                    <h6 class="text-primary">معلومات الحساب</h6>
                    <p class="small">تأكد من صحة اسم المستخدم والبريد الإلكتروني لأنها ستُستخدم لتسجيل الدخول.</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-success">الموقع الجغرافي</h6>
                    <p class="small">الإحداثيات الدقيقة مهمة لحساب رسوم التوصيل وإظهار المتجر على الخريطة.</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-info">الصورة</h6>
                    <p class="small">استخدم صورة واضحة وجذابة للمتجر لتحسين تجربة العملاء.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحميل OpenStreetMap مع Leaflet (بديل مجاني لـ Google Maps) -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.css" />

<script>
let map;
let marker;
let geocoder;

// إحداثيات صنعاء الافتراضية
const SANAA_LAT = 15.3694;
const SANAA_LNG = 44.1910;

// تهيئة الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initMap();
});

// تهيئة الخريطة
function initMap() {
    // الحصول على الإحداثيات الحالية من الحقول
    const currentLat = parseFloat(document.getElementById('{{ form.lat.id_for_label }}').value) || SANAA_LAT;
    const currentLng = parseFloat(document.getElementById('{{ form.long.id_for_label }}').value) || SANAA_LNG;

    // إنشاء الخريطة باستخدام Leaflet
    map = L.map('map').setView([currentLat, currentLng], 13);

    // إضافة طبقة الخريطة من OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);

    // إنشاء أيقونة مخصصة للعلامة
    const storeIcon = L.divIcon({
        html: '<i class="fas fa-map-marker-alt" style="color: #dc3545; font-size: 30px;"></i>',
        iconSize: [30, 30],
        iconAnchor: [15, 30],
        className: 'custom-div-icon'
    });

    // إنشاء العلامة
    marker = L.marker([currentLat, currentLng], {
        icon: storeIcon,
        draggable: true
    }).addTo(map);

    // إضافة نافذة منبثقة للعلامة
    marker.bindPopup('موقع المتجر<br>اسحب العلامة لتغيير الموقع').openPopup();

    // إنشاء خدمة Geocoding
    geocoder = L.Control.Geocoder.nominatim({
        geocodingQueryParams: {
            countrycodes: 'ye', // تقييد البحث على اليمن
            'accept-language': 'ar,en'
        }
    });

    // معالج تغيير موقع العلامة بالسحب
    marker.on('dragend', function(event) {
        const position = event.target.getLatLng();
        updateCoordinates(position.lat, position.lng);
        getAddressFromCoordinates(position.lat, position.lng);
    });

    // معالج النقر على الخريطة
    map.on('click', function(event) {
        const lat = event.latlng.lat;
        const lng = event.latlng.lng;
        moveMarker(lat, lng);
    });

    // تحديث معلومات الموقع إذا كانت الإحداثيات موجودة
    if (currentLat !== BAGHDAD_LAT || currentLng !== BAGHDAD_LNG) {
        getAddressFromCoordinates(currentLat, currentLng);
    }
}

// تحريك العلامة إلى موقع جديد
function moveMarker(lat, lng) {
    const newPosition = [lat, lng];
    marker.setLatLng(newPosition);
    updateCoordinates(lat, lng);
    getAddressFromCoordinates(lat, lng);

    // تحديث مركز الخريطة
    map.setView(newPosition, map.getZoom());

    // تحديث النافذة المنبثقة
    marker.bindPopup(`موقع المتجر<br>خط العرض: ${lat.toFixed(6)}<br>خط الطول: ${lng.toFixed(6)}`).openPopup();
}

// تحديث حقول الإحداثيات
function updateCoordinates(lat, lng) {
    document.getElementById('{{ form.lat.id_for_label }}').value = lat.toFixed(6);
    document.getElementById('{{ form.long.id_for_label }}').value = lng.toFixed(6);
}

// الحصول على العنوان من الإحداثيات
function getAddressFromCoordinates(lat, lng) {
    geocoder.reverse({ lat: lat, lng: lng }, map.getZoom(), function(results) {
        if (results && results.length > 0) {
            const address = results[0].name || results[0].display_name || 'عنوان غير محدد';
            const locationInfo = document.getElementById('locationInfo');
            const locationDetails = document.getElementById('locationDetails');

            locationDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>العنوان:</strong><br>
                        <span class="text-muted">${address}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الإحداثيات:</strong><br>
                        <span class="text-muted">${lat.toFixed(6)}, ${lng.toFixed(6)}</span>
                    </div>
                </div>
            `;

            locationInfo.style.display = 'block';
        } else {
            // إظهار الإحداثيات فقط إذا لم يتم العثور على عنوان
            const locationInfo = document.getElementById('locationInfo');
            const locationDetails = document.getElementById('locationDetails');

            locationDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-12">
                        <strong>الإحداثيات:</strong><br>
                        <span class="text-muted">${lat.toFixed(6)}, ${lng.toFixed(6)}</span>
                    </div>
                </div>
            `;

            locationInfo.style.display = 'block';
        }
    });
}

// الحصول على الموقع الحالي للمستخدم
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            moveMarker(lat, lng);
            map.setCenter({ lat: lat, lng: lng });
            map.setZoom(16);

            // إظهار رسالة نجاح
            showNotification('تم تحديد موقعك الحالي بنجاح!', 'success');
        }, function(error) {
            let errorMessage = 'لا يمكن الحصول على موقعك الحالي. ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'تم رفض الإذن للوصول للموقع.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'معلومات الموقع غير متوفرة.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'انتهت مهلة طلب الموقع.';
                    break;
            }
            showNotification(errorMessage, 'error');
        });
    } else {
        showNotification('المتصفح لا يدعم خدمة تحديد الموقع.', 'error');
    }
}

// البحث عن موقع
function searchLocation() {
    const searchInput = document.getElementById('searchInput');
    searchInput.focus();
    showNotification('اكتب اسم المنطقة أو العنوان في حقل البحث أعلاه', 'info');
}

// تنفيذ البحث
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();

    if (!query) {
        showNotification('يرجى إدخال عنوان أو منطقة للبحث', 'warning');
        return;
    }

    // إضافة "اليمن" للبحث لتحسين النتائج
    const searchQuery = query.includes('اليمن') ? query : query + ' اليمن';

    geocoder.geocode(searchQuery, function(results) {
        if (results && results.length > 0) {
            const result = results[0];
            const lat = result.center.lat;
            const lng = result.center.lng;

            moveMarker(lat, lng);
            map.setView([lat, lng], 16);

            showNotification('تم العثور على الموقع بنجاح!', 'success');

            // مسح حقل البحث
            searchInput.value = '';
        } else {
            showNotification('لم يتم العثور على الموقع. جرب كلمات بحث أخرى.', 'error');
        }
    });
}

// التوسيط على صنعاء
function centerOnSanaa() {
    map.setView([SANAA_LAT, SANAA_LNG], 11);
    moveMarker(SANAA_LAT, SANAA_LNG);
    showNotification('تم التوسيط على صنعاء', 'info');
}

// إظهار الإشعارات
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : type === 'info' ? 'info' : 'success'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : type === 'info' ? 'info-circle' : 'check-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// معالج الضغط على Enter في حقل البحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }
        });
    }
});

// تحديث الخريطة عند تغيير الإحداثيات يدوياً
function setupInputListeners() {
    const latInput = document.getElementById('{{ form.lat.id_for_label }}');
    const lngInput = document.getElementById('{{ form.long.id_for_label }}');

    function updateMapFromInputs() {
        const lat = parseFloat(latInput.value);
        const lng = parseFloat(lngInput.value);

        if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
            if (map && marker) {
                moveMarker(lat, lng);
                map.setView([lat, lng], map.getZoom());
            }
        }
    }

    latInput.addEventListener('blur', updateMapFromInputs);
    lngInput.addEventListener('blur', updateMapFromInputs);
}

// تشغيل إعداد المستمعين بعد تحميل الخريطة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(setupInputListeners, 1000);
});
</script>

{% endblock %}
