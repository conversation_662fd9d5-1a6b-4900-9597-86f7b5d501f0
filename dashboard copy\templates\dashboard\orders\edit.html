{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'orders_list' %}">الطلبات</a></li>
<li class="breadcrumb-item"><a href="{% url 'orders_detail' order.pk %}">طلب #{{ order.id }}</a></li>
<li class="breadcrumb-item active">تعديل</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-edit me-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted">تعديل معلومات وتفاصيل الطلب</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{% url 'orders_detail' order.pk %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للتفاصيل
            </a>
            <a href="{% url 'orders_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>
                قائمة الطلبات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات الطلب
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.custom.id_for_label }}" class="form-label">العميل</label>
                            {{ form.custom }}
                            {% if form.custom.errors %}
                                <div class="text-danger small">{{ form.custom.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.products.id_for_label }}" class="form-label">المنتج</label>
                            {{ form.products }}
                            {% if form.products.errors %}
                                <div class="text-danger small">{{ form.products.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.price.id_for_label }}" class="form-label">السعر</label>
                            {{ form.price }}
                            {% if form.price.errors %}
                                <div class="text-danger small">{{ form.price.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.count.id_for_label }}" class="form-label">الكمية</label>
                            {{ form.count }}
                            {% if form.count.errors %}
                                <div class="text-danger small">{{ form.count.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.all_price.id_for_label }}" class="form-label">السعر الإجمالي</label>
                            {{ form.all_price }}
                            {% if form.all_price.errors %}
                                <div class="text-danger small">{{ form.all_price.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.order_hawalah_number.id_for_label }}" class="form-label">رقم الحوالة</label>
                            {{ form.order_hawalah_number }}
                            {% if form.order_hawalah_number.errors %}
                                <div class="text-danger small">{{ form.order_hawalah_number.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.order_state.id_for_label }}" class="form-label">حالة الطلب</label>
                            {{ form.order_state }}
                            {% if form.order_state.errors %}
                                <div class="text-danger small">{{ form.order_state.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="{% url 'orders_detail' order.pk %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div>
                            <a href="{% url 'orders_delete' order.pk %}" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                حذف الطلب
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الطلب الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">رقم الطلب</label>
                    <p class="fw-bold">#{{ order.id }}</p>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">تاريخ الإنشاء</label>
                    <p class="fw-bold">{{ order.request_date|date:"Y/m/d H:i" }}</p>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">تاريخ الاستلام</label>
                    <p class="fw-bold">{{ order.get_date|date:"Y/m/d H:i" }}</p>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">الحالة الحالية</label>
                    <p class="fw-bold">
                        <span class="badge bg-primary">{{ order.get_order_state_display }}</span>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    حاسبة السعر
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted small">سيتم حساب السعر الإجمالي تلقائياً عند تغيير السعر أو الكمية</p>
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>نصيحة:</strong> تأكد من صحة السعر الإجمالي قبل الحفظ
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // حاسبة السعر التلقائية
    const priceInput = document.getElementById('{{ form.price.id_for_label }}');
    const countInput = document.getElementById('{{ form.count.id_for_label }}');
    const totalInput = document.getElementById('{{ form.all_price.id_for_label }}');
    
    function calculateTotal() {
        const price = parseFloat(priceInput.value) || 0;
        const count = parseInt(countInput.value) || 0;
        const total = price * count;
        totalInput.value = total;
    }
    
    if (priceInput && countInput && totalInput) {
        priceInput.addEventListener('input', calculateTotal);
        countInput.addEventListener('input', calculateTotal);
    }
});
</script>
{% endblock %}
