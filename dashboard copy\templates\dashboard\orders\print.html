<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الطلب #{{ order.id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .container { max-width: 100% !important; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
        }
        
        .print-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-logo {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .order-info-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .total-section {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            color: white;
        }
        
        .status-pending { background-color: #ff9800; }
        .status-waiting_shipping { background-color: #00bcd4; }
        .status-shipped { background-color: #3f51b5; }
        .status-on_way { background-color: #9c27b0; }
        .status-delivered { background-color: #4caf50; }
        .status-no_answer { background-color: #f44336; }
        .status-postponed { background-color: #607d8b; }
        .status-wrong_address { background-color: #f44336; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- رأس الطباعة -->
        <div class="print-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="company-logo">شركة زاد</div>
                    <p class="text-muted mb-0">نظام إدارة توصيل الطلبات</p>
                </div>
                <div class="col-md-6 text-end">
                    <h2 class="mb-0">فاتورة طلب</h2>
                    <p class="text-muted mb-0">رقم الطلب: #{{ order.id }}</p>
                    <p class="text-muted mb-0">تاريخ الطباعة: {{ print_date|date:"Y/m/d H:i" }}</p>
                </div>
            </div>
        </div>

        <!-- معلومات الطلب -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="mb-3">معلومات الطلب</h5>
                <table class="table table-bordered order-info-table">
                    <tr>
                        <th width="40%">رقم الطلب</th>
                        <td>#{{ order.id }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الطلب</th>
                        <td>{{ order.request_date|date:"Y/m/d H:i" }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الاستلام</th>
                        <td>{{ order.get_date|date:"Y/m/d H:i" }}</td>
                    </tr>
                    <tr>
                        <th>رقم الحوالة</th>
                        <td>{{ order.order_hawalah_number }}</td>
                    </tr>
                    <tr>
                        <th>حالة الطلب</th>
                        <td>
                            <span class="status-badge status-{{ order.order_state }}">
                                {{ order.get_order_state_display }}
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="col-md-6">
                <h5 class="mb-3">معلومات العميل</h5>
                {% if order.custom %}
                <table class="table table-bordered order-info-table">
                    <tr>
                        <th width="40%">اسم العميل</th>
                        <td>{{ order.custom.name }}</td>
                    </tr>
                    <tr>
                        <th>رقم الهاتف</th>
                        <td>{{ order.custom.phone_number }}</td>
                    </tr>
                    <tr>
                        <th>العنوان</th>
                        <td>{{ order.custom.place }}</td>
                    </tr>
                    {% if order.custom.notes %}
                    <tr>
                        <th>ملاحظات</th>
                        <td>{{ order.custom.notes }}</td>
                    </tr>
                    {% endif %}
                </table>
                {% else %}
                <div class="alert alert-warning">
                    لا توجد معلومات عميل
                </div>
                {% endif %}
            </div>
        </div>

        <!-- تفاصيل المنتج -->
        <div class="mb-4">
            <h5 class="mb-3">تفاصيل المنتج</h5>
            {% if order.products %}
            <table class="table table-bordered">
                <thead>
                    <tr class="table-primary">
                        <th>المنتج</th>
                        <th>الفئة</th>
                        <th>المتجر</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <strong>{{ order.products.product_name }}</strong>
                            {% if order.products.seo %}
                                <br><small class="text-muted">{{ order.products.seo|truncatechars:100 }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.products.categ %}
                                {{ order.products.categ.categ_name }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                        <td>
                            {% if order.products.store %}
                                {{ order.products.store.store_name }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                        <td>{{ order.price }} د.ع</td>
                        <td>{{ order.count }}</td>
                        <td><strong>{{ order.all_price }} ريال يمني</strong></td>
                    </tr>
                </tbody>
            </table>
            {% else %}
            <div class="alert alert-warning">
                لا يوجد منتج مرتبط بهذا الطلب
            </div>
            {% endif %}
        </div>

        <!-- المجموع الإجمالي -->
        <div class="row">
            <div class="col-md-6 ms-auto">
                <div class="total-section">
                    <div class="row">
                        <div class="col-6">
                            <strong>المجموع الإجمالي:</strong>
                        </div>
                        <div class="col-6 text-end">
                            <h4 class="mb-0 text-primary">{{ order.all_price }} ريال يمني</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تذييل الطباعة -->
        <div class="mt-5 pt-4 border-top">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted small mb-0">شكراً لاختياركم شركة زاد</p>
                    <p class="text-muted small mb-0">للاستفسارات: <EMAIL></p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted small mb-0">تم الطباعة بواسطة: {{ user.username }}</p>
                    <p class="text-muted small mb-0">{{ print_date|date:"Y/m/d H:i" }}</p>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="no-print mt-4 text-center">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
            <a href="{% url 'orders_detail' order.pk %}" class="btn btn-secondary ms-2">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للطلب
            </a>
            <button onclick="window.close()" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times me-2"></i>
                إغلاق
            </button>
        </div>
    </div>

    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
