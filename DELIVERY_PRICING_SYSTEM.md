# 🚚 نظام تسعير التوصيل حسب المسافة بالمتر

## 🎯 نظرة عامة

تم إنشاء نظام تسعير التوصيل الجديد الذي يعتمد على **المسافة بالمتر** بدلاً من النطاقات الدائرية. هذا النظام أكثر دقة ومرونة في تحديد تكلفة التوصيل.

## ✨ المميزات الرئيسية

### **🎯 تسعير دقيق حسب المسافة:**
- تحديد نطاقات مسافة بالمتر (مثل: 0-500م، 500-1000م، إلخ)
- حساب دقيق للمسافة باستخدام صيغة Haversine
- تسعير مرن لكل نطاق مسافة

### **🧮 حاسبة تكلفة تفاعلية:**
- خريطة تفاعلية لتحديد المواقع
- حساب فوري للمسافة والتكلفة
- عرض النتائج بشكل واضح ومفصل

### **⚙️ إدارة شاملة:**
- إضافة وتعديل وحذف نطاقات التسعير
- فلاتر بحث متقدمة
- واجهات سهلة الاستخدام

## 🗄️ نموذج البيانات

### **📋 جدول DeliveryPricing:**

```python
class DeliveryPricing(models.Model):
    # نطاق المسافة
    distance_from = models.IntegerField()      # بداية النطاق بالمتر
    distance_to = models.IntegerField()        # نهاية النطاق بالمتر
    
    # التسعير
    price_per_delivery = models.DecimalField() # السعر بالريال اليمني
    description = models.CharField()           # وصف النطاق
    
    # الحالة
    is_active = models.BooleanField()          # نشط/غير نشط
    
    # معلومات النظام
    created_at = models.DateTimeField()
    updated_at = models.DateTimeField()
    created_by = models.ForeignKey(User)
```

### **🔧 الوظائف الذكية:**

#### **📐 حساب المسافة:**
```python
@classmethod
def calculate_distance_meters(cls, lat1, lng1, lat2, lng2):
    """حساب المسافة بالمتر باستخدام صيغة Haversine"""
    # تحويل الدرجات إلى راديان
    # تطبيق صيغة Haversine
    # إرجاع المسافة بالمتر
```

#### **💰 تحديد السعر:**
```python
@classmethod
def get_price_for_distance(cls, distance_meters):
    """الحصول على السعر لمسافة معينة"""
    # البحث عن النطاق المناسب
    # إرجاع السعر أو None
```

#### **🧮 حساب التكلفة الكاملة:**
```python
@classmethod
def get_delivery_cost(cls, store_lat, store_lng, customer_lat, customer_lng):
    """حساب تكلفة التوصيل بين المتجر والعميل"""
    # حساب المسافة
    # تحديد السعر
    # إرجاع النتيجة الكاملة
```

## 🗺️ الواجهات والشاشات

### **📋 1. قائمة التسعير (`/delivery-pricing/`):**

```
┌─────────────────────────────────────────┐
│ 🔍 فلاتر البحث                         │
│ ├─ الحالة (نشط/غير نشط)               │
│ └─ البحث النصي                         │
├─────────────────────────────────────────┤
│ 📊 بطاقات النطاقات                     │
│ ├─ نطاق المسافة (كم ومتر)             │
│ ├─ السعر بالريال اليمني                │
│ ├─ الوصف والحالة                       │
│ └─ أزرار التعديل والحذف                │
└─────────────────────────────────────────┘
```

### **➕ 2. إضافة نطاق جديد (`/delivery-pricing/create/`):**

```
┌─────────────────────────────────────────┐
│ 📏 نطاق المسافة                        │
│ ├─ المسافة من (متر)                    │
│ ├─ المسافة إلى (متر)                   │
│ ├─ أزرار مسافات سريعة                  │
│ └─ محول المسافات (متر ↔ كم)            │
├─────────────────────────────────────────┤
│ 💰 سعر التوصيل                         │
│ ├─ السعر بالريال اليمني                │
│ └─ معاينة فورية للسعر                  │
├─────────────────────────────────────────┤
│ ⚙️ إعدادات إضافية                      │
│ ├─ وصف النطاق                          │
│ └─ حالة النشاط                          │
└─────────────────────────────────────────┘
```

### **🧮 3. حاسبة التكلفة (`/delivery-pricing/calculator/`):**

```
┌─────────────────┬───────────────────────┐
│ 📝 نموذج الحساب │ 🗺️ خريطة تفاعلية     │
│ ├─ اختيار المتجر │ ├─ عرض موقع المتجر    │
│ ├─ موقع العميل  │ ├─ تحديد موقع العميل  │
│ └─ زر الحساب    │ └─ علامات ملونة       │
├─────────────────┴───────────────────────┤
│ 📊 نتيجة الحساب                        │
│ ├─ المتجر المختار                      │
│ ├─ المسافة (كم ومتر)                   │
│ ├─ تكلفة التوصيل                       │
│ └─ حالة التوفر                          │
└─────────────────────────────────────────┘
```

## 🎨 التصميم والألوان

### **🌈 نظام الألوان:**
- **🔵 أزرق:** نطاقات المسافة
- **🟢 أخضر:** الأسعار والتكاليف
- **🟡 أصفر:** أزرار التعديل
- **🔴 أحمر:** أزرار الحذف والتحذيرات
- **⚫ رمادي:** المعلومات الثانوية

### **📱 التجاوب:**
- **💻 الحاسوب:** تخطيط شبكي مع بطاقات
- **📱 الجوال:** تخطيط عمودي متكيف
- **📟 التابلت:** تخطيط مرن حسب الحجم

## 🔗 الروابط والتنقل

### **📍 الروابط الأساسية:**
```
/delivery-pricing/                    # قائمة التسعير
/delivery-pricing/create/             # إضافة نطاق جديد
/delivery-pricing/<id>/edit/          # تعديل نطاق
/delivery-pricing/<id>/delete/        # حذف نطاق
/delivery-pricing/calculator/         # حاسبة التكلفة
/api/delivery-pricing/calculate/      # API حساب التكلفة
```

### **🎛️ القائمة الجانبية:**
```html
<a href="{% url 'delivery_pricing_list' %}" class="top-nav-link">
    <i class="fas fa-calculator"></i>
    <span>تسعير التوصيل</span>
</a>
```

## 🧮 كيفية عمل النظام

### **📐 1. حساب المسافة:**
```javascript
// صيغة Haversine للحساب الدقيق
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371000; // نصف قطر الأرض بالمتر
    
    // تحويل إلى راديان
    const lat1_rad = Math.radians(lat1);
    const lat2_rad = Math.radians(lat2);
    const delta_lat = Math.radians(lat2 - lat1);
    const delta_lng = Math.radians(lng2 - lng1);
    
    // صيغة Haversine
    const a = Math.sin(delta_lat / 2) ** 2 + 
              Math.cos(lat1_rad) * Math.cos(lat2_rad) * 
              Math.sin(delta_lng / 2) ** 2;
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance); // بالمتر
}
```

### **💰 2. تحديد السعر:**
```python
def get_price_for_distance(distance_meters):
    # البحث عن النطاق المناسب
    pricing = DeliveryPricing.objects.filter(
        distance_from__lte=distance_meters,
        distance_to__gte=distance_meters,
        is_active=True
    ).first()
    
    return pricing.price_per_delivery if pricing else None
```

### **🔄 3. سير العمل:**
```
1. تحديد موقع المتجر (من قاعدة البيانات)
2. تحديد موقع العميل (من الخريطة أو الإحداثيات)
3. حساب المسافة بالمتر باستخدام Haversine
4. البحث عن النطاق المناسب في قاعدة البيانات
5. إرجاع السعر أو رسالة "غير متاح"
```

## 📊 أمثلة على النطاقات

### **🏙️ نطاقات مقترحة للمدن:**
```
┌─────────────────┬─────────────────┬─────────────────┐
│ النطاق          │ المسافة         │ السعر المقترح   │
├─────────────────┼─────────────────┼─────────────────┤
│ قريب جداً       │ 0 - 500 متر     │ 200 ريال        │
│ قريب           │ 500 - 1000 متر   │ 300 ريال        │
│ متوسط          │ 1 - 2 كم        │ 500 ريال        │
│ بعيد           │ 2 - 5 كم        │ 800 ريال        │
│ بعيد جداً       │ 5 - 10 كم       │ 1200 ريال       │
│ خارج المدينة    │ أكثر من 10 كم   │ 2000 ريال       │
└─────────────────┴─────────────────┴─────────────────┘
```

## 🔧 الإعدادات والتخصيص

### **⚙️ إعدادات النطاقات:**
- **التداخل:** النظام يمنع تداخل النطاقات
- **التحقق:** التأكد من أن "المسافة إلى" أكبر من "المسافة من"
- **الحالة:** إمكانية تفعيل/إلغاء النطاقات
- **الوصف:** إضافة وصف توضيحي لكل نطاق

### **🎯 مسافات سريعة:**
```javascript
// أزرار للمسافات الشائعة
const quickDistances = [
    { from: 0, to: 500, label: "0-500م" },
    { from: 500, to: 1000, label: "500م-1كم" },
    { from: 1000, to: 2000, label: "1-2كم" },
    { from: 2000, to: 5000, label: "2-5كم" },
    { from: 5000, to: 10000, label: "5-10كم" },
    { from: 10000, to: 999999, label: "أكثر من 10كم" }
];
```

## 🚀 API للتطبيقات الخارجية

### **📡 نقطة النهاية:**
```
POST /api/delivery-pricing/calculate/
```

### **📥 البيانات المطلوبة:**
```json
{
    "store_id": 1,
    "customer_lat": 15.3694,
    "customer_lng": 44.1910
}
```

### **📤 الاستجابة:**
```json
{
    "success": true,
    "distance_meters": 1250,
    "distance_km": 1.25,
    "price": 500.00,
    "store_name": "متجر الأمل"
}
```

## 🎯 الفوائد والمميزات

### **✅ مقارنة مع النظام السابق:**

#### **❌ النظام القديم (النطاقات الدائرية):**
- معقد في الإعداد (تحديد مراكز ونصف أقطار)
- يتطلب خرائط تفاعلية لكل متجر
- صعوبة في إدارة التداخلات
- غير دقيق للمسافات الطويلة

#### **✅ النظام الجديد (المسافة بالمتر):**
- بسيط في الإعداد (نطاقات مسافة فقط)
- نظام موحد لجميع المتاجر
- دقة عالية في الحساب
- سهولة في الإدارة والصيانة
- مرونة في التسعير

### **🎯 المميزات الإضافية:**
- **🧮 حاسبة تفاعلية** لاختبار التكلفة
- **📊 واجهات جميلة** ومتجاوبة
- **🔍 فلاتر بحث** متقدمة
- **⚙️ إدارة سهلة** للنطاقات
- **📱 تجاوب ممتاز** مع جميع الأجهزة
- **🔗 API جاهز** للتطبيقات الخارجية

## 🎉 الخلاصة

### **✅ تم إنجاز:**
- ✅ **حذف النظام القديم** بالكامل
- ✅ **إنشاء نظام جديد** يعتمد على المسافة بالمتر
- ✅ **واجهات شاملة** للإدارة والاستخدام
- ✅ **حاسبة تفاعلية** مع خريطة
- ✅ **API متكامل** للتطبيقات الخارجية
- ✅ **تصميم احترافي** ومتجاوب
- ✅ **نظام دقيق** وسهل الاستخدام

### **🚚 النتيجة النهائية:**
**نظام تسعير التوصيل الجديد جاهز ويعمل بكفاءة عالية!**

### **🎯 الآن يمكنك:**
- 📏 تحديد نطاقات المسافة بالمتر بدقة
- 💰 تحديد أسعار مختلفة لكل نطاق
- 🧮 حساب تكلفة التوصيل فورياً
- 🗺️ استخدام الحاسبة التفاعلية مع الخريطة
- ⚙️ إدارة النطاقات بسهولة
- 📊 مراقبة وتتبع جميع النطاقات
- 🔗 دمج النظام مع التطبيقات الأخرى

**النظام الجديد أكثر دقة ومرونة وسهولة في الاستخدام!** 🚚✨
