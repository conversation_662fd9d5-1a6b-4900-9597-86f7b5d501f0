{% extends 'dashboard/base.html' %}

{% block title %}تفاصيل المتجر - {{ store.store_name }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block extra_css %}
<style>
/* تحسين مظهر صفحة تفاصيل المتجر */
.store-detail-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.store-info-card {
    border: none;
    box-shadow: var(--shadow-sm);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
}

.store-info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stats-card {
    border: none;
    box-shadow: var(--shadow-sm);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stats-card .card-body {
    position: relative;
    padding: var(--spacing-xl);
}

.stats-card i {
    opacity: 0.8;
}

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--glass-border);
}

.product-placeholder {
    width: 50px;
    height: 50px;
    background: var(--bg-light);
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-custom {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
}

.btn-action {
    width: 35px;
    height: 35px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.btn-action:hover {
    transform: scale(1.1);
}

.empty-state {
    padding: var(--spacing-xxl);
    text-align: center;
}

.empty-state i {
    opacity: 0.3;
    margin-bottom: var(--spacing-lg);
}

/* تحسين الجدول */
.table-hover tbody tr:hover {
    background-color: var(--bg-light);
    transform: scale(1.01);
    transition: var(--transition-fast);
}

.table th {
    background: var(--bg-light);
    border: none;
    font-weight: 600;
    color: var(--text-dark);
    padding: var(--spacing-md) var(--spacing-lg);
}

.table td {
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    vertical-align: middle;
}

/* تحسين البطاقات الإحصائية */
.stats-card.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
.stats-card.info { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
.stats-card.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
.stats-card.warning { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; }

.stats-card h4 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0;
}

.stats-card p {
    opacity: 0.9;
    margin-bottom: 0;
}

/* تخصيص خريطة المتجر */
.custom-store-icon {
    background: transparent !important;
    border: none !important;
}

.leaflet-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 12px;
}

.leaflet-popup-content-wrapper {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: none;
}

.leaflet-popup-tip {
    background: white;
}

.leaflet-popup-content {
    margin: 0;
    padding: 0;
}

.custom-popup .leaflet-popup-content-wrapper {
    background: white;
    color: #333;
}

.coverage-tooltip {
    background: rgba(40, 167, 69, 0.9);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    padding: 4px 8px;
}

/* تحسين أزرار التحكم */
.leaflet-control-zoom a {
    border-radius: 8px;
    font-size: 16px;
    background: white;
    color: #333;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.leaflet-control-zoom a:hover {
    background: #f8f9fa;
    border-color: #28a745;
    color: #28a745;
}
</style>
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'stores_list' %}">المتاجر</a></li>
<li class="breadcrumb-item active">{{ store.store_name }}</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-store me-2"></i>
            {{ store.store_name }}
        </h1>
        <p class="text-muted">تفاصيل شاملة عن المتجر ومنتجاته</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{% url 'stores_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
            <a href="{% url 'stores_edit' store.pk %}" class="btn btn-success">
                <i class="fas fa-edit me-2"></i>
                تعديل المتجر
            </a>
            <a href="{% url 'stores_delete' store.pk %}" class="btn btn-danger">
                <i class="fas fa-trash me-2"></i>
                حذف المتجر
            </a>
        </div>
    </div>
</div>

<!-- بيانات الحساب المُولدة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card" style="border: 2px solid #28a745; border-radius: 16px; background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);">
            <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 14px 14px 0 0;">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    بيانات الحساب المُولدة تلقائياً
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label text-muted">
                            <i class="fas fa-fingerprint me-1"></i>
                            معرف المتجر (Firebase)
                        </label>
                        <div class="d-flex align-items-center">
                            <code class="bg-light p-2 rounded flex-grow-1" style="font-size: 0.9rem;">{{ store.id_store }}</code>
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('{{ store.id_store }}', 'معرف المتجر')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label class="form-label text-muted">
                            <i class="fas fa-user me-1"></i>
                            اسم المستخدم
                        </label>
                        <div class="d-flex align-items-center">
                            <code class="bg-light p-2 rounded flex-grow-1" style="font-size: 0.9rem;">{{ store.generated_username|default:store.username }}</code>
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('{{ store.generated_username|default:store.username }}', 'اسم المستخدم')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label class="form-label text-muted">
                            <i class="fas fa-lock me-1"></i>
                            كلمة المرور
                        </label>
                        <div class="d-flex align-items-center">
                            <code class="bg-light p-2 rounded flex-grow-1" style="font-size: 0.9rem;" id="password-{{ store.pk }}">
                                {% if store.generated_password %}
                                    ••••••••••••
                                {% else %}
                                    غير متوفرة
                                {% endif %}
                            </code>
                            {% if store.generated_password %}
                            <button class="btn btn-sm btn-outline-info ms-1" onclick="togglePassword('{{ store.pk }}', '{{ store.generated_password }}')">
                                <i class="fas fa-eye" id="eye-{{ store.pk }}"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary ms-1" onclick="copyToClipboard('{{ store.generated_password }}', 'كلمة المرور')">
                                <i class="fas fa-copy"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label class="form-label text-muted">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </label>
                        <div class="d-flex align-items-center">
                            <code class="bg-light p-2 rounded flex-grow-1" style="font-size: 0.9rem;">{{ store.email }}</code>
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('{{ store.email }}', 'البريد الإلكتروني')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            تاريخ إنشاء الحساب: {{ store.account_created_date|date:"Y/m/d H:i" }}
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-sm btn-warning" onclick="regeneratePassword({{ store.pk }})">
                            <i class="fas fa-sync-alt me-1"></i>
                            إعادة توليد كلمة المرور
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات المتجر الأساسية -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card store-info-card">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0 text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المتجر
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">اسم المتجر</label>
                        <p class="fw-bold">{{ store.store_name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">نوع المتجر</label>
                        <p class="fw-bold">{{ store.store_type.store_type }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المنطقة</label>
                        <p class="fw-bold">{{ store.area.area_name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">نوع المتجر</label>
                        <p class="fw-bold">
                            <span class="badge bg-info">{{ store.store_type.store_type }}</span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المنطقة</label>
                        <p class="fw-bold">
                            <span class="badge bg-warning">{{ store.area.area_name }}</span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الحالة</label>
                        <p class="fw-bold">
                            {% if store.enable %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                    {% if store.description %}
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">الوصف</label>
                        <p class="fw-bold">{{ store.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card store-info-card">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0 text-success">
                    <i class="fas fa-user me-2"></i>
                    معلومات الاتصال
                </h5>
            </div>
            <div class="card-body">
                {% if store.store_person_name %}
                <div class="mb-3">
                    <label class="form-label text-muted">اسم المسؤول</label>
                    <p class="fw-bold">{{ store.store_person_name }}</p>
                </div>
                {% endif %}
                {% if store.store_person_phone_number %}
                <div class="mb-3">
                    <label class="form-label text-muted">رقم الهاتف</label>
                    <p class="fw-bold">
                        <a href="tel:{{ store.store_person_phone_number }}" class="text-decoration-none">
                            <i class="fas fa-phone me-1"></i>
                            {{ store.store_person_phone_number }}
                        </a>
                    </p>
                </div>
                {% endif %}
                {% if store.lat and store.long %}
                <div class="mb-3">
                    <label class="form-label text-muted">الموقع الجغرافي</label>
                    <p class="fw-bold">
                        <a href="https://maps.google.com/?q={{ store.lat }},{{ store.long }}" target="_blank" class="text-decoration-none">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            عرض على خرائط جوجل
                        </a>
                    </p>
                    <small class="text-muted">
                        {{ store.lat|floatformat:6 }}, {{ store.long|floatformat:6 }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- خريطة موقع المتجر -->
{% if store.lat and store.long %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card store-info-card">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0 text-primary">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    موقع المتجر على الخريطة
                </h5>
            </div>
            <div class="card-body">
                <div id="storeMap" style="height: 400px; border-radius: 12px; border: 2px solid #e9ecef;"></div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-map-marker-alt text-danger me-2"></i>
                            <div>
                                <strong>{{ store.store_name }}</strong>
                                <br>
                                <small class="text-muted">{{ store.area.area_name }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group">
                            <a href="https://maps.google.com/?q={{ store.lat }},{{ store.long }}"
                               target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>
                                فتح في خرائط جوجل
                            </a>
                            <a href="https://maps.google.com/dir/?api=1&destination={{ store.lat }},{{ store.long }}"
                               target="_blank" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-route me-1"></i>
                                الحصول على الاتجاهات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- إحصائيات المتجر -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card primary text-center">
            <div class="card-body">
                <i class="fas fa-box fa-2x mb-3"></i>
                <h4>{{ products.count }}</h4>
                <p>إجمالي المنتجات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card info text-center">
            <div class="card-body">
                <i class="fas fa-eye fa-2x mb-3"></i>
                <h4>{{ products.count }}</h4>
                <p>المنتجات النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card success text-center">
            <div class="card-body">
                <i class="fas fa-tags fa-2x mb-3"></i>
                <h4>5</h4>
                <p>الفئات المختلفة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card warning text-center">
            <div class="card-body">
                <i class="fas fa-star fa-2x mb-3"></i>
                <h4>4.5</h4>
                <p>التقييم العام</p>
            </div>
        </div>
    </div>
</div>

<!-- منتجات المتجر -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-boxes me-2"></i>
            منتجات المتجر ({{ products.count }})
        </h5>
        <a href="{% url 'products_create' %}" class="btn btn-primary btn-sm">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج جديد
        </a>
    </div>
    <div class="card-body">
        {% if products %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الفئة</th>
                            <th>السعر</th>
                            <th>المشاهدات</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if product.img %}
                                        <img src="{{ product.img.url }}" alt="{{ product.product_name }}"
                                             class="product-image me-3">
                                    {% else %}
                                        <div class="product-placeholder me-3">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ product.product_name|truncatechars:30 }}</strong>
                                        {% if product.seo %}
                                            <br><small class="text-muted">{{ product.seo|truncatechars:40 }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if product.categ %}
                                    <span class="badge bg-secondary">{{ product.categ.categ_name }}</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-success">{{ product.price }} د.ع</strong>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ product.views|default:0 }}</span>
                            </td>
                            <td>
                                {% if product.enable %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ product.datetimes|date:"Y/m/d" }}</small>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <a href="{% url 'products_detail' product.pk %}" class="btn btn-outline-primary btn-action" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'products_edit' product.pk %}" class="btn btn-outline-warning btn-action" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'products_delete' product.pk %}" class="btn btn-outline-danger btn-action" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-box fa-5x"></i>
                <h4 class="text-muted mb-3">لا توجد منتجات في هذا المتجر</h4>
                <p class="text-muted mb-4">يمكنك إضافة منتجات جديدة للمتجر لبدء البيع</p>
                <a href="{% url 'products_create' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول منتج
                </a>
            </div>
        {% endif %}
    </div>
</div>

{% if store.lat and store.long %}
<!-- تحميل OpenStreetMap مع Leaflet لعرض موقع المتجر -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initStoreMap();
});

function initStoreMap() {
    // إحداثيات المتجر
    const storeLocation = [{{ store.lat }}, {{ store.long }}];

    // إنشاء الخريطة باستخدام Leaflet
    const map = L.map('storeMap').setView(storeLocation, 16);

    // إضافة طبقة الخريطة من OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);

    // إنشاء أيقونة مخصصة للمتجر
    const storeIcon = L.divIcon({
        html: `
            <div style="background: #dc3545; border-radius: 50% 50% 50% 0; width: 30px; height: 30px; position: relative; transform: rotate(-45deg); border: 3px solid white; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                <i class="fas fa-store" style="color: white; font-size: 14px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(45deg);"></i>
            </div>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 30],
        className: 'custom-store-icon'
    });

    // إنشاء العلامة
    const marker = L.marker(storeLocation, {
        icon: storeIcon,
        title: '{{ store.store_name }}'
    }).addTo(map);

    // إنشاء محتوى النافذة المنبثقة
    const popupContent = `
        <div style="padding: 15px; max-width: 300px; font-family: Arial, sans-serif;">
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                {% if store.store_picture %}
                <img src="{{ store.store_picture.url }}"
                     style="width: 60px; height: 60px; border-radius: 12px; object-fit: cover; margin-left: 15px; border: 2px solid #e9ecef;"
                     alt="{{ store.store_name }}">
                {% else %}
                <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px; border: 2px solid #e9ecef;">
                    <i class="fas fa-store" style="color: #6c757d; font-size: 20px;"></i>
                </div>
                {% endif %}
                <div>
                    <h6 style="margin: 0; color: #333; font-size: 16px; font-weight: 600;">{{ store.store_name }}</h6>
                    <small style="color: #666; background: #e3f2fd; padding: 2px 8px; border-radius: 12px; font-size: 11px;">{{ store.store_type.store_type }}</small>
                </div>
            </div>

            <div style="margin-bottom: 10px;">
                <i class="fas fa-map-marker-alt" style="color: #dc3545; margin-left: 8px; width: 16px;"></i>
                <span style="color: #666; font-size: 14px;">{{ store.area.area_name }}</span>
            </div>

            {% if store.store_person_name %}
            <div style="margin-bottom: 10px;">
                <i class="fas fa-user" style="color: #28a745; margin-left: 8px; width: 16px;"></i>
                <span style="color: #666; font-size: 14px;">{{ store.store_person_name }}</span>
            </div>
            {% endif %}

            {% if store.store_person_phone_number %}
            <div style="margin-bottom: 10px;">
                <i class="fas fa-phone" style="color: #007bff; margin-left: 8px; width: 16px;"></i>
                <a href="tel:{{ store.store_person_phone_number }}" style="color: #007bff; text-decoration: none; font-size: 14px;">
                    {{ store.store_person_phone_number }}
                </a>
            </div>
            {% endif %}

            {% if store.is_found_24_for_food_stor %}
            <div style="margin-bottom: 15px;">
                <span style="background: linear-gradient(135deg, #17a2b8, #138496); color: white; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 500;">
                    🕐 متوفر 24 ساعة
                </span>
            </div>
            {% endif %}

            <div style="margin-top: 15px; text-align: center;">
                <a href="https://www.google.com/maps/dir/?api=1&destination={{ store.lat }},{{ store.long }}"
                   target="_blank"
                   style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 10px 20px; border-radius: 8px; text-decoration: none; font-size: 14px; font-weight: 500; display: inline-block; transition: all 0.3s ease;">
                    <i class="fas fa-route" style="margin-left: 8px;"></i>
                    الحصول على الاتجاهات
                </a>
            </div>
        </div>
    `;

    // ربط النافذة المنبثقة بالعلامة
    marker.bindPopup(popupContent, {
        maxWidth: 350,
        className: 'custom-popup'
    });

    // إظهار النافذة المنبثقة تلقائياً
    setTimeout(() => {
        marker.openPopup();
    }, 1000);

    // إضافة دائرة لإظهار منطقة التغطية
    const coverageCircle = L.circle(storeLocation, {
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.1,
        radius: 2000, // 2 كيلومتر
        weight: 2,
        opacity: 0.8
    }).addTo(map);

    // إضافة تسمية للدائرة
    coverageCircle.bindTooltip('منطقة التغطية (2 كم)', {
        permanent: false,
        direction: 'center',
        className: 'coverage-tooltip'
    });

    // تأثير تفاعلي للعلامة
    marker.on('mouseover', function() {
        this.getElement().style.transform = 'scale(1.2)';
        this.getElement().style.transition = 'transform 0.3s ease';
    });

    marker.on('mouseout', function() {
        this.getElement().style.transform = 'scale(1)';
    });
}
</script>
{% endif %}

<script>
// نسخ النص إلى الحافظة
function copyToClipboard(text, label) {
    navigator.clipboard.writeText(text).then(function() {
        // إظهار إشعار نجاح
        showNotification(`تم نسخ ${label} بنجاح!`, 'success');
    }).catch(function(err) {
        // في حالة فشل النسخ، استخدم طريقة بديلة
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification(`تم نسخ ${label} بنجاح!`, 'success');
    });
}

// إظهار/إخفاء كلمة المرور
function togglePassword(storeId, password) {
    const passwordElement = document.getElementById(`password-${storeId}`);
    const eyeElement = document.getElementById(`eye-${storeId}`);

    if (passwordElement.textContent.includes('••••')) {
        // إظهار كلمة المرور
        passwordElement.textContent = password;
        eyeElement.className = 'fas fa-eye-slash';
    } else {
        // إخفاء كلمة المرور
        passwordElement.textContent = '••••••••••••';
        eyeElement.className = 'fas fa-eye';
    }
}

// إعادة توليد كلمة المرور
function regeneratePassword(storeId) {
    if (confirm('هل أنت متأكد من إعادة توليد كلمة مرور جديدة؟\nسيتم إلغاء كلمة المرور الحالية.')) {
        // إرسال طلب AJAX لإعادة توليد كلمة المرور
        fetch(`/stores/${storeId}/regenerate-password/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث كلمة المرور في الصفحة
                const passwordElement = document.getElementById(`password-${storeId}`);
                passwordElement.textContent = '••••••••••••';
                passwordElement.setAttribute('data-password', data.new_password);

                // تحديث زر التبديل
                const toggleBtn = passwordElement.parentElement.querySelector('.btn-outline-info');
                toggleBtn.setAttribute('onclick', `togglePassword('${storeId}', '${data.new_password}')`);

                // تحديث زر النسخ
                const copyBtn = passwordElement.parentElement.querySelector('.btn-outline-primary');
                copyBtn.setAttribute('onclick', `copyToClipboard('${data.new_password}', 'كلمة المرور')`);

                showNotification('تم إعادة توليد كلمة مرور جديدة بنجاح!', 'success');
            } else {
                showNotification('حدث خطأ في إعادة توليد كلمة المرور', 'error');
            }
        })
        .catch(error => {
            showNotification('حدث خطأ في الاتصال', 'error');
        });
    }
}

// إظهار الإشعارات
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// إضافة CSRF token للطلبات
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء CSRF token مخفي إذا لم يكن موجوداً
    if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        document.body.appendChild(csrfToken);
    }
});
</script>

{% endblock %}
