from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.views import LoginView
from django.contrib import messages
from django.db.models import Q, Count, Sum
from django.db import models
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from django.urls import reverse_lazy
from django.utils import timezone
from .models import *
from .forms import *

# Create your views here.

class CustomLoginView(LoginView):
    template_name = 'registration/login.html'
    redirect_authenticated_user = True
    success_url = reverse_lazy('dashboard_home')

    def get_success_url(self):
        return reverse_lazy('dashboard_home')

    def form_valid(self, form):
        messages.success(self.request, f'مرحباً بك {form.get_user().username}!')
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, 'خطأ في اسم المستخدم أو كلمة المرور')
        return super().form_invalid(form)

def custom_logout(request):
    logout(request)
    messages.info(request, 'تم تسجيل الخروج بنجاح')
    return redirect('login')

@login_required
def dashboard_home(request):
    """الصفحة الرئيسية للوحة التحكم"""

    # إحصائيات حالات الطلبات
    orders_stats = {}
    for status_code, status_name in Order.ORDER_STATUS_CHOICES:
        count = Order.objects.filter(order_state=status_code).count()
        colors = {
            'pending': '#f59e0b',  # برتقالي ذهبي مشرق للتجهيز
            'waiting_shipping': '#06b6d4',  # تركوازي مشرق للانتظار
            'shipped': '#2563eb',  # أزرق حيوي للشحن
            'on_way': '#8b5cf6',  # بنفسجي جميل للطريق
            'delivered': '#10b981',  # أخضر زمردي للاستلام
            'no_answer': '#ef4444',  # أحمر حيوي لعدم الرد
            'postponed': '#6b7280',  # رمادي متوسط للتأجيل
            'wrong_address': '#ef4444',  # أحمر حيوي للعنوان الخاطئ
        }
        orders_stats[status_code] = {
            'name': status_name,
            'count': count,
            'color': colors.get(status_code, '#64748b')
        }

    context = {
        'total_stores': Stores.objects.count(),
        'total_products': Products.objects.count(),
        'total_customers': Customer.objects.count(),
        'total_orders': Order.objects.count(),
        'orders_stats': orders_stats,
        'recent_orders': Order.objects.select_related('custom', 'products').order_by('-request_date')[:10],
        'recent_products': Products.objects.select_related('store', 'categ').order_by('-datetimes')[:10],
        'top_stores': Stores.objects.annotate(
            product_count=Count('products')
        ).order_by('-product_count')[:5],
        'top_categories': Categ.objects.annotate(
            product_count=Count('products')
        ).order_by('-product_count')[:5],
    }
    return render(request, 'dashboard/home.html', context)

# عروض المناطق
@login_required
def areas_list(request):
    areas = Areas.objects.all()
    return render(request, 'dashboard/areas/list.html', {'areas': areas})

@login_required
def areas_create(request):
    if request.method == 'POST':
        form = AreasForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المنطقة بنجاح')
            return redirect('areas_list')
    else:
        form = AreasForm()
    return render(request, 'dashboard/areas/form.html', {'form': form, 'title': 'إضافة منطقة جديدة'})

@login_required
def areas_edit(request, pk):
    area = get_object_or_404(Areas, pk=pk)
    if request.method == 'POST':
        form = AreasForm(request.POST, instance=area)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث المنطقة بنجاح')
            return redirect('areas_list')
    else:
        form = AreasForm(instance=area)
    return render(request, 'dashboard/areas/form.html', {'form': form, 'title': 'تعديل المنطقة'})

@login_required
def areas_delete(request, pk):
    area = get_object_or_404(Areas, pk=pk)
    if request.method == 'POST':
        area.delete()
        messages.success(request, 'تم حذف المنطقة بنجاح')
        return redirect('areas_list')
    return render(request, 'dashboard/confirm_delete.html', {'object': area, 'type': 'المنطقة'})

# عروض أنواع المتاجر
@login_required
def store_types_list(request):
    store_types = StoreType.objects.all()
    return render(request, 'dashboard/store_types/list.html', {'store_types': store_types})

@login_required
def store_types_create(request):
    if request.method == 'POST':
        form = StoreTypeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة نوع المتجر بنجاح')
            return redirect('store_types_list')
    else:
        form = StoreTypeForm()
    return render(request, 'dashboard/store_types/form.html', {'form': form, 'title': 'إضافة نوع متجر جديد'})

@login_required
def store_types_edit(request, pk):
    store_type = get_object_or_404(StoreType, pk=pk)
    if request.method == 'POST':
        form = StoreTypeForm(request.POST, instance=store_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث نوع المتجر بنجاح')
            return redirect('store_types_list')
    else:
        form = StoreTypeForm(instance=store_type)
    return render(request, 'dashboard/store_types/form.html', {'form': form, 'title': 'تعديل نوع المتجر'})

@login_required
def store_types_delete(request, pk):
    store_type = get_object_or_404(StoreType, pk=pk)
    if request.method == 'POST':
        store_type.delete()
        messages.success(request, 'تم حذف نوع المتجر بنجاح')
        return redirect('store_types_list')
    return render(request, 'dashboard/confirm_delete.html', {'object': store_type, 'type': 'نوع المتجر'})

# عروض الشركات
@login_required
def companies_list(request):
    companies = Companies.objects.all()
    return render(request, 'dashboard/companies/list.html', {'companies': companies})

@login_required
def companies_create(request):
    if request.method == 'POST':
        form = CompaniesForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الشركة بنجاح')
            return redirect('companies_list')
    else:
        form = CompaniesForm()
    return render(request, 'dashboard/companies/form.html', {'form': form, 'title': 'إضافة شركة جديدة'})

@login_required
def companies_edit(request, pk):
    company = get_object_or_404(Companies, pk=pk)
    if request.method == 'POST':
        form = CompaniesForm(request.POST, instance=company)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الشركة بنجاح')
            return redirect('companies_list')
    else:
        form = CompaniesForm(instance=company)
    return render(request, 'dashboard/companies/form.html', {'form': form, 'title': 'تعديل الشركة'})

@login_required
def companies_delete(request, pk):
    company = get_object_or_404(Companies, pk=pk)
    if request.method == 'POST':
        company.delete()
        messages.success(request, 'تم حذف الشركة بنجاح')
        return redirect('companies_list')
    return render(request, 'dashboard/confirm_delete.html', {'object': company, 'type': 'الشركة'})

# عروض المتاجر
@login_required
def stores_list(request):
    stores = Stores.objects.select_related('area', 'store_type').all()
    return render(request, 'dashboard/stores/list.html', {'stores': stores})

@login_required
def stores_detail(request, pk):
    store = get_object_or_404(Stores, pk=pk)
    products = Products.objects.filter(store=store)
    return render(request, 'dashboard/stores/detail.html', {'store': store, 'products': products})

@login_required
def stores_create(request):
    if request.method == 'POST':
        form = StoresForm(request.POST, request.FILES)
        if form.is_valid():
            store = form.save(commit=False)
            # سيتم توليد البيانات تلقائياً في دالة save()
            store.save()

            # الحصول على البيانات المُولدة
            credentials = store.get_store_credentials()

            # إضافة رسالة نجاح مع البيانات المُولدة
            success_message = f'''
            تم إنشاء المتجر "{store.store_name}" بنجاح!<br>
            <strong>بيانات الحساب المُولدة:</strong><br>
            • معرف المتجر: {credentials['id_store']}<br>
            • اسم المستخدم: {credentials['username']}<br>
            • كلمة المرور: {credentials['password']}<br>
            • البريد الإلكتروني: {credentials['email']}
            '''
            messages.success(request, success_message)

            # إعادة توجيه لصفحة تفاصيل المتجر لعرض البيانات
            return redirect('stores_detail', pk=store.pk)
    else:
        form = StoresForm()
    return render(request, 'dashboard/stores/form.html', {'form': form, 'title': 'إضافة متجر جديد'})

@login_required
def stores_edit(request, pk):
    store = get_object_or_404(Stores, pk=pk)
    if request.method == 'POST':
        form = StoresForm(request.POST, request.FILES, instance=store)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث المتجر "{store.store_name}" بنجاح!')
            return redirect('stores_detail', pk=pk)
    else:
        form = StoresForm(instance=store)
    return render(request, 'dashboard/stores/form.html', {'form': form, 'store': store, 'title': f'تعديل المتجر: {store.store_name}'})

@login_required
def stores_delete(request, pk):
    store = get_object_or_404(Stores, pk=pk)
    if request.method == 'POST':
        store_name = store.store_name
        store.delete()
        messages.success(request, f'تم حذف المتجر "{store_name}" بنجاح!')
        return redirect('stores_list')
    return render(request, 'dashboard/confirm_delete.html', {'object': store, 'type': 'المتجر'})

@login_required
def stores_regenerate_password(request, pk):
    """إعادة توليد كلمة مرور المتجر"""
    from django.http import JsonResponse

    if request.method == 'POST':
        store = get_object_or_404(Stores, pk=pk)
        try:
            new_password = store.regenerate_password()
            return JsonResponse({
                'success': True,
                'new_password': new_password,
                'message': 'تم إعادة توليد كلمة المرور بنجاح'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})

@login_required
def stores_map(request):
    """خريطة المتاجر التفاعلية"""
    from django.db.models import Count, Q
    from django.http import JsonResponse

    # إذا كان الطلب AJAX لجلب بيانات المتاجر
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # الحصول على فلاتر البحث
        area_id = request.GET.get('area')
        store_type_id = request.GET.get('store_type')
        status = request.GET.get('status')
        is_24_hours = request.GET.get('is_24_hours')
        search_query = request.GET.get('search', '').strip()

        # بناء الاستعلام
        stores = Stores.objects.filter(lat__isnull=False, long__isnull=False)

        # تطبيق الفلاتر
        if area_id:
            stores = stores.filter(area_id=area_id)

        if store_type_id:
            stores = stores.filter(store_type_id=store_type_id)

        if status == 'active':
            stores = stores.filter(enable=True)
        elif status == 'inactive':
            stores = stores.filter(enable=False)

        if is_24_hours == 'true':
            stores = stores.filter(is_found_24_for_food_stor=True)

        if search_query:
            stores = stores.filter(
                Q(store_name__icontains=search_query) |
                Q(store_person_name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        # تحويل البيانات إلى JSON
        stores_data = []
        for store in stores:
            try:
                lat = float(store.lat)
                lng = float(store.long)

                stores_data.append({
                    'id': store.id,
                    'name': store.store_name,
                    'lat': lat,
                    'lng': lng,
                    'type': store.store_type.store_type,
                    'area': store.area.area_name,
                    'person_name': store.store_person_name,
                    'phone': store.store_person_phone_number,
                    'email': store.email or 'غير محدد',
                    'is_active': store.enable,
                    'is_24_hours': store.is_found_24_for_food_stor,
                    'is_featured': store.is_static_in_main_screen,
                    'image': store.store_picture.url if store.store_picture else None,
                    'description': store.description or '',
                    'note': store.note or '',
                    'price_stor': store.price_stor,
                    'created_date': store.date_joined.strftime('%Y/%m/%d'),
                    'created_time': store.date_joined.strftime('%H:%M'),
                    'last_login': store.last_login.strftime('%Y/%m/%d %H:%M') if store.last_login else 'لم يسجل دخول',
                    'username': store.generated_username or store.username,
                    'id_store': store.id_store if hasattr(store, 'id_store') else f'STORE_{store.id}',
                    'detail_url': f'/stores/{store.id}/',
                    'edit_url': f'/stores/{store.id}/edit/',
                    'coordinates_text': f'{lat:.6f}, {lng:.6f}'
                })
            except (ValueError, TypeError):
                # تجاهل المتاجر التي لها إحداثيات غير صحيحة
                continue

        return JsonResponse({
            'stores': stores_data,
            'total_count': len(stores_data)
        })

    # عرض الصفحة العادية
    areas = Areas.objects.all().order_by('area_name')
    store_types = StoreType.objects.all().order_by('store_type')

    # إحصائيات سريعة
    total_stores = Stores.objects.filter(lat__isnull=False, long__isnull=False).count()
    active_stores = Stores.objects.filter(enable=True, lat__isnull=False, long__isnull=False).count()
    stores_24h = Stores.objects.filter(is_found_24_for_food_stor=True, lat__isnull=False, long__isnull=False).count()
    featured_stores = Stores.objects.filter(is_static_in_main_screen=True, lat__isnull=False, long__isnull=False).count()

    context = {
        'areas': areas,
        'store_types': store_types,
        'total_stores': total_stores,
        'active_stores': active_stores,
        'stores_24h': stores_24h,
        'featured_stores': featured_stores,
    }

    return render(request, 'dashboard/stores/map.html', context)

# عروض الفئات
@login_required
def categories_list(request):
    categories = Categ.objects.all()
    # إضافة عدد المنتجات لكل فئة
    for category in categories:
        category.products_count = Products.objects.filter(categ=category).count()
    return render(request, 'dashboard/categories/list.html', {'categories': categories})

@login_required
def categories_create(request):
    if request.method == 'POST':
        form = CategForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الفئة بنجاح')
            return redirect('categories_list')
    else:
        form = CategForm()
    return render(request, 'dashboard/categories/form.html', {'form': form, 'title': 'إضافة فئة جديدة'})

@login_required
def categories_edit(request, pk):
    category = get_object_or_404(Categ, pk=pk)
    if request.method == 'POST':
        form = CategForm(request.POST, request.FILES, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الفئة بنجاح')
            return redirect('categories_list')
    else:
        form = CategForm(instance=category)
    return render(request, 'dashboard/categories/form.html', {'form': form, 'title': 'تعديل الفئة'})

@login_required
def categories_delete(request, pk):
    category = get_object_or_404(Categ, pk=pk)
    if request.method == 'POST':
        category.delete()
        messages.success(request, 'تم حذف الفئة بنجاح')
        return redirect('categories_list')
    return render(request, 'dashboard/confirm_delete.html', {'object': category, 'type': 'الفئة'})

# عروض المنتجات
@login_required
def products_list(request):
    products = Products.objects.select_related('store', 'categ', 'family', 'company').all()

    # البحث والتصفية
    search = request.GET.get('search')
    if search:
        products = products.filter(
            Q(product_name__icontains=search) |
            Q(seo__icontains=search) |
            Q(store__store_name__icontains=search)
        )

    category = request.GET.get('category')
    if category:
        products = products.filter(categ_id=category)

    store = request.GET.get('store')
    if store:
        products = products.filter(store_id=store)

    # تصفية حسب السعر
    price_min = request.GET.get('price_min')
    price_max = request.GET.get('price_max')
    if price_min:
        products = products.filter(price__gte=price_min)
    if price_max:
        products = products.filter(price__lte=price_max)

    # تصفية حسب الحالة
    status = request.GET.get('status')
    if status == 'active':
        products = products.filter(enable=True)
    elif status == 'inactive':
        products = products.filter(enable=False)

    # ترتيب النتائج
    sort = request.GET.get('sort')
    if sort == 'name':
        products = products.order_by('product_name')
    elif sort == 'price':
        products = products.order_by('price')
    elif sort == 'views':
        products = products.order_by('-views')
    elif sort == 'date':
        products = products.order_by('-datetimes')

    context = {
        'products': products,
        'categories': Categ.objects.all(),
        'stores': Stores.objects.all(),
        'search': search,
        'selected_category': category,
        'selected_store': store,
    }
    return render(request, 'dashboard/products/list.html', context)

@login_required
def products_create(request):
    if request.method == 'POST':
        form = ProductsForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المنتج بنجاح')
            return redirect('products_list')
    else:
        form = ProductsForm()
    return render(request, 'dashboard/products/form.html', {'form': form, 'title': 'إضافة منتج جديد'})

@login_required
def products_edit(request, pk):
    product = get_object_or_404(Products, pk=pk)
    if request.method == 'POST':
        form = ProductsForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث المنتج بنجاح')
            return redirect('products_list')
    else:
        form = ProductsForm(instance=product)
    return render(request, 'dashboard/products/form.html', {'form': form, 'title': 'تعديل المنتج'})

@login_required
def products_detail(request, pk):
    product = get_object_or_404(Products, pk=pk)
    images = ProductsImg.objects.filter(product=product)
    return render(request, 'dashboard/products/detail.html', {'product': product, 'images': images})

@login_required
def products_delete(request, pk):
    product = get_object_or_404(Products, pk=pk)
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'تم حذف المنتج بنجاح')
        return redirect('products_list')
    return render(request, 'dashboard/confirm_delete.html', {'object': product, 'type': 'المنتج'})

# عروض العملاء
@login_required
def customers_list(request):
    customers = Customer.objects.all()
    return render(request, 'dashboard/customers/list.html', {'customers': customers})

@login_required
def customers_detail(request, pk):
    customer = get_object_or_404(Customer, pk=pk)
    orders = Order.objects.filter(custom=customer).select_related('products')
    return render(request, 'dashboard/customers/detail.html', {'customer': customer, 'orders': orders})

# عروض الطلبات
@login_required
def orders_list(request):
    orders = Order.objects.select_related('custom', 'products').all()

    # التصفية حسب الحالة
    status = request.GET.get('status')
    if status:
        orders = orders.filter(order_state=status)

    context = {
        'orders': orders,
        'selected_status': status,
        'order_states': Order.objects.values_list('order_state', flat=True).distinct(),
    }
    return render(request, 'dashboard/orders/list.html', context)

@login_required
def orders_detail(request, pk):
    order = get_object_or_404(Order, pk=pk)
    return render(request, 'dashboard/orders/detail.html', {'order': order})

@login_required
def orders_edit(request, pk):
    order = get_object_or_404(Order, pk=pk)
    if request.method == 'POST':
        form = OrderForm(request.POST, instance=order)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الطلب #{order.id} بنجاح!')
            return redirect('orders_detail', pk=order.pk)
    else:
        form = OrderForm(instance=order)

    context = {
        'form': form,
        'order': order,
        'title': f'تعديل الطلب #{order.id}'
    }
    return render(request, 'dashboard/orders/edit.html', context)

@login_required
def orders_update_status(request, pk):
    if request.method == 'POST':
        order = get_object_or_404(Order, pk=pk)
        new_status = request.POST.get('status')

        if new_status in dict(Order.ORDER_STATUS_CHOICES):
            old_status = order.get_order_state_display()
            order.order_state = new_status
            order.save()

            new_status_display = order.get_order_state_display()
            messages.success(request, f'تم تغيير حالة الطلب من "{old_status}" إلى "{new_status_display}"')
        else:
            messages.error(request, 'حالة الطلب غير صحيحة')

    return redirect('orders_detail', pk=pk)

@login_required
def orders_add_note(request, pk):
    order = get_object_or_404(Order, pk=pk)

    if request.method == 'POST':
        note = request.POST.get('note', '').strip()
        if note:
            # يمكن إضافة نموذج ملاحظات منفصل هنا
            # لكن الآن سنستخدم رسالة نجاح
            messages.success(request, f'تم إضافة الملاحظة: "{note}"')
        else:
            messages.error(request, 'يرجى كتابة ملاحظة')

    return redirect('orders_detail', pk=pk)

@login_required
def orders_print(request, pk):
    order = get_object_or_404(Order, pk=pk)
    context = {
        'order': order,
        'print_date': timezone.now()
    }
    return render(request, 'dashboard/orders/print.html', context)

@login_required
def orders_history(request, pk):
    order = get_object_or_404(Order, pk=pk)
    # يمكن إضافة نموذج سجل التغييرات هنا
    context = {
        'order': order,
        'history': []  # سيتم تطويره لاحقاً
    }
    return render(request, 'dashboard/orders/history.html', context)

@login_required
def orders_delete(request, pk):
    order = get_object_or_404(Order, pk=pk)

    if request.method == 'POST':
        order_id = order.id
        order.delete()
        messages.success(request, f'تم حذف الطلب #{order_id} بنجاح!')
        return redirect('orders_list')

    context = {
        'order': order,
        'title': f'حذف الطلب #{order.id}'
    }
    return render(request, 'dashboard/orders/confirm_delete.html', context)

# النظام المحاسبي
@login_required
def accounting_dashboard(request):
    """لوحة تحكم النظام المحاسبي"""
    # إحصائيات عامة
    total_stores = Stores.objects.count()
    active_periods = AccountingPeriod.objects.filter(status='open').count()
    total_commission = StoreAccount.objects.aggregate(
        total=models.Sum('commission_amount')
    )['total'] or 0
    total_paid = Payment.objects.aggregate(
        total=models.Sum('amount')
    )['total'] or 0

    # أحدث الفترات
    recent_periods = AccountingPeriod.objects.all()[:5]

    # المتاجر التي لديها مستحقات
    pending_accounts = StoreAccount.objects.filter(
        remaining_amount__gt=0,
        period__status='open'
    ).select_related('store', 'period')[:10]

    # أحدث الدفعات
    recent_payments = Payment.objects.select_related(
        'store_account__store'
    ).all()[:10]

    context = {
        'total_stores': total_stores,
        'active_periods': active_periods,
        'total_commission': total_commission,
        'total_paid': total_paid,
        'total_pending': total_commission - total_paid,
        'recent_periods': recent_periods,
        'pending_accounts': pending_accounts,
        'recent_payments': recent_payments,
    }
    return render(request, 'dashboard/accounting/dashboard.html', context)

@login_required
def commission_settings_list(request):
    """قائمة إعدادات العمولات"""
    settings = CommissionSettings.objects.select_related('store').all()

    # إحصائيات
    total_settings = settings.count()
    active_settings = settings.filter(is_active=True).count()

    context = {
        'settings': settings,
        'total_settings': total_settings,
        'active_settings': active_settings,
    }
    return render(request, 'dashboard/accounting/commission_settings_list.html', context)

@login_required
def commission_settings_create(request):
    """إنشاء إعدادات عمولة جديدة"""
    if request.method == 'POST':
        form = CommissionSettingsForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إنشاء إعدادات العمولة بنجاح!')
            return redirect('commission_settings_list')
    else:
        form = CommissionSettingsForm()

    context = {
        'form': form,
        'title': 'إنشاء إعدادات عمولة جديدة'
    }
    return render(request, 'dashboard/accounting/commission_settings_form.html', context)

@login_required
def commission_settings_edit(request, pk):
    """تعديل إعدادات العمولة"""
    settings = get_object_or_404(CommissionSettings, pk=pk)

    if request.method == 'POST':
        form = CommissionSettingsForm(request.POST, instance=settings)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث إعدادات العمولة بنجاح!')
            return redirect('commission_settings_list')
    else:
        form = CommissionSettingsForm(instance=settings)

    context = {
        'form': form,
        'settings': settings,
        'title': f'تعديل إعدادات عمولة {settings.store.store_name}'
    }
    return render(request, 'dashboard/accounting/commission_settings_form.html', context)

@login_required
def accounting_periods_list(request):
    """قائمة الفترات المحاسبية"""
    periods = AccountingPeriod.objects.all()

    context = {
        'periods': periods,
    }
    return render(request, 'dashboard/accounting/periods_list.html', context)

@login_required
def accounting_period_create(request):
    """إنشاء فترة محاسبية جديدة"""
    if request.method == 'POST':
        form = AccountingPeriodForm(request.POST)
        if form.is_valid():
            period = form.save()
            messages.success(request, f'تم إنشاء الفترة المحاسبية "{period.name}" بنجاح!')
            return redirect('accounting_periods_list')
    else:
        form = AccountingPeriodForm()

    context = {
        'form': form,
        'title': 'إنشاء فترة محاسبية جديدة'
    }
    return render(request, 'dashboard/accounting/period_form.html', context)

@login_required
def accounting_period_detail(request, pk):
    """تفاصيل الفترة المحاسبية"""
    period = get_object_or_404(AccountingPeriod, pk=pk)
    store_accounts = StoreAccount.objects.filter(period=period).select_related('store')

    # إحصائيات الفترة
    total_stores = store_accounts.count()
    total_orders = store_accounts.aggregate(total=models.Sum('orders_count'))['total'] or 0
    total_revenue = store_accounts.aggregate(total=models.Sum('total_orders_value'))['total'] or 0
    total_commission = store_accounts.aggregate(total=models.Sum('commission_amount'))['total'] or 0
    total_paid = store_accounts.aggregate(total=models.Sum('paid_amount'))['total'] or 0
    total_remaining = store_accounts.aggregate(total=models.Sum('remaining_amount'))['total'] or 0

    context = {
        'period': period,
        'store_accounts': store_accounts,
        'total_stores': total_stores,
        'total_orders': total_orders,
        'total_revenue': total_revenue,
        'total_commission': total_commission,
        'total_paid': total_paid,
        'total_remaining': total_remaining,
    }
    return render(request, 'dashboard/accounting/period_detail.html', context)

@login_required
def calculate_period_accounts(request, pk):
    """حساب حسابات المتاجر للفترة المحاسبية"""
    period = get_object_or_404(AccountingPeriod, pk=pk)

    if period.status != 'open':
        messages.error(request, 'لا يمكن إعادة حساب فترة مغلقة أو نهائية')
        return redirect('accounting_period_detail', pk=pk)

    # حذف الحسابات الموجودة
    StoreAccount.objects.filter(period=period).delete()

    # حساب الطلبات لكل متجر في الفترة
    orders_in_period = Order.objects.filter(
        request_date__range=[period.start_date, period.end_date],
        products__store__isnull=False
    ).select_related('products__store')

    store_data = {}
    for order in orders_in_period:
        store = order.products.store
        if store.id not in store_data:
            store_data[store.id] = {
                'store': store,
                'orders_count': 0,
                'total_value': 0
            }
        store_data[store.id]['orders_count'] += 1
        store_data[store.id]['total_value'] += order.all_price

    # إنشاء حسابات المتاجر
    created_accounts = 0
    for store_id, data in store_data.items():
        store_account = StoreAccount.objects.create(
            period=period,
            store=data['store'],
            orders_count=data['orders_count'],
            total_orders_value=data['total_value']
        )
        store_account.calculate_commission()
        created_accounts += 1

    # تحديث إحصائيات الفترة
    period.total_orders = sum(data['orders_count'] for data in store_data.values())
    period.total_revenue = sum(data['total_value'] for data in store_data.values())
    period.total_commission = StoreAccount.objects.filter(period=period).aggregate(
        total=models.Sum('commission_amount')
    )['total'] or 0
    period.save()

    messages.success(request, f'تم حساب {created_accounts} حساب متجر للفترة المحاسبية')
    return redirect('accounting_period_detail', pk=pk)

@login_required
def store_account_detail(request, pk):
    """تفاصيل حساب المتجر"""
    store_account = get_object_or_404(StoreAccount, pk=pk)
    payments = store_account.payments.all()

    context = {
        'store_account': store_account,
        'payments': payments,
    }
    return render(request, 'dashboard/accounting/store_account_detail.html', context)

@login_required
def add_payment(request, store_account_id):
    """إضافة دفعة لحساب المتجر"""
    store_account = get_object_or_404(StoreAccount, pk=store_account_id)

    if request.method == 'POST':
        form = PaymentForm(request.POST, store_account=store_account)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.store_account = store_account
            payment.created_by = request.user
            payment.save()
            messages.success(request, f'تم إضافة دفعة بمبلغ {payment.amount} د.ع بنجاح!')
            return redirect('store_account_detail', pk=store_account.pk)
    else:
        form = PaymentForm(store_account=store_account)

    context = {
        'form': form,
        'store_account': store_account,
        'title': f'إضافة دفعة لحساب {store_account.store.store_name}'
    }
    return render(request, 'dashboard/accounting/payment_form.html', context)

@login_required
def payments_list(request):
    """قائمة جميع الدفعات"""
    payments = Payment.objects.select_related(
        'store_account__store',
        'store_account__period',
        'created_by'
    ).all()

    # فلترة حسب المتجر إذا تم تحديده
    store_id = request.GET.get('store')
    if store_id:
        payments = payments.filter(store_account__store_id=store_id)

    # فلترة حسب الفترة إذا تم تحديدها
    period_id = request.GET.get('period')
    if period_id:
        payments = payments.filter(store_account__period_id=period_id)

    # إحصائيات
    total_payments = payments.count()
    total_amount = payments.aggregate(total=models.Sum('amount'))['total'] or 0

    context = {
        'payments': payments,
        'total_payments': total_payments,
        'total_amount': total_amount,
        'stores': Stores.objects.all(),
        'periods': AccountingPeriod.objects.all(),
        'selected_store': store_id,
        'selected_period': period_id,
    }
    return render(request, 'dashboard/accounting/payments_list.html', context)

@login_required
def accounting_reports(request):
    """تقارير المحاسبة"""
    if request.method == 'POST':
        form = AccountingReportForm(request.POST)
        if form.is_valid():
            # معالجة التقرير وإرجاع البيانات
            report_data = generate_accounting_report(form.cleaned_data)
            context = {
                'form': form,
                'report_data': report_data,
                'show_report': True
            }
            return render(request, 'dashboard/accounting/reports.html', context)
    else:
        form = AccountingReportForm()

    context = {
        'form': form,
        'show_report': False
    }
    return render(request, 'dashboard/accounting/reports.html', context)

def generate_accounting_report(data):
    """توليد بيانات التقرير"""
    report_type = data['report_type']
    period = data.get('period')
    store = data.get('store')
    start_date = data.get('start_date')
    end_date = data.get('end_date')

    # بناء الاستعلام الأساسي
    queryset = StoreAccount.objects.select_related('store', 'period')

    if period:
        queryset = queryset.filter(period=period)
    if store:
        queryset = queryset.filter(store=store)
    if start_date:
        queryset = queryset.filter(period__start_date__gte=start_date)
    if end_date:
        queryset = queryset.filter(period__end_date__lte=end_date)

    # فلترة حسب حالة التسديد
    if not data.get('include_settled', True):
        queryset = queryset.exclude(is_settled=True)
    if not data.get('include_unsettled', True):
        queryset = queryset.exclude(is_settled=False)

    # إحصائيات عامة
    total_accounts = queryset.count()
    total_orders = queryset.aggregate(total=models.Sum('orders_count'))['total'] or 0
    total_revenue = queryset.aggregate(total=models.Sum('total_orders_value'))['total'] or 0
    total_commission = queryset.aggregate(total=models.Sum('commission_amount'))['total'] or 0
    total_paid = queryset.aggregate(total=models.Sum('paid_amount'))['total'] or 0
    total_remaining = queryset.aggregate(total=models.Sum('remaining_amount'))['total'] or 0

    return {
        'report_type': report_type,
        'accounts': queryset,
        'total_accounts': total_accounts,
        'total_orders': total_orders,
        'total_revenue': total_revenue,
        'total_commission': total_commission,
        'total_paid': total_paid,
        'total_remaining': total_remaining,
        'filters': data
    }

# العروض الخاصة
@login_required
def special_offers_list(request):
    """قائمة العروض الخاصة"""
    offers = SpecialOffer.objects.all()

    # تطبيق الفلاتر
    filter_form = OfferFilterForm(request.GET)
    if filter_form.is_valid():
        if filter_form.cleaned_data['status']:
            offers = offers.filter(status=filter_form.cleaned_data['status'])
        if filter_form.cleaned_data['offer_type']:
            offers = offers.filter(offer_type=filter_form.cleaned_data['offer_type'])
        if filter_form.cleaned_data['store']:
            offers = offers.filter(target_stores=filter_form.cleaned_data['store'])
        if filter_form.cleaned_data['is_featured']:
            offers = offers.filter(is_featured=True)
        if filter_form.cleaned_data['search']:
            search_term = filter_form.cleaned_data['search']
            offers = offers.filter(
                Q(title__icontains=search_term) |
                Q(description__icontains=search_term)
            )

    # إحصائيات
    total_offers = offers.count()
    active_offers = offers.filter(status='active').count()
    featured_offers = offers.filter(is_featured=True).count()

    context = {
        'offers': offers,
        'filter_form': filter_form,
        'total_offers': total_offers,
        'active_offers': active_offers,
        'featured_offers': featured_offers,
    }
    return render(request, 'dashboard/offers/offers_list.html', context)

@login_required
def special_offer_create(request):
    """إنشاء عرض خاص جديد"""
    if request.method == 'POST':
        form = SpecialOfferForm(request.POST, request.FILES)
        if form.is_valid():
            offer = form.save(commit=False)
            offer.created_by = request.user
            offer.save()
            form.save_m2m()  # حفظ العلاقات many-to-many
            messages.success(request, f'تم إنشاء العرض "{offer.title}" بنجاح!')
            return redirect('special_offers_list')
    else:
        form = SpecialOfferForm()

    context = {
        'form': form,
        'title': 'إنشاء عرض خاص جديد'
    }
    return render(request, 'dashboard/offers/offer_form.html', context)

@login_required
def special_offer_edit(request, pk):
    """تعديل عرض خاص"""
    offer = get_object_or_404(SpecialOffer, pk=pk)

    if request.method == 'POST':
        form = SpecialOfferForm(request.POST, request.FILES, instance=offer)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث العرض "{offer.title}" بنجاح!')
            return redirect('special_offers_list')
    else:
        form = SpecialOfferForm(instance=offer)

    context = {
        'form': form,
        'offer': offer,
        'title': f'تعديل العرض: {offer.title}'
    }
    return render(request, 'dashboard/offers/offer_form.html', context)

@login_required
def special_offer_detail(request, pk):
    """تفاصيل العرض الخاص"""
    offer = get_object_or_404(SpecialOffer, pk=pk)

    # إحصائيات الاستخدام
    usage_stats = OfferUsage.objects.filter(offer=offer).aggregate(
        total_usage=Count('id'),
        total_discount=Sum('discount_amount'),
        unique_customers=Count('customer', distinct=True)
    )

    # أحدث الاستخدامات
    recent_usage = OfferUsage.objects.filter(offer=offer).select_related(
        'customer', 'order'
    ).order_by('-usage_date')[:10]

    context = {
        'offer': offer,
        'usage_stats': usage_stats,
        'recent_usage': recent_usage,
    }
    return render(request, 'dashboard/offers/offer_detail.html', context)

@login_required
def special_offer_toggle_status(request, pk):
    """تغيير حالة العرض"""
    offer = get_object_or_404(SpecialOffer, pk=pk)

    if request.method == 'POST':
        new_status = request.POST.get('status')
        if new_status in dict(SpecialOffer.STATUS_CHOICES):
            old_status = offer.get_status_display()
            offer.status = new_status
            offer.save()
            new_status_display = offer.get_status_display()
            messages.success(request, f'تم تغيير حالة العرض من "{old_status}" إلى "{new_status_display}"')
        else:
            messages.error(request, 'حالة غير صحيحة')

    return redirect('special_offer_detail', pk=pk)

@login_required
def special_offer_delete(request, pk):
    """حذف عرض خاص"""
    offer = get_object_or_404(SpecialOffer, pk=pk)

    if request.method == 'POST':
        offer_title = offer.title
        offer.delete()
        messages.success(request, f'تم حذف العرض "{offer_title}" بنجاح!')
        return redirect('special_offers_list')

    context = {
        'offer': offer,
        'title': f'حذف العرض: {offer.title}'
    }
    return render(request, 'dashboard/offers/offer_confirm_delete.html', context)

# عروض الإشعارات
@login_required
def notifications_list(request):
    notifications = Notifications.objects.select_related('store').all()
    return render(request, 'dashboard/notifications/list.html', {'notifications': notifications})

@login_required
def notifications_create(request):
    if request.method == 'POST':
        form = NotificationsForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الإشعار بنجاح')
            return redirect('notifications_list')
    else:
        form = NotificationsForm()
    return render(request, 'dashboard/notifications/form.html', {'form': form, 'title': 'إضافة إشعار جديد'})

# عروض السلال
@login_required
def baskets_list(request):
    baskets = BasketsTestHeaders.objects.all()
    return render(request, 'dashboard/baskets/list.html', {'baskets': baskets})

@login_required
def baskets_detail(request, pk):
    basket = get_object_or_404(BasketsTestHeaders, pk=pk)
    items = BasketsTest.objects.filter(basket_headers=basket).select_related('products')
    return render(request, 'dashboard/baskets/detail.html', {'basket': basket, 'items': items})

# API للإحصائيات
@login_required
def api_stats(request):
    data = {
        'stores_count': Stores.objects.count(),
        'products_count': Products.objects.count(),
        'customers_count': Customer.objects.count(),
        'orders_count': Order.objects.count(),
        'total_sales': Order.objects.aggregate(total=Sum('all_price'))['total'] or 0,
    }
    return JsonResponse(data)

# نظام الإضافة المتعددة
@login_required
@require_http_methods(["POST"])
def bulk_add_areas(request):
    """إضافة مناطق متعددة"""
    try:
        data = json.loads(request.body)
        items = data.get('items', [])

        if not items:
            return JsonResponse({'success': False, 'message': 'لا توجد بيانات للحفظ'})

        saved_count = 0
        errors = []

        for item in items:
            try:
                area_name = item.get('area_name', '').strip()
                if area_name:
                    # التحقق من عدم وجود منطقة بنفس الاسم
                    if not Areas.objects.filter(area_name=area_name).exists():
                        Areas.objects.create(area_name=area_name)
                        saved_count += 1
                    else:
                        errors.append(f'المنطقة "{area_name}" موجودة مسبقاً')
            except Exception as e:
                errors.append(f'خطأ في حفظ المنطقة: {str(e)}')

        if saved_count > 0:
            return JsonResponse({
                'success': True,
                'saved_count': saved_count,
                'message': f'تم حفظ {saved_count} منطقة بنجاح',
                'errors': errors
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم حفظ أي منطقة',
                'errors': errors
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ في الخادم: {str(e)}'})

@login_required
@require_http_methods(["POST"])
def bulk_add_companies(request):
    """إضافة شركات متعددة"""
    try:
        data = json.loads(request.body)
        items = data.get('items', [])

        if not items:
            return JsonResponse({'success': False, 'message': 'لا توجد بيانات للحفظ'})

        saved_count = 0
        errors = []

        for item in items:
            try:
                company_name = item.get('name', '').strip()
                if company_name:
                    # التحقق من عدم وجود شركة بنفس الاسم
                    if not Companies.objects.filter(name=company_name).exists():
                        Companies.objects.create(name=company_name)
                        saved_count += 1
                    else:
                        errors.append(f'الشركة "{company_name}" موجودة مسبقاً')
            except Exception as e:
                errors.append(f'خطأ في حفظ الشركة: {str(e)}')

        if saved_count > 0:
            return JsonResponse({
                'success': True,
                'saved_count': saved_count,
                'message': f'تم حفظ {saved_count} شركة بنجاح',
                'errors': errors
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم حفظ أي شركة',
                'errors': errors
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ في الخادم: {str(e)}'})

@login_required
@require_http_methods(["POST"])
def bulk_add_store_types(request):
    """إضافة أنواع متاجر متعددة"""
    try:
        data = json.loads(request.body)
        items = data.get('items', [])

        if not items:
            return JsonResponse({'success': False, 'message': 'لا توجد بيانات للحفظ'})

        saved_count = 0
        errors = []

        for item in items:
            try:
                store_type_name = item.get('store_type', '').strip()
                if store_type_name:
                    # التحقق من عدم وجود نوع متجر بنفس الاسم
                    if not StoreType.objects.filter(store_type=store_type_name).exists():
                        StoreType.objects.create(store_type=store_type_name)
                        saved_count += 1
                    else:
                        errors.append(f'نوع المتجر "{store_type_name}" موجود مسبقاً')
            except Exception as e:
                errors.append(f'خطأ في حفظ نوع المتجر: {str(e)}')

        if saved_count > 0:
            return JsonResponse({
                'success': True,
                'saved_count': saved_count,
                'message': f'تم حفظ {saved_count} نوع متجر بنجاح',
                'errors': errors
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم حفظ أي نوع متجر',
                'errors': errors
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ في الخادم: {str(e)}'})

@login_required
@require_http_methods(["POST"])
def bulk_add_categories(request):
    """إضافة فئات متعددة"""
    try:
        data = json.loads(request.body)
        items = data.get('items', [])

        if not items:
            return JsonResponse({'success': False, 'message': 'لا توجد بيانات للحفظ'})

        saved_count = 0
        errors = []

        for item in items:
            try:
                categ_name = item.get('categ_name', '').strip()
                note = item.get('note', '').strip()

                if categ_name:
                    # التحقق من عدم وجود فئة بنفس الاسم
                    if not Categ.objects.filter(categ_name=categ_name).exists():
                        Categ.objects.create(
                            categ_name=categ_name,
                            note=note
                        )
                        saved_count += 1
                    else:
                        errors.append(f'الفئة "{categ_name}" موجودة مسبقاً')
            except Exception as e:
                errors.append(f'خطأ في حفظ الفئة: {str(e)}')

        if saved_count > 0:
            return JsonResponse({
                'success': True,
                'saved_count': saved_count,
                'message': f'تم حفظ {saved_count} فئة بنجاح',
                'errors': errors
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم حفظ أي فئة',
                'errors': errors
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ في الخادم: {str(e)}'})

@login_required
@require_http_methods(["POST"])
def bulk_add_customers(request):
    """إضافة عملاء متعددين"""
    try:
        data = json.loads(request.body)
        items = data.get('items', [])

        if not items:
            return JsonResponse({'success': False, 'message': 'لا توجد بيانات للحفظ'})

        saved_count = 0
        errors = []

        for item in items:
            try:
                name = item.get('name', '').strip()
                phone_number = item.get('phone_number', '').strip()
                place = item.get('place', '').strip()

                if name and phone_number:
                    # التحقق من عدم وجود عميل بنفس رقم الهاتف
                    if not Customer.objects.filter(phone_number=phone_number).exists():
                        Customer.objects.create(
                            name=name,
                            phone_number=phone_number,
                            place=place
                        )
                        saved_count += 1
                    else:
                        errors.append(f'العميل برقم الهاتف "{phone_number}" موجود مسبقاً')
            except Exception as e:
                errors.append(f'خطأ في حفظ العميل: {str(e)}')

        if saved_count > 0:
            return JsonResponse({
                'success': True,
                'saved_count': saved_count,
                'message': f'تم حفظ {saved_count} عميل بنجاح',
                'errors': errors
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم حفظ أي عميل',
                'errors': errors
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ في الخادم: {str(e)}'})
