{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}التقييمات{% endblock %}

{% block extra_css %}
<style>
    .rating-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        margin-bottom: 1rem;
        transition: all 0.3s;
    }
    .rating-card:hover {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transform: translateY(-2px);
    }
    .rating-stars {
        color: #f6c23e;
        font-size: 1.2rem;
    }
    .rating-type-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    .stats-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-star text-warning"></i>
            إدارة التقييمات
        </h1>
        <div>
            <a href="{% url 'ratings_create' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i>
                إضافة تقييم جديد
            </a>
            <a href="{% url 'ratings_statistics' %}" class="btn btn-info btn-sm">
                <i class="fas fa-chart-bar"></i>
                الإحصائيات
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="h4 mb-0">{{ total_ratings }}</div>
                <div class="small">إجمالي التقييمات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="h4 mb-0">{{ average_rating }}</div>
                <div class="small">متوسط التقييم</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="h4 mb-0">
                    {% for i in "12345" %}
                        {% if average_rating >= forloop.counter %}
                            <i class="fas fa-star text-warning"></i>
                        {% else %}
                            <i class="far fa-star text-warning"></i>
                        {% endif %}
                    {% endfor %}
                </div>
                <div class="small">تقييم النجوم</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="h4 mb-0">
                    <i class="fas fa-thumbs-up"></i>
                </div>
                <div class="small">نظام التقييمات</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                فلاتر البحث
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row">
                <div class="col-md-2">
                    {{ form.rating_type.label_tag }}
                    {{ form.rating_type }}
                </div>
                <div class="col-md-2">
                    {{ form.rating_value.label_tag }}
                    {{ form.rating_value }}
                </div>
                <div class="col-md-2">
                    {{ form.store.label_tag }}
                    {{ form.store }}
                </div>
                <div class="col-md-2">
                    {{ form.customer.label_tag }}
                    {{ form.customer }}
                </div>
                <div class="col-md-2">
                    {{ form.search.label_tag }}
                    {{ form.search }}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-sm me-2">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <a href="{% url 'ratings_list' %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة التقييمات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i>
                قائمة التقييمات ({{ ratings.count }})
            </h6>
        </div>
        <div class="card-body">
            {% if ratings %}
                <div class="row">
                    {% for rating in ratings %}
                    <div class="col-md-6 col-lg-4">
                        <div class="rating-card p-3">
                            <!-- نوع التقييم -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                {% if rating.rating_type == 'store' %}
                                    <span class="badge badge-primary rating-type-badge">
                                        <i class="fas fa-store"></i>
                                        تقييم متجر
                                    </span>
                                {% elif rating.rating_type == 'customer' %}
                                    <span class="badge badge-success rating-type-badge">
                                        <i class="fas fa-user"></i>
                                        تقييم عميل
                                    </span>
                                {% elif rating.rating_type == 'delivery' %}
                                    <span class="badge badge-warning rating-type-badge">
                                        <i class="fas fa-motorcycle"></i>
                                        تقييم توصيل
                                    </span>
                                {% endif %}
                                
                                {% if rating.is_verified %}
                                    <span class="badge badge-success">
                                        <i class="fas fa-check-circle"></i>
                                        موثق
                                    </span>
                                {% endif %}
                            </div>

                            <!-- النجوم -->
                            <div class="rating-stars mb-2">
                                {% for i in "12345" %}
                                    {% if rating.rating_value >= forloop.counter %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                                <span class="text-muted small">({{ rating.rating_value }}/5)</span>
                            </div>

                            <!-- المُقيَّم -->
                            <div class="mb-2">
                                <strong>المُقيَّم:</strong>
                                <span class="text-primary">{{ rating.rated_name }}</span>
                            </div>

                            <!-- المُقيِّم -->
                            <div class="mb-2">
                                <strong>المُقيِّم:</strong>
                                <span class="text-secondary">{{ rating.reviewer_name }}</span>
                            </div>

                            <!-- التعليق -->
                            {% if rating.comment %}
                            <div class="mb-2">
                                <small class="text-muted">
                                    "{{ rating.comment|truncatechars:100 }}"
                                </small>
                            </div>
                            {% endif %}

                            <!-- التاريخ -->
                            <div class="text-muted small mb-3">
                                <i class="fas fa-clock"></i>
                                {{ rating.created_at|date:"d/m/Y H:i" }}
                            </div>

                            <!-- الأزرار -->
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'ratings_detail' rating.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                                <div>
                                    <a href="{% url 'ratings_edit' rating.id %}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'ratings_respond' rating.id %}" class="btn btn-success btn-sm">
                                        <i class="fas fa-reply"></i>
                                        رد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تقييمات</h5>
                    <p class="text-muted">لم يتم العثور على أي تقييمات بالمعايير المحددة</p>
                    <a href="{% url 'ratings_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة تقييم جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحسين تفاعل البطاقات
    $('.rating-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
});
</script>
{% endblock %}
