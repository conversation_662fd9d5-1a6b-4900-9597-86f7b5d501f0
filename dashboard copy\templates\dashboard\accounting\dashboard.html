{% extends 'dashboard/base.html' %}

{% block title %}النظام المحاسبي - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">النظام المحاسبي</li>
{% endblock %}

{% block extra_css %}
<style>
.accounting-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.accounting-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.accounting-stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

.quick-action-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.pending-account {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.recent-payment {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left: 4px solid #17a2b8;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-calculator me-2"></i>
            النظام المحاسبي
        </h1>
        <p class="text-muted">إدارة شاملة للحسابات والعمولات مع المتاجر</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{% url 'accounting_period_create' %}" class="btn quick-action-btn">
                <i class="fas fa-plus me-2"></i>
                فترة جديدة
            </a>
            <a href="{% url 'accounting_reports' %}" class="btn btn-outline-primary">
                <i class="fas fa-chart-bar me-2"></i>
                التقارير
            </a>
        </div>
    </div>
</div>

<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="accounting-stats-card">
            <div class="stats-number">{{ total_stores }}</div>
            <div class="stats-label">
                <i class="fas fa-store me-2"></i>
                إجمالي المتاجر
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="accounting-stats-card">
            <div class="stats-number">{{ active_periods }}</div>
            <div class="stats-label">
                <i class="fas fa-calendar-alt me-2"></i>
                الفترات النشطة
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="accounting-stats-card">
            <div class="stats-number">{{ total_commission|floatformat:0 }}</div>
            <div class="stats-label">
                <i class="fas fa-coins me-2"></i>
                إجمالي العمولات (د.ع)
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="accounting-stats-card">
            <div class="stats-number">{{ total_pending|floatformat:0 }}</div>
            <div class="stats-label">
                <i class="fas fa-hourglass-half me-2"></i>
                المبالغ المعلقة (د.ع)
            </div>
        </div>
    </div>
</div>

<!-- المحتوى الرئيسي -->
<div class="row">
    <!-- الفترات المحاسبية الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="accounting-card">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2 text-primary"></i>
                        الفترات المحاسبية الحديثة
                    </h5>
                    <a href="{% url 'accounting_periods_list' %}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_periods %}
                    {% for period in recent_periods %}
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div>
                            <strong>{{ period.name }}</strong>
                            <br>
                            <small class="text-muted">
                                {{ period.start_date }} - {{ period.end_date }}
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{% if period.status == 'open' %}success{% elif period.status == 'closed' %}warning{% else %}secondary{% endif %}">
                                {{ period.get_status_display }}
                            </span>
                            <br>
                            <small class="text-muted">{{ period.total_orders }} طلب</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد فترات محاسبية</p>
                        <a href="{% url 'accounting_period_create' %}" class="btn btn-primary">
                            إنشاء فترة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الحسابات المعلقة -->
    <div class="col-lg-6 mb-4">
        <div class="accounting-card">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                        الحسابات المعلقة
                    </h5>
                    <a href="{% url 'payments_list' %}" class="btn btn-sm btn-outline-warning">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if pending_accounts %}
                    {% for account in pending_accounts %}
                    <div class="pending-account">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ account.store.store_name }}</strong>
                                <br>
                                <small class="text-muted">{{ account.period.name }}</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-warning">{{ account.remaining_amount|floatformat:0 }} د.ع</strong>
                                <br>
                                <small class="text-muted">{{ account.orders_count }} طلب</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">جميع الحسابات مسددة!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- الدفعات الحديثة -->
<div class="row">
    <div class="col-12">
        <div class="accounting-card">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2 text-success"></i>
                        الدفعات الحديثة
                    </h5>
                    <a href="{% url 'payments_list' %}" class="btn btn-sm btn-outline-success">
                        عرض جميع الدفعات
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                    <div class="row">
                        {% for payment in recent_payments %}
                        <div class="col-lg-6 mb-3">
                            <div class="recent-payment">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ payment.store_account.store.store_name }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            {{ payment.payment_date|date:"Y/m/d H:i" }} - 
                                            {{ payment.get_payment_method_display }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success">{{ payment.amount|floatformat:0 }} د.ع</strong>
                                        <br>
                                        <small class="text-muted">{{ payment.created_by.username }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد دفعات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="accounting-card">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'commission_settings_list' %}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <br>
                            إعدادات العمولات
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_periods_list' %}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <br>
                            الفترات المحاسبية
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'payments_list' %}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-credit-card fa-2x mb-2"></i>
                            <br>
                            إدارة الدفعات
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_reports' %}" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <br>
                            التقارير والإحصائيات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
