# Generated by Django 4.1.4 on 2025-07-09 15:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dashboard', '0006_alter_basketstest_all_price_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryPricing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('distance_from', models.IntegerField(help_text='بداية النطاق بالمتر', verbose_name='المسافة من (متر)')),
                ('distance_to', models.IntegerField(help_text='نهاية النطاق بالمتر', verbose_name='المسافة إلى (متر)')),
                ('price_per_delivery', models.DecimalField(decimal_places=2, help_text='سعر التوصيل بالريال اليمني لهذا النطاق', max_digits=10, verbose_name='سعر التوصيل')),
                ('description', models.CharField(blank=True, help_text='وصف اختياري للنطاق (مثل: قريب، متوسط، بعيد)', max_length=255, null=True, verbose_name='وصف النطاق')),
                ('is_active', models.BooleanField(default=True, help_text='هل هذا النطاق نشط للتوصيل؟', verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تسعير التوصيل',
                'verbose_name_plural': 'تسعير التوصيل',
                'ordering': ['distance_from'],
            },
        ),
        migrations.DeleteModel(
            name='DeliveryCost',
        ),
        migrations.AddConstraint(
            model_name='deliverypricing',
            constraint=models.CheckConstraint(check=models.Q(('distance_to__gt', models.F('distance_from'))), name='distance_to_greater_than_distance_from'),
        ),
    ]
