{% extends 'dashboard/base.html' %}

{% block title %}المنتجات - لوحة تحكم نظام توصيل الطلبات المتطور{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item">
    <a href="{% url 'products_list' %}">المنتجات</a>
</li>
<li class="breadcrumb-item active">قائمة المنتجات</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h1 class="display-5 mb-2 animate__animated animate__fadeInUp">
            <i class="fas fa-boxes me-3"></i>
            إدارة المنتجات المتطورة
        </h1>
        <p class="lead text-white-50 animate__animated animate__fadeInUp animate__delay-1s">
            عرض وإدارة جميع المنتجات في النظام مع أدوات بحث وتصفية متقدمة
        </p>
    </div>
    <div class="animate__animated animate__fadeInRight animate__delay-2s">
        <a href="{% url 'products_create' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>
            إضافة منتج جديد
        </a>
    </div>
</div>
{% endblock %}

{% block content %}

<!-- Advanced Search & Filter Panel -->
<div class="card mb-4" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>
                البحث والتصفية المتقدم
            </h5>
            <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                <i class="fas fa-filter me-1"></i>
                فلاتر متقدمة
            </button>
        </div>
    </div>
    <div class="card-body">
        <form method="get" id="searchForm">
            <!-- Basic Search Row -->
            <div class="row g-3 mb-3">
                <div class="col-lg-4">
                    <label class="form-label">
                        <i class="fas fa-search me-1"></i>
                        البحث السريع
                    </label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control"
                               placeholder="البحث في أسماء المنتجات..."
                               value="{{ search }}" id="searchInput">
                        <button class="btn btn-outline-primary" type="button" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-lg-3">
                    <label class="form-label">
                        <i class="fas fa-tags me-1"></i>
                        الفئة
                    </label>
                    <select name="category" class="form-select" onchange="this.form.submit()">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.categ_name }} ({{ category.get_products.count }})
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-3">
                    <label class="form-label">
                        <i class="fas fa-store me-1"></i>
                        المتجر
                    </label>
                    <select name="store" class="form-select" onchange="this.form.submit()">
                        <option value="">جميع المتاجر</option>
                        {% for store in stores %}
                            <option value="{{ store.id }}" {% if selected_store == store.id|stringformat:"s" %}selected{% endif %}>
                                {{ store.store_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters (Collapsible) -->
            <div class="collapse" id="advancedFilters">
                <div class="row g-3 p-3 glass rounded">
                    <div class="col-md-3">
                        <label class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>
                            نطاق السعر
                        </label>
                        <div class="row g-1">
                            <div class="col-6">
                                <input type="number" name="price_min" class="form-control form-control-sm" placeholder="من" value="{{ request.GET.price_min }}">
                            </div>
                            <div class="col-6">
                                <input type="number" name="price_max" class="form-control form-control-sm" placeholder="إلى" value="{{ request.GET.price_max }}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <i class="fas fa-toggle-on me-1"></i>
                            الحالة
                        </label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>مفعل</option>
                            <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>معطل</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <i class="fas fa-sort me-1"></i>
                            ترتيب حسب
                        </label>
                        <select name="sort" class="form-select form-select-sm">
                            <option value="">الافتراضي</option>
                            <option value="name" {% if request.GET.sort == 'name' %}selected{% endif %}>الاسم</option>
                            <option value="price" {% if request.GET.sort == 'price' %}selected{% endif %}>السعر</option>
                            <option value="views" {% if request.GET.sort == 'views' %}selected{% endif %}>المشاهدات</option>
                            <option value="date" {% if request.GET.sort == 'date' %}selected{% endif %}>التاريخ</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="view" id="gridView" value="grid" {% if request.GET.view != 'list' %}checked{% endif %}>
                            <label class="btn btn-outline-light btn-sm" for="gridView">
                                <i class="fas fa-th"></i>
                            </label>
                            <input type="radio" class="btn-check" name="view" id="listView" value="list" {% if request.GET.view == 'list' %}checked{% endif %}>
                            <label class="btn btn-outline-light btn-sm" for="listView">
                                <i class="fas fa-list"></i>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Display -->
<div class="card" data-aos="fade-up" data-aos-delay="300">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-boxes me-2"></i>
                قائمة المنتجات
                <span class="badge bg-primary ms-2">{{ products.count }}</span>
            </h5>
            <div class="d-flex align-items-center">
                <!-- View Toggle -->
                <div class="btn-group me-3" role="group">
                    <button type="button" class="btn btn-sm btn-outline-light active" id="gridViewBtn" onclick="toggleView('grid')">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-light" id="listViewBtn" onclick="toggleView('list')">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
                <!-- Bulk Actions -->
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>
                        إجراءات
                    </button>
                    <ul class="dropdown-menu glass">
                        <li><a class="dropdown-item" href="#" onclick="selectAll()"><i class="fas fa-check-square me-2"></i>تحديد الكل</a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportProducts()"><i class="fas fa-download me-2"></i>تصدير</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkDelete()"><i class="fas fa-trash me-2"></i>حذف المحدد</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if products %}
            <!-- Grid View -->
            <div id="gridView" class="products-grid">
                <div class="row g-4">
                    {% for product in products %}
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                        <div class="product-card card h-100 animate__animated animate__fadeInUp" style="animation-delay: {{ forloop.counter0|add:1 }}00ms;" data-aos="zoom-in" data-aos-delay="{{ forloop.counter0|add:1 }}00">
                            <!-- Product Image -->
                            <div class="product-image-container position-relative">
                                {% if product.product_picture %}
                                    <img src="{{ product.product_picture.url }}" alt="{{ product.product_name }}"
                                         class="card-img-top product-image" style="height: 200px; object-fit: cover;">
                                {% else %}
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                {% endif %}

                                <!-- Product Badges -->
                                <div class="product-badges position-absolute top-0 start-0 p-2">
                                    {% if product.is_static_in_main_screen %}
                                        <span class="badge bg-warning mb-1 d-block">مثبت</span>
                                    {% endif %}
                                    {% if product.enable %}
                                        <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                        <span class="badge bg-danger">معطل</span>
                                    {% endif %}
                                </div>

                                <!-- Quick Actions Overlay -->
                                <div class="product-overlay position-absolute top-0 end-0 p-2">
                                    <div class="btn-group-vertical" role="group">
                                        <a href="{% url 'products_detail' product.pk %}" class="btn btn-sm btn-light mb-1" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'products_edit' product.pk %}" class="btn btn-sm btn-primary mb-1" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger" onclick="deleteProduct({{ product.pk }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Product Info -->
                            <div class="card-body">
                                <h6 class="card-title mb-2">{{ product.product_name|truncatechars:25 }}</h6>
                                <div class="product-meta mb-2">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-store me-1"></i>
                                        {{ product.store.store_name|default:"غير محدد" }}
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-tags me-1"></i>
                                        {{ product.categ.categ_name|default:"غير محدد" }}
                                    </small>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="product-price">
                                        <h5 class="text-success mb-0">{{ product.price }} ريال يمني</h5>
                                    </div>
                                    <div class="product-stats">
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i>
                                            {{ product.views }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Product Footer -->
                            <div class="card-footer bg-transparent">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button class="btn btn-sm btn-outline-primary flex-fill" onclick="quickEdit({{ product.pk }})">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل سريع
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="duplicateProduct({{ product.pk }})" title="نسخ">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- List View (Hidden by default) -->
            <div id="listView" class="products-list d-none">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                </th>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>المتجر</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>المشاهدات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="animate__animated animate__fadeInUp" style="animation-delay: {{ forloop.counter0|add:1 }}00ms;">
                                <td>
                                    <input type="checkbox" class="form-check-input product-checkbox" value="{{ product.pk }}">
                                </td>
                                <td>
                                    {% if product.product_picture %}
                                        <img src="{{ product.product_picture.url }}" alt="{{ product.product_name }}"
                                             class="rounded" width="50" height="50" style="object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ product.product_name|truncatechars:30 }}</strong>
                                        {% if product.is_static_in_main_screen %}
                                            <span class="badge bg-warning ms-1">مثبت</span>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">{{ product.product_description|truncatechars:50|default:"لا يوجد وصف" }}</small>
                                    </div>
                                </td>
                                <td>{{ product.store.store_name|default:"غير محدد" }}</td>
                                <td>{{ product.categ.categ_name|default:"غير محدد" }}</td>
                                <td>
                                    <strong class="text-success">{{ product.price }} ريال يمني</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ product.views }}</span>
                                </td>
                                <td>
                                    {% if product.enable %}
                                        <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                        <span class="badge bg-danger">معطل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'products_detail' product.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'products_edit' product.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct({{ product.pk }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <small class="text-muted">عرض {{ products.count }} من إجمالي المنتجات</small>
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled">
                            <span class="page-link">السابق</span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">التالي</a>
                        </li>
                    </ul>
                </nav>
            </div>
        {% else %}
            <div class="text-center py-5">
                <div class="animate__animated animate__bounceIn">
                    <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات</h5>
                    <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
                    <a href="{% url 'products_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول منتج
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Product Cards */
    .product-card {
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        border: none;
        overflow: hidden;
        position: relative;
    }

    .product-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .product-image-container {
        overflow: hidden;
    }

    .product-image {
        transition: transform 0.4s ease;
    }

    .product-card:hover .product-image {
        transform: scale(1.1);
    }

    /* Product Overlay */
    .product-overlay {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    /* Product Badges */
    .product-badges .badge {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Enhanced Search Form */
    #searchForm {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border-radius: var(--border-radius);
        padding: 20px;
        border: 1px solid var(--glass-border);
    }

    /* View Toggle Buttons */
    .btn-check:checked + .btn-outline-light {
        background: var(--primary-gradient);
        border-color: transparent;
        color: white;
    }

    /* Enhanced Table */
    .table tbody tr:hover {
        background: var(--glass-bg);
        transform: scale(1.01);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    /* Product Grid Animation */
    .products-grid .product-card {
        animation-fill-mode: both;
    }

    /* Loading States */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        border-radius: var(--border-radius);
    }

    /* Enhanced Pagination */
    .pagination .page-link {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        color: rgba(255,255,255,0.9);
        backdrop-filter: blur(10px);
        margin: 0 2px;
        border-radius: 8px;
    }

    .pagination .page-link:hover {
        background: var(--primary-gradient);
        border-color: transparent;
        color: white;
        transform: translateY(-2px);
    }

    .pagination .page-item.active .page-link {
        background: var(--primary-gradient);
        border-color: transparent;
    }

    /* Search Input Enhancement */
    #searchInput {
        background: rgba(255,255,255,0.9);
        border: 2px solid var(--glass-border);
        transition: all 0.3s ease;
    }

    #searchInput:focus {
        background: white;
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: scale(1.02);
    }

    /* Dropdown Menu Enhancement */
    .dropdown-menu.glass {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-heavy);
    }

    .dropdown-item {
        color: rgba(255,255,255,0.9);
        transition: all 0.3s ease;
    }

    .dropdown-item:hover {
        background: var(--glass-bg);
        color: white;
        transform: translateX(-5px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .product-card {
            margin-bottom: 20px;
        }

        .product-overlay {
            opacity: 1;
            background: rgba(0,0,0,0.5);
        }

        #advancedFilters .row {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        initializeProductsPage();
        initializeSearch();
        initializeViewToggle();
    });

    // Initialize products page functionality
    function initializeProductsPage() {
        // Add loading states to forms
        document.getElementById('searchForm').addEventListener('submit', function() {
            showLoadingOverlay();
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize product card animations
        animateProductCards();
    }

    // Initialize search functionality
    function initializeSearch() {
        const searchInput = document.getElementById('searchInput');
        let searchTimeout;

        // Auto-search on typing (debounced)
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });

        // Search suggestions (mock implementation)
        searchInput.addEventListener('focus', function() {
            // Could implement search suggestions here
        });
    }

    // Initialize view toggle
    function initializeViewToggle() {
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');

        // Set initial view based on URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const currentView = urlParams.get('view') || 'grid';
        toggleView(currentView);
    }

    // Toggle between grid and list view
    function toggleView(view) {
        const gridView = document.getElementById('gridView');
        const listView = document.getElementById('listView');
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (view === 'list') {
            gridView.classList.add('d-none');
            listView.classList.remove('d-none');
            gridBtn.classList.remove('active');
            listBtn.classList.add('active');
        } else {
            gridView.classList.remove('d-none');
            listView.classList.add('d-none');
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        }

        // Update URL parameter
        const url = new URL(window.location);
        url.searchParams.set('view', view);
        window.history.replaceState({}, '', url);

        // Re-animate cards if switching to grid view
        if (view === 'grid') {
            setTimeout(animateProductCards, 100);
        }
    }

    // Animate product cards
    function animateProductCards() {
        const cards = document.querySelectorAll('.product-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = (index * 100) + 'ms';
            card.classList.add('animate__animated', 'animate__fadeInUp');
        });
    }

    // Clear search
    function clearSearch() {
        document.getElementById('searchInput').value = '';
        document.getElementById('searchForm').submit();
    }

    // Select all products
    function selectAll() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
        }

        updateBulkActionsVisibility();
    }

    // Toggle select all
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const checkboxes = document.querySelectorAll('.product-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        updateBulkActionsVisibility();
    }

    // Update bulk actions visibility
    function updateBulkActionsVisibility() {
        const selectedCount = document.querySelectorAll('.product-checkbox:checked').length;
        // Could show/hide bulk action buttons based on selection
    }

    // Delete product
    function deleteProduct(productId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'سيتم حذف هذا المنتج نهائياً',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء',
            background: 'var(--glass-bg)',
            backdrop: 'rgba(0,0,0,0.8)',
            customClass: {
                popup: 'glass'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Simulate deletion
                showLoadingOverlay();
                setTimeout(() => {
                    hideLoadingOverlay();
                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف المنتج بنجاح',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                    // Remove product card from DOM
                    const productCard = document.querySelector(`[onclick="deleteProduct(${productId})"]`).closest('.col-xl-3, tr');
                    if (productCard) {
                        productCard.style.animation = 'fadeOut 0.5s ease';
                        setTimeout(() => productCard.remove(), 500);
                    }
                }, 1000);
            }
        });
    }

    // Quick edit product
    function quickEdit(productId) {
        Swal.fire({
            title: 'تعديل سريع',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <label class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" id="quickEditName" placeholder="اسم المنتج">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">السعر</label>
                        <input type="number" class="form-control" id="quickEditPrice" placeholder="السعر">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="quickEditStatus">
                            <option value="1">مفعل</option>
                            <option value="0">معطل</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'حفظ',
            cancelButtonText: 'إلغاء',
            background: 'var(--glass-bg)',
            customClass: {
                popup: 'glass'
            },
            preConfirm: () => {
                const name = document.getElementById('quickEditName').value;
                const price = document.getElementById('quickEditPrice').value;
                const status = document.getElementById('quickEditStatus').value;

                if (!name || !price) {
                    Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }

                return { name, price, status };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showLoadingOverlay();
                setTimeout(() => {
                    hideLoadingOverlay();
                    Swal.fire({
                        title: 'تم الحفظ!',
                        text: 'تم تحديث المنتج بنجاح',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                }, 1000);
            }
        });
    }

    // Duplicate product
    function duplicateProduct(productId) {
        Swal.fire({
            title: 'نسخ المنتج',
            text: 'هل تريد إنشاء نسخة من هذا المنتج؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، انسخ',
            cancelButtonText: 'إلغاء',
            background: 'var(--glass-bg)',
            customClass: {
                popup: 'glass'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showLoadingOverlay();
                setTimeout(() => {
                    hideLoadingOverlay();
                    Swal.fire({
                        title: 'تم النسخ!',
                        text: 'تم إنشاء نسخة من المنتج بنجاح',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                }, 1000);
            }
        });
    }

    // Export products
    function exportProducts() {
        showLoadingOverlay();
        setTimeout(() => {
            hideLoadingOverlay();
            Swal.fire({
                title: 'تم التصدير!',
                text: 'تم تصدير قائمة المنتجات بنجاح',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }, 2000);
    }

    // Bulk delete
    function bulkDelete() {
        const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
        if (selectedProducts.length === 0) {
            Swal.fire({
                title: 'لا توجد منتجات محددة',
                text: 'يرجى تحديد منتج واحد على الأقل للحذف',
                icon: 'warning',
                confirmButtonText: 'موافق'
            });
            return;
        }

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: `سيتم حذف ${selectedProducts.length} منتج نهائياً`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف الكل',
            cancelButtonText: 'إلغاء',
            background: 'var(--glass-bg)',
            customClass: {
                popup: 'glass'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showLoadingOverlay();
                setTimeout(() => {
                    hideLoadingOverlay();
                    Swal.fire({
                        title: 'تم الحذف!',
                        text: `تم حذف ${selectedProducts.length} منتج بنجاح`,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                }, 1500);
            }
        });
    }

    // Show loading overlay
    function showLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="text-center text-white">
                <div class="spinner-border mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري المعالجة...</h5>
            </div>
        `;
        overlay.id = 'loadingOverlay';
        document.body.appendChild(overlay);
    }

    // Hide loading overlay
    function hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // Add CSS animation for fadeOut
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeOut {
            from { opacity: 1; transform: scale(1); }
            to { opacity: 0; transform: scale(0.8); }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}
