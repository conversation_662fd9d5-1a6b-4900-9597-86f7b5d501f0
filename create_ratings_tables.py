#!/usr/bin/env python
import os
import sys
import django
import sqlite3
from datetime import datetime

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'delivery_control.settings')
django.setup()

def create_ratings_tables():
    """إنشاء جداول التقييمات مباشرة في قاعدة البيانات"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    try:
        print("🔄 إنشاء جدول التقييمات...")
        
        # إنشاء جدول التقييمات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dashboard_rating (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rating_type VARCHAR(20) NOT NULL,
                rating_value INTEGER NOT NULL,
                comment TEXT,
                rated_delivery_person VARCHAR(255),
                is_public BOOLEAN NOT NULL DEFAULT 1,
                is_verified BOOLEAN NOT NULL DEFAULT 0,
                admin_response TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                order_id INTEGER,
                rated_customer_id INTEGER,
                rated_store_id INTEGER,
                reviewer_customer_id INTEGER,
                reviewer_store_id INTEGER,
                FOREIGN KEY (order_id) REFERENCES dashboard_order (id),
                FOREIGN KEY (rated_customer_id) REFERENCES dashboard_customer (id),
                FOREIGN KEY (rated_store_id) REFERENCES dashboard_stores (id),
                FOREIGN KEY (reviewer_customer_id) REFERENCES dashboard_customer (id),
                FOREIGN KEY (reviewer_store_id) REFERENCES dashboard_stores (id)
            )
        ''')
        
        print("✅ تم إنشاء جدول التقييمات بنجاح")
        
        print("🔄 إنشاء جدول إحصائيات التقييمات...")
        
        # إنشاء جدول إحصائيات التقييمات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dashboard_ratingstatistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                delivery_person_name VARCHAR(255) UNIQUE,
                total_ratings INTEGER NOT NULL DEFAULT 0,
                average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00,
                five_stars INTEGER NOT NULL DEFAULT 0,
                four_stars INTEGER NOT NULL DEFAULT 0,
                three_stars INTEGER NOT NULL DEFAULT 0,
                two_stars INTEGER NOT NULL DEFAULT 0,
                one_star INTEGER NOT NULL DEFAULT 0,
                last_updated DATETIME NOT NULL,
                customer_id INTEGER UNIQUE,
                store_id INTEGER UNIQUE,
                FOREIGN KEY (customer_id) REFERENCES dashboard_customer (id),
                FOREIGN KEY (store_id) REFERENCES dashboard_stores (id)
            )
        ''')
        
        print("✅ تم إنشاء جدول إحصائيات التقييمات بنجاح")
        
        print("🔄 تحديث جدول الهجرات...")
        
        # إضافة سجل الهجرة
        cursor.execute('''
            INSERT OR IGNORE INTO django_migrations (app, name, applied) 
            VALUES ('dashboard', '0008_rating_ratingstatistics', ?)
        ''', (datetime.now(),))
        
        print("✅ تم تحديث جدول الهجرات بنجاح")
        
        # حفظ التغييرات
        conn.commit()
        print("🎉 تم إنشاء جميع الجداول بنجاح!")
        
        # التحقق من الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dashboard_rating%'")
        tables = cursor.fetchall()
        print(f"📋 الجداول المُنشأة: {[table[0] for table in tables]}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    create_ratings_tables()
