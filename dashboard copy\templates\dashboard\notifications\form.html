{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <p class="text-muted">إدارة الإشعارات</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'notifications_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.store.id_for_label }}" class="form-label">
                            المتجر <span class="text-danger">*</span>
                        </label>
                        {{ form.store }}
                        {% if form.store.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.store.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notifications_text.id_for_label }}" class="form-label">
                            نص الإشعار <span class="text-danger">*</span>
                        </label>
                        {{ form.notifications_text }}
                        {% if form.notifications_text.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notifications_text.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.from_date.id_for_label }}" class="form-label">
                                    من تاريخ <span class="text-danger">*</span>
                                </label>
                                {{ form.from_date }}
                                {% if form.from_date.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.from_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.to_date.id_for_label }}" class="form-label">
                                    إلى تاريخ <span class="text-danger">*</span>
                                </label>
                                {{ form.to_date }}
                                {% if form.to_date.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.to_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">الوصف</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.products.id_for_label }}" class="form-label">المنتجات المرتبطة</label>
                        {{ form.products }}
                        {% if form.products.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.products.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">أدخل أسماء المنتجات المرتبطة بالإشعار (اختياري)</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'notifications_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإشعار
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
