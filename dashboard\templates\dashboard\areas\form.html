{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <p class="text-muted">إدارة بيانات المناطق</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'areas_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.area_name.id_for_label }}" class="form-label">
                            اسم المنطقة <span class="text-danger">*</span>
                        </label>
                        {{ form.area_name }}
                        {% if form.area_name.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.area_name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'areas_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
