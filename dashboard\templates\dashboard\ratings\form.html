{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .rating-preview {
        font-size: 2rem;
        color: #f6c23e;
        margin: 1rem 0;
    }
    .rating-input {
        display: none;
    }
    .rating-label {
        cursor: pointer;
        color: #ddd;
        font-size: 2rem;
        transition: color 0.3s;
    }
    .rating-label:hover,
    .rating-label.active {
        color: #f6c23e;
    }
    .form-section {
        background: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-star text-warning"></i>
            {{ title }}
        </h1>
        <a href="{% url 'ratings_list' %}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- نموذج التقييم -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        بيانات التقييم
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" id="ratingForm">
                        {% csrf_token %}
                        
                        <!-- نوع التقييم -->
                        <div class="form-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-tag"></i>
                                نوع التقييم
                            </h6>
                            <div class="form-group">
                                {{ form.rating_type.label_tag }}
                                {{ form.rating_type }}
                                {% if form.rating_type.errors %}
                                    <div class="text-danger small">{{ form.rating_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- التقييم بالنجوم -->
                        <div class="form-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-star"></i>
                                التقييم
                            </h6>
                            <div class="form-group">
                                <label>اختر التقييم:</label>
                                <div class="rating-stars-input">
                                    {% for value, label in form.rating_value.field.choices %}
                                        <input type="radio" name="rating_value" value="{{ value }}" id="rating_{{ value }}" class="rating-input" {% if form.rating_value.value == value %}checked{% endif %}>
                                        <label for="rating_{{ value }}" class="rating-label" data-rating="{{ value }}">
                                            <i class="fas fa-star"></i>
                                        </label>
                                    {% endfor %}
                                </div>
                                <div class="rating-preview" id="ratingPreview">
                                    <span id="ratingText">اختر التقييم</span>
                                </div>
                                {% if form.rating_value.errors %}
                                    <div class="text-danger small">{{ form.rating_value.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- المُقيَّم -->
                        <div class="form-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user-check"></i>
                                المُقيَّم
                            </h6>
                            
                            <!-- متجر -->
                            <div class="form-group" id="storeGroup" style="display: none;">
                                {{ form.rated_store.label_tag }}
                                {{ form.rated_store }}
                                {% if form.rated_store.errors %}
                                    <div class="text-danger small">{{ form.rated_store.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- عميل -->
                            <div class="form-group" id="customerGroup" style="display: none;">
                                {{ form.rated_customer.label_tag }}
                                {{ form.rated_customer }}
                                {% if form.rated_customer.errors %}
                                    <div class="text-danger small">{{ form.rated_customer.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- عامل توصيل -->
                            <div class="form-group" id="deliveryGroup" style="display: none;">
                                {{ form.rated_delivery_person.label_tag }}
                                {{ form.rated_delivery_person }}
                                {% if form.rated_delivery_person.errors %}
                                    <div class="text-danger small">{{ form.rated_delivery_person.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- التعليق -->
                        <div class="form-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-comment"></i>
                                التعليق
                            </h6>
                            <div class="form-group">
                                {{ form.comment.label_tag }}
                                {{ form.comment }}
                                {% if form.comment.errors %}
                                    <div class="text-danger small">{{ form.comment.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cog"></i>
                                معلومات إضافية
                            </h6>
                            
                            <div class="form-group">
                                {{ form.order.label_tag }}
                                {{ form.order }}
                                {% if form.order.errors %}
                                    <div class="text-danger small">{{ form.order.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="form-check">
                                {{ form.is_public }}
                                {{ form.is_public.label_tag }}
                                {% if form.is_public.errors %}
                                    <div class="text-danger small">{{ form.is_public.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i>
                                حفظ التقييم
                            </button>
                            <a href="{% url 'ratings_list' %}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- معلومات مساعدة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        معلومات مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">أنواع التقييمات:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-store text-primary"></i> <strong>تقييم متجر:</strong> تقييم أداء المتجر</li>
                            <li><i class="fas fa-user text-success"></i> <strong>تقييم عميل:</strong> تقييم سلوك العميل</li>
                            <li><i class="fas fa-motorcycle text-warning"></i> <strong>تقييم توصيل:</strong> تقييم عامل التوصيل</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary">مقياس التقييم:</h6>
                        <ul class="list-unstyled">
                            <li><span class="text-warning">⭐</span> سيء جداً</li>
                            <li><span class="text-warning">⭐⭐</span> سيء</li>
                            <li><span class="text-warning">⭐⭐⭐</span> متوسط</li>
                            <li><span class="text-warning">⭐⭐⭐⭐</span> جيد</li>
                            <li><span class="text-warning">⭐⭐⭐⭐⭐</span> ممتاز</li>
                        </ul>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb"></i>
                        <strong>نصيحة:</strong> اكتب تعليقاً مفصلاً لمساعدة الآخرين على فهم سبب التقييم.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث عرض النجوم
    function updateStarDisplay() {
        const selectedRating = $('input[name="rating_value"]:checked').val();
        const ratingTexts = {
            '1': 'سيء جداً ⭐',
            '2': 'سيء ⭐⭐',
            '3': 'متوسط ⭐⭐⭐',
            '4': 'جيد ⭐⭐⭐⭐',
            '5': 'ممتاز ⭐⭐⭐⭐⭐'
        };
        
        $('.rating-label').removeClass('active');
        if (selectedRating) {
            for (let i = 1; i <= selectedRating; i++) {
                $(`label[data-rating="${i}"]`).addClass('active');
            }
            $('#ratingText').text(ratingTexts[selectedRating] || 'اختر التقييم');
        }
    }

    // تفاعل النجوم
    $('.rating-label').click(function() {
        const rating = $(this).data('rating');
        $(`#rating_${rating}`).prop('checked', true);
        updateStarDisplay();
    });

    // تحديث الحقول حسب نوع التقييم
    function updateRatedFields() {
        const ratingType = $('#id_rating_type').val();
        
        $('#storeGroup, #customerGroup, #deliveryGroup').hide();
        
        if (ratingType === 'store') {
            $('#storeGroup').show();
        } else if (ratingType === 'customer') {
            $('#customerGroup').show();
        } else if (ratingType === 'delivery') {
            $('#deliveryGroup').show();
        }
    }

    // تحديث عند تغيير نوع التقييم
    $('#id_rating_type').change(updateRatedFields);

    // تحديث أولي
    updateStarDisplay();
    updateRatedFields();

    // تحقق من صحة النموذج
    $('#ratingForm').submit(function(e) {
        const ratingType = $('#id_rating_type').val();
        const ratingValue = $('input[name="rating_value"]:checked').val();
        
        if (!ratingType) {
            alert('يرجى اختيار نوع التقييم');
            e.preventDefault();
            return false;
        }
        
        if (!ratingValue) {
            alert('يرجى اختيار التقييم بالنجوم');
            e.preventDefault();
            return false;
        }
        
        // التحقق من اختيار المُقيَّم
        if (ratingType === 'store' && !$('#id_rated_store').val()) {
            alert('يرجى اختيار المتجر المُقيَّم');
            e.preventDefault();
            return false;
        }
        
        if (ratingType === 'customer' && !$('#id_rated_customer').val()) {
            alert('يرجى اختيار العميل المُقيَّم');
            e.preventDefault();
            return false;
        }
        
        if (ratingType === 'delivery' && !$('#id_rated_delivery_person').val().trim()) {
            alert('يرجى إدخال اسم عامل التوصيل المُقيَّم');
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
