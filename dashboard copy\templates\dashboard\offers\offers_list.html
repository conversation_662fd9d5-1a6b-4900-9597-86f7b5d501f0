{% extends 'dashboard/base.html' %}

{% block title %}العروض الخاصة - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">العروض الخاصة</li>
{% endblock %}

{% block extra_css %}
<style>
.offers-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.offer-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.offer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.offer-header {
    padding: 1.5rem;
    position: relative;
}

.offer-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-draft { background: #e2e3e5; color: #383d41; }
.status-paused { background: #fff3cd; color: #856404; }
.status-expired { background: #f8d7da; color: #721c24; }
.status-cancelled { background: #f5c6cb; color: #721c24; }

.offer-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.type-discount_percentage { background: #cce5ff; color: #004085; }
.type-discount_fixed { background: #d4edda; color: #155724; }
.type-buy_get { background: #fff3cd; color: #856404; }
.type-free_delivery { background: #e2e3e5; color: #383d41; }
.type-bundle { background: #f8d7da; color: #721c24; }
.type-seasonal { background: #d1ecf1; color: #0c5460; }

.featured-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
}

.stat-card.total { border-left-color: #007bff; }
.stat-card.active { border-left-color: #28a745; }
.stat-card.featured { border-left-color: #ffc107; }

.filter-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #007bff;
}

.create-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.create-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الصفحة -->
<div class="offers-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">
                <i class="fas fa-tags me-2"></i>
                العروض الخاصة
            </h2>
            <p class="mb-0 opacity-75">إدارة وتنظيم العروض الخاصة لأصحاب المتاجر والعملاء</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'special_offer_create' %}" class="create-btn">
                <i class="fas fa-plus me-2"></i>
                إنشاء عرض جديد
            </a>
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<div class="stats-grid">
    <div class="stat-card total">
        <h3 class="text-primary mb-0">{{ total_offers }}</h3>
        <p class="text-muted mb-0">إجمالي العروض</p>
    </div>
    <div class="stat-card active">
        <h3 class="text-success mb-0">{{ active_offers }}</h3>
        <p class="text-muted mb-0">عروض نشطة</p>
    </div>
    <div class="stat-card featured">
        <h3 class="text-warning mb-0">{{ featured_offers }}</h3>
        <p class="text-muted mb-0">عروض مميزة</p>
    </div>
</div>

<!-- فلترة -->
<div class="filter-card">
    <h6 class="mb-3">
        <i class="fas fa-filter me-2"></i>
        فلترة العروض
    </h6>
    <form method="get" class="row align-items-end">
        <div class="col-md-2 mb-3">
            {{ filter_form.status.label_tag }}
            {{ filter_form.status }}
        </div>
        <div class="col-md-2 mb-3">
            {{ filter_form.offer_type.label_tag }}
            {{ filter_form.offer_type }}
        </div>
        <div class="col-md-2 mb-3">
            {{ filter_form.store.label_tag }}
            {{ filter_form.store }}
        </div>
        <div class="col-md-3 mb-3">
            {{ filter_form.search.label_tag }}
            {{ filter_form.search }}
        </div>
        <div class="col-md-2 mb-3">
            <div class="form-check">
                {{ filter_form.is_featured }}
                {{ filter_form.is_featured.label_tag }}
            </div>
        </div>
        <div class="col-md-1 mb-3">
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </form>
</div>

<!-- قائمة العروض -->
<div class="row">
    {% if offers %}
        {% for offer in offers %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="offer-card">
                <div class="offer-header">
                    <span class="offer-status status-{{ offer.status }}">
                        {{ offer.get_status_display }}
                    </span>
                    
                    {% if offer.is_featured %}
                    <span class="featured-badge position-absolute" style="top: 1rem; right: 1rem;">
                        <i class="fas fa-star me-1"></i>
                        مميز
                    </span>
                    {% endif %}
                    
                    <div class="mt-4">
                        <h5 class="mb-2">{{ offer.title }}</h5>
                        <p class="text-muted mb-3">{{ offer.description|truncatechars:100 }}</p>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="offer-type-badge type-{{ offer.offer_type }}">
                                {{ offer.get_offer_type_display }}
                            </span>
                            <div class="text-end">
                                {% if offer.offer_type == 'discount_percentage' %}
                                    <strong class="text-success">{{ offer.discount_percentage }}%</strong>
                                {% elif offer.offer_type == 'discount_fixed' %}
                                    <strong class="text-success">{{ offer.discount_amount|floatformat:0 }} د.ع</strong>
                                {% elif offer.offer_type == 'buy_get' %}
                                    <strong class="text-info">{{ offer.buy_quantity }}+{{ offer.get_quantity }}</strong>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">الاستخدامات</small>
                                <br>
                                <strong>{{ offer.current_usage_count }}</strong>
                                {% if offer.total_usage_limit > 0 %}
                                    / {{ offer.total_usage_limit }}
                                {% endif %}
                            </div>
                            <div class="col-6">
                                <small class="text-muted">ينتهي في</small>
                                <br>
                                <strong>{{ offer.end_date|timeuntil }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group btn-group-sm">
                            <a href="{% url 'special_offer_detail' offer.pk %}" 
                               class="btn btn-outline-primary" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'special_offer_edit' offer.pk %}" 
                               class="btn btn-outline-success" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'special_offer_delete' offer.pk %}" 
                               class="btn btn-outline-danger" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                        
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ offer.created_date|date:"Y/m/d" }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-tags fa-4x mb-3"></i>
                        <h5>لا توجد عروض خاصة</h5>
                        <p class="mb-4">ابدأ بإنشاء عروض خاصة جذابة لعملائك</p>
                        <a href="{% url 'special_offer_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء أول عرض
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- معلومات إضافية -->
{% if offers %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    أنواع العروض
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="offer-type-badge type-discount_percentage me-2">خصم نسبة</span>
                        نسبة مئوية من قيمة الطلب
                    </li>
                    <li class="mb-2">
                        <span class="offer-type-badge type-discount_fixed me-2">خصم ثابت</span>
                        مبلغ ثابت من قيمة الطلب
                    </li>
                    <li class="mb-2">
                        <span class="offer-type-badge type-buy_get me-2">اشتري واحصل</span>
                        عرض كمية مجانية
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح للعروض الناجحة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد فترة زمنية واضحة للعرض
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم عناوين جذابة ووصف واضح
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        راقب أداء العروض وعدّلها حسب الحاجة
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
