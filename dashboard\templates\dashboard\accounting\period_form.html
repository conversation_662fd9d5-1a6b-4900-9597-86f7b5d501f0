{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounting_periods_list' %}">الفترات المحاسبية</a></li>
<li class="breadcrumb-item active">إنشاء فترة جديدة</li>
{% endblock %}

{% block extra_css %}
<style>
.period-form-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.form-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.form-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.period-type-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.period-type-card:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.period-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.period-type-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.date-preview {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.btn-create {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-create:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.help-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="period-form-card">
            <!-- رأس النموذج -->
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-calendar-plus"></i>
                </div>
                <h3 class="mb-0">{{ title }}</h3>
                <p class="mb-0 mt-2 opacity-75">إنشاء فترة محاسبية جديدة لحساب مستحقات المتاجر</p>
            </div>

            <!-- النموذج -->
            <div class="card-body p-4">
                <form method="post" id="periodForm">
                    {% csrf_token %}
                    
                    <!-- معلومات أساسية -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-info-circle me-2"></i>
                            المعلومات الأساسية
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">اسم الفترة</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors }}</div>
                                {% endif %}
                                <div class="form-text">مثال: يناير 2024، الربع الأول 2024</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.period_type.id_for_label }}" class="form-label">نوع الفترة</label>
                                {{ form.period_type }}
                                {% if form.period_type.errors %}
                                    <div class="text-danger small">{{ form.period_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- التواريخ -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-calendar-alt me-2"></i>
                            فترة المحاسبة
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small">{{ form.start_date.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small">{{ form.end_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معاينة المدة -->
                        <div class="date-preview" id="datePreview" style="display: none;">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <strong>مدة الفترة:</strong>
                                    <br><span id="periodDuration">-</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>عدد الأيام:</strong>
                                    <br><span id="periodDays">-</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>الحالة:</strong>
                                    <br><span class="badge bg-success">صالحة</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات إضافية
                        </h6>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small">{{ form.notes.errors }}</div>
                            {% endif %}
                            <div class="form-text">أضف أي ملاحظات أو تفاصيل إضافية عن هذه الفترة</div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'accounting_periods_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="previewPeriod()">
                                <i class="fas fa-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-create">
                                <i class="fas fa-save me-2"></i>
                                إنشاء الفترة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قسم المساعدة -->
        <div class="help-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>
                نصائح مهمة
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من عدم تداخل الفترات مع بعضها
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            استخدم أسماء واضحة ومميزة للفترات
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            يمكن تعديل الفترة قبل إغلاقها
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-info text-primary me-2"></i>
                            الفترة الشهرية: من 1 إلى آخر يوم في الشهر
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info text-primary me-2"></i>
                            الفترة الربعية: 3 أشهر متتالية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info text-primary me-2"></i>
                            يمكن حساب المستحقات بعد إنشاء الفترة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateInput = document.getElementById('{{ form.end_date.id_for_label }}');
    const periodTypeSelect = document.getElementById('{{ form.period_type.id_for_label }}');
    const nameInput = document.getElementById('{{ form.name.id_for_label }}');
    
    // تحديث معاينة التواريخ
    function updateDatePreview() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        
        if (startDate && endDate && startDate < endDate) {
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            document.getElementById('periodDuration').textContent = 
                startDate.toLocaleDateString('ar-EG') + ' - ' + endDate.toLocaleDateString('ar-EG');
            document.getElementById('periodDays').textContent = diffDays + ' يوم';
            document.getElementById('datePreview').style.display = 'block';
        } else {
            document.getElementById('datePreview').style.display = 'none';
        }
    }
    
    // تحديث اسم الفترة تلقائياً
    function updatePeriodName() {
        const periodType = periodTypeSelect.value;
        const startDate = new Date(startDateInput.value);
        
        if (startDate && periodType && !nameInput.value) {
            let suggestedName = '';
            const year = startDate.getFullYear();
            const month = startDate.getMonth() + 1;
            
            switch(periodType) {
                case 'weekly':
                    suggestedName = `الأسبوع ${Math.ceil(startDate.getDate() / 7)} - ${getMonthName(month)} ${year}`;
                    break;
                case 'monthly':
                    suggestedName = `${getMonthName(month)} ${year}`;
                    break;
                case 'quarterly':
                    const quarter = Math.ceil(month / 3);
                    suggestedName = `الربع ${quarter} - ${year}`;
                    break;
                case 'custom':
                    suggestedName = `فترة مخصصة ${year}`;
                    break;
            }
            
            nameInput.value = suggestedName;
        }
    }
    
    function getMonthName(month) {
        const months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return months[month - 1];
    }
    
    // ربط الأحداث
    startDateInput.addEventListener('change', function() {
        updateDatePreview();
        updatePeriodName();
    });
    
    endDateInput.addEventListener('change', updateDatePreview);
    periodTypeSelect.addEventListener('change', updatePeriodName);
    
    // معاينة الفترة
    window.previewPeriod = function() {
        const formData = new FormData(document.getElementById('periodForm'));
        let preview = 'معاينة الفترة المحاسبية:\n\n';
        preview += `الاسم: ${formData.get('name')}\n`;
        preview += `النوع: ${periodTypeSelect.options[periodTypeSelect.selectedIndex].text}\n`;
        preview += `من: ${formData.get('start_date')}\n`;
        preview += `إلى: ${formData.get('end_date')}\n`;
        if (formData.get('notes')) {
            preview += `الملاحظات: ${formData.get('notes')}\n`;
        }
        
        alert(preview);
    };
    
    // تحقق من صحة النموذج
    document.getElementById('periodForm').addEventListener('submit', function(e) {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        
        if (startDate >= endDate) {
            e.preventDefault();
            alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            return false;
        }
        
        if (!confirm('هل أنت متأكد من إنشاء هذه الفترة المحاسبية؟')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
