{% extends 'dashboard/base.html' %}

{% block title %}الفئات - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة الفئات</h1>
        <p class="text-muted">عرض وإدارة جميع فئات المنتجات في النظام</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="{% url 'categories_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة فئة جديدة
            </a>
            <button type="button"
                    class="btn btn-success bulk-add-btn"
                    data-entity="categories"
                    data-url="{% url 'bulk_add_categories' %}"
                    data-fields='[{"name": "categ_name", "label": "اسم الفئة", "type": "text", "required": true, "placeholder": "أدخل اسم الفئة"}, {"name": "note", "label": "ملاحظة", "type": "textarea", "required": false, "placeholder": "ملاحظة اختيارية"}]'>
                <i class="fas fa-layer-group me-2"></i>
                إضافة متعددة
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-tags me-2"></i>
            قائمة الفئات ({{ categories.count }})
        </h5>
    </div>
    <div class="card-body">
        {% if categories %}
            <div class="row">
                {% for category in categories %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        {% if category.categ_picture %}
                            <img src="{{ category.categ_picture.url }}" class="card-img-top" alt="{{ category.categ_name }}" 
                                 style="height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title">{{ category.categ_name }}</h5>
                            {% if category.note %}
                                <p class="card-text text-muted">{{ category.note|truncatechars:100 }}</p>
                            {% endif %}
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-box me-1"></i>
                                    {{ category.products_count|default:0 }} منتج
                                </small>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'categories_edit' category.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'products_list' %}?category={{ category.id }}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'categories_delete' category.pk %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فئات</h5>
                <p class="text-muted">لم يتم إضافة أي فئات حتى الآن</p>
                <a href="{% url 'categories_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول فئة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
