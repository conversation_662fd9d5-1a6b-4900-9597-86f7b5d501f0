{% extends 'dashboard/base.html' %}

{% block title %}العملاء - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة العملاء</h1>
        <p class="text-muted">عرض وإدارة جميع العملاء في النظام</p>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>
            قائمة العملاء ({{ customers.count }})
        </h5>
        <div class="btn-group">
            <a href="#" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة عميل جديد
            </a>
            <button type="button"
                    class="btn btn-success bulk-add-btn"
                    data-entity="customers"
                    data-url="{% url 'bulk_add_customers' %}"
                    data-fields='[{"name": "name", "label": "اسم العميل", "type": "text", "required": true, "placeholder": "أدخل اسم العميل"}, {"name": "phone_number", "label": "رقم الهاتف", "type": "tel", "required": true, "placeholder": "أدخل رقم الهاتف"}, {"name": "place", "label": "العنوان", "type": "text", "required": false, "placeholder": "أدخل العنوان"}]'>
                <i class="fas fa-layer-group me-2"></i>
                إضافة متعددة
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if customers %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>المكان</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td><strong>#{{ customer.id }}</strong></td>
                            <td>{{ customer.name }}</td>
                            <td>
                                <a href="tel:{{ customer.phone_number }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ customer.phone_number }}
                                </a>
                            </td>
                            <td>{{ customer.place }}</td>
                            <td>{{ customer.notes|default:"لا توجد ملاحظات"|truncatechars:30 }}</td>
                            <td>
                                <a href="{% url 'customers_detail' customer.pk %}" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                    عرض التفاصيل
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد عملاء</h5>
                <p class="text-muted">لم يتم تسجيل أي عملاء حتى الآن</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
