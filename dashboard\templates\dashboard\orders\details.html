{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تفاصيل الطلب #{{ order.order_id }}{% endblock %}

{% block extra_css %}
<style>
.order-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.info-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #5a5c69;
}

.info-value {
    color: #858796;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1rem;
}

.status-new { background: #e3f2fd; color: #1976d2; }
.status-pending { background: #fff3e0; color: #f57c00; }
.status-accepted { background: #e8f5e8; color: #388e3c; }
.status-preparing { background: #fff8e1; color: #f9a825; }
.status-ready { background: #e1f5fe; color: #0288d1; }
.status-out_for_delivery { background: #f3e5f5; color: #7b1fa2; }
.status-delivered { background: #e8f5e8; color: #2e7d32; }
.status-cancelled { background: #ffebee; color: #d32f2f; }
.status-rejected { background: #fce4ec; color: #c2185b; }

.item-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -2rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
}

.timeline-item:after {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1.25rem;
    width: 2px;
    height: calc(100% - 0.75rem);
    background: #e3e6f0;
}

.timeline-item:last-child:after {
    display: none;
}

.timeline-item.completed:before {
    background: #28a745;
}

.timeline-item.current:before {
    background: #ffc107;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.print-btn {
    background: #6f42c1;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.print-btn:hover {
    background: #5a2d91;
    transform: translateY(-1px);
}

/* تحسينات عرض المنتجات */
.item-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fc;
}

.item-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
    background: white;
}

.item-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 0.25rem;
    border: 1px solid #e3e6f0;
}

.item-placeholder {
    width: 50px;
    height: 50px;
    background: #e3e6f0;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.item-name {
    color: #2c3e50;
    font-weight: 600;
}

.item-description {
    display: block;
    margin-top: 0.25rem;
    line-height: 1.4;
}

.quantity-badge {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
}

.unit-price {
    color: #007bff;
    font-size: 1.1rem;
}

.total-price {
    font-size: 1.2rem;
    font-weight: 700;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f1f1;
}

.info-row:last-child {
    border-bottom: none;
}

.order-summary-details {
    background: #f8f9fc;
    padding: 1rem;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
}

.timeline-content {
    padding: 0.5rem 0;
}

.timeline-title {
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.timeline-time {
    display: block;
    margin-bottom: 0.25rem;
}

.timeline-message {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0.25rem 0;
    font-style: italic;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-receipt text-primary"></i>
            تفاصيل الطلب
        </h1>
        <div>
            <a href="{% url 'orders_dashboard' %}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i>
                العودة للوحة الرئيسية
            </a>
            <a href="{% url 'orders_history' %}" class="btn btn-info btn-sm ml-2">
                <i class="fas fa-history"></i>
                تاريخ الطلبات
            </a>
            <button class="print-btn btn-sm ml-2" onclick="window.print()">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>

    <!-- رأس الطلب -->
    <div class="order-header">
        <div class="row">
            <div class="col-md-8">
                <h2 class="mb-2">
                    <i class="fas fa-store"></i>
                    {{ order.store_name }}
                </h2>
                <h4 class="mb-3">طلب #{{ order.order_id }}</h4>
                <p class="mb-0">
                    <i class="fas fa-calendar"></i>
                    تاريخ الطلب: {{ order.createdAt_formatted }}
                </p>
            </div>
            <div class="col-md-4 text-md-right">
                <div class="mb-3">
                    <span class="status-badge status-{{ order.status }}">
                        {{ order.status_arabic }}
                    </span>
                </div>
                <h3 class="mb-0">{{ order.storeSubtotal }} ريال يمني</h3>
                <small>{{ order.payment_method_arabic }}</small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات العميل -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-user"></i>
                    معلومات العميل
                </h5>
                
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value">{{ order.customerName }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">{{ order.customerPhone }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">معرف العميل:</span>
                    <span class="info-value">{{ order.customerId }}</span>
                </div>
            </div>
        </div>

        <!-- معلومات التوصيل -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5 class="text-success mb-3">
                    <i class="fas fa-map-marker-alt"></i>
                    معلومات التوصيل
                </h5>
                
                <div class="info-row">
                    <span class="info-label">العنوان الكامل:</span>
                    <span class="info-value">{{ order.full_address_formatted }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">المنطقة:</span>
                    <span class="info-value">{{ order.delivery_area }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">المدينة:</span>
                    <span class="info-value">{{ order.deliveryAddress.city }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">وقت التوصيل:</span>
                    <span class="info-value">
                        {% if order.deliveryTime == 'asap' %}
                            في أسرع وقت ممكن
                        {% else %}
                            {{ order.deliveryTime }}
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الطلب -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shopping-cart"></i>
                        تفاصيل الطلب ({{ order.items|length }} منتج)
                    </h6>
                </div>
                <div class="card-body">
                    {% for item in order.items %}
                    <div class="item-card">
                        <div class="row align-items-center">
                            <div class="col-md-1 text-center">
                                {% if item.imageUrl %}
                                <img src="{{ item.imageUrl }}" alt="{{ item.name }}" class="item-image">
                                {% else %}
                                <div class="item-placeholder">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-5">
                                <h6 class="mb-1 item-name">{{ item.name }}</h6>
                                {% if item.description %}
                                <small class="text-muted item-description">{{ item.description }}</small>
                                {% endif %}
                                {% if item.category %}
                                <div class="mt-1">
                                    <span class="badge badge-secondary">
                                        <i class="fas fa-tag"></i>
                                        {{ item.category }}
                                    </span>
                                </div>
                                {% endif %}
                                {% if item.notes %}
                                <div class="mt-1">
                                    <small class="text-info">
                                        <i class="fas fa-sticky-note"></i>
                                        {{ item.notes }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-2 text-center">
                                <div class="quantity-display">
                                    <span class="badge badge-primary quantity-badge">{{ item.quantity }}</span>
                                </div>
                                <small class="text-muted">الكمية</small>
                            </div>
                            <div class="col-md-2 text-center">
                                <strong class="unit-price">{{ item.price }} ريال</strong>
                                <br>
                                <small class="text-muted">سعر الوحدة</small>
                            </div>
                            <div class="col-md-2 text-center">
                                <strong class="text-success total-price">
                                    {% widthratio item.price 1 item.quantity %} ريال
                                </strong>
                                <br>
                                <small class="text-muted">الإجمالي</small>
                            </div>
                        </div>

                        <!-- معلومات إضافية عن المنتج -->
                        {% if item.storeId and item.storeName %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-store"></i>
                                    من متجر: {{ item.storeName }} ({{ item.storeId }})
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                    
                    <!-- ملخص الطلب -->
                    <div class="border-top pt-3 mt-3">
                        <div class="row">
                            <div class="col-md-8"></div>
                            <div class="col-md-4">
                                <div class="order-summary-details">
                                    {% if order.subtotal %}
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المبلغ الفرعي:</span>
                                        <span>{{ order.subtotal }} ريال</span>
                                    </div>
                                    {% endif %}

                                    {% if order.tax and order.tax > 0 %}
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الضريبة:</span>
                                        <span>{{ order.tax }} ريال</span>
                                    </div>
                                    {% endif %}

                                    {% if order.deliveryFee and order.deliveryFee > 0 %}
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>رسوم التوصيل:</span>
                                        <span>{{ order.deliveryFee }} ريال</span>
                                    </div>
                                    {% endif %}

                                    {% if order.discount and order.discount > 0 %}
                                    <div class="d-flex justify-content-between mb-2 text-success">
                                        <span>الخصم:</span>
                                        <span>-{{ order.discount }} ريال</span>
                                    </div>
                                    {% endif %}

                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <strong>الإجمالي النهائي:</strong>
                                        <strong class="text-success h5">{{ order.storeSubtotal }} ريال</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <!-- معلومات التوصيل -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-truck"></i>
                        معلومات التوصيل
                    </h6>
                </div>
                <div class="card-body">
                    {% if order.estimatedDeliveryMinutes %}
                    <div class="info-row">
                        <strong>وقت التوصيل المتوقع:</strong>
                        <span class="text-warning">{{ order.estimatedDeliveryMinutes }} دقيقة</span>
                    </div>
                    {% endif %}

                    {% if order.deliveryTime %}
                    <div class="info-row">
                        <strong>نوع التوصيل:</strong>
                        <span>
                            {% if order.deliveryTime == 'asap' %}
                                في أسرع وقت
                            {% else %}
                                {{ order.deliveryTime }}
                            {% endif %}
                        </span>
                    </div>
                    {% endif %}

                    {% if order.scheduledTime %}
                    <div class="info-row">
                        <strong>وقت التوصيل المجدول:</strong>
                        <span>{{ order.scheduledTime }}</span>
                    </div>
                    {% endif %}

                    {% if order.deliveryPersonName %}
                    <div class="info-row">
                        <strong>عامل التوصيل:</strong>
                        <span>{{ order.deliveryPersonName }}</span>
                    </div>
                    {% endif %}

                    {% if order.deliveryPersonPhone %}
                    <div class="info-row">
                        <strong>هاتف عامل التوصيل:</strong>
                        <span>{{ order.deliveryPersonPhone }}</span>
                    </div>
                    {% endif %}

                    {% if order.notes %}
                    <div class="info-row">
                        <strong>ملاحظات:</strong>
                        <span class="text-muted">{{ order.notes }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات المنصة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-mobile-alt"></i>
                        معلومات المنصة
                    </h6>
                </div>
                <div class="card-body">
                    {% if order.orderSource %}
                    <div class="info-row">
                        <strong>مصدر الطلب:</strong>
                        <span class="badge badge-primary">{{ order.orderSource }}</span>
                    </div>
                    {% endif %}

                    {% if order.platform %}
                    <div class="info-row">
                        <strong>المنصة:</strong>
                        <span class="badge badge-info">{{ order.platform }}</span>
                    </div>
                    {% endif %}

                    {% if order.appVersion %}
                    <div class="info-row">
                        <strong>إصدار التطبيق:</strong>
                        <span>{{ order.appVersion }}</span>
                    </div>
                    {% endif %}

                    {% if order.userEmail %}
                    <div class="info-row">
                        <strong>البريد الإلكتروني:</strong>
                        <span>{{ order.userEmail }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- الجدول الزمني -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock"></i>
                        الجدول الزمني للطلب
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% if order.statusHistory %}
                            {% for history in order.statusHistory %}
                            <div class="timeline-item {% if forloop.last %}current{% else %}completed{% endif %}">
                                <div class="timeline-content">
                                    <h6 class="timeline-title">
                                        {% if history.status == 'pending' %}
                                            تم إنشاء الطلب
                                        {% elif history.status == 'accepted' %}
                                            تم قبول الطلب
                                        {% elif history.status == 'preparing' %}
                                            قيد التحضير
                                        {% elif history.status == 'ready' %}
                                            الطلب جاهز
                                        {% elif history.status == 'out_for_delivery' %}
                                            في الطريق
                                        {% elif history.status == 'delivered' %}
                                            تم التوصيل
                                        {% elif history.status == 'cancelled' %}
                                            تم الإلغاء
                                        {% elif history.status == 'rejected' %}
                                            تم الرفض
                                        {% else %}
                                            {{ history.status }}
                                        {% endif %}
                                    </h6>
                                    <small class="text-muted timeline-time">
                                        {% if history.timestamp %}
                                            {{ history.timestamp }}
                                        {% endif %}
                                    </small>
                                    {% if history.message %}
                                    <p class="timeline-message">{{ history.message }}</p>
                                    {% endif %}
                                    {% if history.updatedBy %}
                                    <small class="text-info">
                                        بواسطة: {{ history.updatedBy }}
                                        {% if history.updatedByRole %}
                                            ({{ history.updatedByRole }})
                                        {% endif %}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <!-- الجدول الزمني التقليدي إذا لم يوجد statusHistory -->
                            <div class="timeline-item completed">
                                <h6>تم إنشاء الطلب</h6>
                                <small class="text-muted">{{ order.createdAt_formatted }}</small>
                            </div>

                            <div class="timeline-item current">
                                <h6>الحالة الحالية</h6>
                                <small class="text-muted">{{ order.status_arabic }}</small>
                            </div>

                            {% if order.updatedAt_formatted %}
                            <div class="timeline-item">
                                <h6>آخر تحديث</h6>
                                <small class="text-muted">{{ order.updatedAt_formatted }}</small>
                            </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <h5 class="text-secondary mb-3">
                    <i class="fas fa-info-circle"></i>
                    معلومات إضافية
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">معرف الطلب:</span>
                            <span class="info-value">{{ order.orderId }}</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">معرف المتجر:</span>
                            <span class="info-value">{{ order.storeId }}</span>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">إجمالي الكمية:</span>
                            <span class="info-value">{{ order.total_quantity }} قطعة</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">طريقة الدفع:</span>
                            <span class="info-value">{{ order.payment_method_arabic }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// طباعة الطلب
function printOrder() {
    window.print();
}

// تحديث الصفحة كل دقيقة للحصول على آخر التحديثات
setInterval(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}
