#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'control_zad.settings')
django.setup()

from dashboard.firebase_service import firebase_service

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase"""
    print("🔥 اختبار الاتصال بـ Firebase...")
    
    try:
        # اختبار تهيئة Firebase
        db = firebase_service.db
        print(f"✅ تم الاتصال بـ Firebase بنجاح: {type(db)}")
        
        # اختبار إرسال متجر تجريبي
        test_store_data = {
            'id': 999,
            'name': 'متجر تجريبي',
            'category': 'مطعم',
            'phone': '123456789',
            'email': '<EMAIL>',
            'is_active': True,
            'firebase_username': 'test_user',
            'firebase_password': 'test_pass',
            'id_store': 'store_999'
        }
        
        print("📤 محاولة إرسال متجر تجريبي...")
        result = firebase_service.send_store_to_firestore(test_store_data)
        
        if result:
            print("✅ تم إرسال المتجر التجريبي بنجاح!")
        else:
            print("❌ فشل في إرسال المتجر التجريبي")
            
        # اختبار إرسال منتج تجريبي
        test_product_data = {
            'id': 999,
            'name': 'منتج تجريبي',
            'description': 'وصف المنتج التجريبي',
            'category_id': 1,
            'category_name': 'فئة تجريبية',
            'store_id': 1,
            'store_name': 'متجر تجريبي',
            'price': 100,
            'is_available': True,
            'is_featured': False,
            'tags': ['تجريبي', 'اختبار'],
            'main_image_url': '',
            'meta_title': 'منتج تجريبي',
            'meta_description': 'وصف المنتج التجريبي',
            'keywords': ['تجريبي', 'اختبار']
        }
        
        print("📤 محاولة إرسال منتج تجريبي...")
        result = firebase_service.send_product_to_firestore(test_product_data)
        
        if result:
            print("✅ تم إرسال المنتج التجريبي بنجاح!")
        else:
            print("❌ فشل في إرسال المنتج التجريبي")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    test_firebase_connection()
