{% extends 'dashboard/base.html' %}

{% block title %}سجل تغييرات الطلب #{{ order.id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-0">
                <i class="fas fa-history me-2"></i>
                سجل تغييرات الطلب #{{ order.id }}
            </h1>
            <p class="text-muted">تتبع جميع التغييرات والإجراءات المتعلقة بالطلب</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <a href="{% url 'orders_detail' order.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للطلب
                </a>
                <a href="{% url 'orders_list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>
                    قائمة الطلبات
                </a>
            </div>
        </div>
    </div>

    <!-- معلومات الطلب السريعة -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">طلب #{{ order.id }}</h6>
                            <small class="text-muted">{{ order.request_date|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <strong>العميل:</strong> {{ order.custom.name|default:"غير محدد" }}
                </div>
                <div class="col-md-3">
                    <strong>المنتج:</strong> {{ order.products.product_name|default:"غير محدد" }}
                </div>
                <div class="col-md-3">
                    <strong>الحالة:</strong> 
                    <span class="badge bg-primary">{{ order.get_order_state_display }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- سجل التغييرات -->
    <div class="row">
        <div class="col-12">
            {% if history %}
                <!-- سيتم تطوير هذا الجزء لاحقاً -->
                <div class="card">
                    <div class="card-body">
                        <h5>سجل التغييرات</h5>
                        <!-- قائمة التغييرات -->
                    </div>
                </div>
            {% else %}
                <!-- حالة عدم وجود سجل -->
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                        <h5>لا يوجد سجل تغييرات</h5>
                        <p class="mb-4">لم يتم تسجيل أي تغييرات على هذا الطلب بعد</p>
                        
                        <!-- سجل افتراضي -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-plus text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">إنشاء الطلب</h6>
                                        <small class="text-muted">{{ order.request_date|date:"Y/m/d H:i" }}</small>
                                        <p class="mb-0 mt-2">تم إنشاء الطلب #{{ order.id }} بنجاح</p>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                العميل: {{ order.custom.name|default:"غير محدد" }} | 
                                                المنتج: {{ order.products.product_name|default:"غير محدد" }} | 
                                                المبلغ: {{ order.all_price }} ريال
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
