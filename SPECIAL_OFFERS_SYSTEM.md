# 🎯 نظام العروض الخاصة المتطور

## 🌟 نظرة عامة
تم إنشاء نظام شامل ومتطور لإدارة العروض الخاصة لأصحاب المتاجر والعملاء، يوفر مرونة عالية في أنواع العروض والاستهداف مع إمكانيات متقدمة للتتبع والتحليل.

## ✨ المكونات الرئيسية

### 🏗️ **النماذج (Models):**

#### **1. العرض الخاص (SpecialOffer):**
```python
class SpecialOffer(models.Model):
    title = models.CharField(...)                    # عنوان العرض
    description = models.TextField(...)              # وصف العرض
    offer_type = models.CharField(...)               # نوع العرض
    
    # تفاصيل العرض
    discount_percentage = models.DecimalField(...)   # نسبة الخصم
    discount_amount = models.DecimalField(...)       # مبلغ الخصم
    minimum_order_value = models.DecimalField(...)   # الحد الأدنى للطلب
    maximum_discount = models.DecimalField(...)      # الحد الأقصى للخصم
    
    # عرض اشتري واحصل على
    buy_quantity = models.IntegerField(...)          # اشتري كمية
    get_quantity = models.IntegerField(...)          # احصل على كمية
    
    # الفترة الزمنية
    start_date = models.DateTimeField(...)           # تاريخ البداية
    end_date = models.DateTimeField(...)             # تاريخ النهاية
    
    # الاستهداف
    target_audience = models.CharField(...)          # الجمهور المستهدف
    target_stores = models.ManyToManyField(...)      # المتاجر المستهدفة
    target_areas = models.ManyToManyField(...)       # المناطق المستهدفة
    target_products = models.ManyToManyField(...)    # المنتجات المستهدفة
    
    # الحدود والقيود
    usage_limit_per_customer = models.IntegerField(...) # حد الاستخدام لكل عميل
    total_usage_limit = models.IntegerField(...)     # حد الاستخدام الإجمالي
    current_usage_count = models.IntegerField(...)   # عدد الاستخدامات الحالي
    
    # الحالة والإعدادات
    status = models.CharField(...)                   # الحالة
    is_featured = models.BooleanField(...)           # عرض مميز
    show_on_homepage = models.BooleanField(...)      # عرض في الصفحة الرئيسية
    requires_coupon_code = models.BooleanField(...)  # يتطلب كود خصم
    coupon_code = models.CharField(...)              # كود الخصم
    
    # الصور والوسائط
    image = models.ImageField(...)                   # صورة العرض
    banner_image = models.ImageField(...)            # صورة البانر
    
    # معلومات إضافية
    terms_and_conditions = models.TextField(...)     # الشروط والأحكام
    notes = models.TextField(...)                    # ملاحظات داخلية
```

#### **2. استخدام العرض (OfferUsage):**
```python
class OfferUsage(models.Model):
    offer = models.ForeignKey(SpecialOffer, ...)     # العرض
    customer = models.ForeignKey(Customer, ...)      # العميل
    order = models.ForeignKey(Order, ...)            # الطلب
    discount_amount = models.DecimalField(...)       # مبلغ الخصم المطبق
    usage_date = models.DateTimeField(...)           # تاريخ الاستخدام
```

### 🎯 **أنواع العروض المدعومة:**

#### **💰 خصم نسبة مئوية (discount_percentage):**
- نسبة مئوية من قيمة الطلب
- إمكانية تحديد حد أقصى للخصم
- مثال: خصم 15% بحد أقصى 50,000 د.ع

#### **🪙 خصم مبلغ ثابت (discount_fixed):**
- مبلغ ثابت يُخصم من الطلب
- مثال: خصم 10,000 د.ع من أي طلب

#### **🎁 اشتري واحصل على (buy_get):**
- عرض كمية مجانية عند شراء كمية معينة
- مثال: اشتري 3 واحصل على 1 مجاناً

#### **🚚 توصيل مجاني (free_delivery):**
- إلغاء رسوم التوصيل
- يمكن ربطه بحد أدنى لقيمة الطلب

#### **📦 عرض حزمة (bundle):**
- تجميع منتجات بسعر مخفض
- عروض المنتجات المترابطة

#### **🎄 عرض موسمي (seasonal):**
- عروض المناسبات والأعياد
- عروض محدودة الوقت

### 🎭 **حالات العرض:**

#### **📝 مسودة (draft):**
- العرض قيد الإعداد
- غير مرئي للعملاء
- يمكن تعديله بحرية

#### **✅ نشط (active):**
- العرض متاح للعملاء
- يظهر في التطبيق/الموقع
- يمكن استخدامه

#### **⏸️ متوقف (paused):**
- العرض معطل مؤقتاً
- غير متاح للاستخدام
- يمكن إعادة تفعيله

#### **⏰ منتهي (expired):**
- انتهت فترة العرض
- لا يمكن استخدامه
- محفوظ للسجلات

#### **❌ ملغي (cancelled):**
- تم إلغاء العرض
- غير متاح نهائياً
- محفوظ للسجلات

### 🎯 **الجمهور المستهدف:**

#### **👥 جميع العملاء (all):**
- العرض متاح لجميع العملاء
- بدون قيود على الاستهداف

#### **🆕 العملاء الجدد (new_customers):**
- العملاء الذين لم يطلبوا من قبل
- لجذب عملاء جدد

#### **🔄 العملاء المتكررين (returning_customers):**
- العملاء الذين طلبوا سابقاً
- لتشجيع الولاء

#### **⭐ العملاء المميزين (vip_customers):**
- العملاء ذوي الطلبات الكثيرة
- عروض حصرية

#### **📍 منطقة محددة (specific_area):**
- عملاء مناطق معينة
- عروض جغرافية مستهدفة

### 🎨 **الواجهات والصفحات:**

#### **📋 قائمة العروض:**
- **عرض شبكي جميل** للعروض مع البطاقات
- **فلترة متقدمة** حسب الحالة والنوع والمتجر
- **بحث نصي** في العنوان والوصف
- **إحصائيات سريعة** للعروض النشطة والمميزة
- **أزرار إجراءات** للعرض والتعديل والحذف

#### **📝 نموذج إنشاء/تعديل العرض:**
- **واجهة تفاعلية** مع بطاقات اختيار نوع العرض
- **حقول ديناميكية** تظهر حسب نوع العرض المختار
- **معاينة مباشرة** للعرض أثناء الإنشاء
- **رفع الصور** للعرض والبانر
- **استهداف متقدم** للمتاجر والمناطق والمنتجات
- **التحقق من صحة البيانات** مع رسائل خطأ واضحة

#### **👁️ تفاصيل العرض:**
- **عرض شامل** لجميع تفاصيل العرض
- **إحصائيات الاستخدام** مع الرسوم البيانية
- **سجل الاستخدامات** الحديثة
- **شريط تقدم زمني** لفترة العرض
- **أزرار تغيير الحالة** والتعديل

#### **🗑️ تأكيد الحذف:**
- **تحذيرات واضحة** من عواقب الحذف
- **ملخص العرض** المراد حذفه
- **بدائل للحذف** مثل التعطيل
- **تأكيد مزدوج** لمنع الحذف العرضي

### 🔧 **الوظائف المتقدمة:**

#### **🧮 حساب قيمة الخصم:**
```python
def get_discount_value(self, order_value=0):
    if self.offer_type == 'discount_percentage':
        discount = (order_value * self.discount_percentage) / 100
        if self.maximum_discount > 0:
            discount = min(discount, self.maximum_discount)
        return discount
    elif self.offer_type == 'discount_fixed':
        return min(self.discount_amount, order_value)
    elif self.offer_type == 'free_delivery':
        return 0  # يتم التعامل معه في منطق التوصيل
    return 0
```

#### **✅ التحقق من صحة العرض:**
```python
def is_active(self):
    from django.utils import timezone
    now = timezone.now()
    return (
        self.status == 'active' and
        self.start_date <= now <= self.end_date and
        (self.total_usage_limit == 0 or 
         self.current_usage_count < self.total_usage_limit)
    )
```

#### **👤 التحقق من إمكانية الاستخدام:**
```python
def can_be_used_by_customer(self, customer_id):
    if not self.is_active():
        return False
    
    customer_usage = OfferUsage.objects.filter(
        offer=self,
        customer_id=customer_id
    ).count()
    
    return customer_usage < self.usage_limit_per_customer
```

#### **📊 الاستخدامات المتبقية:**
```python
def get_remaining_uses(self):
    if self.total_usage_limit == 0:
        return "بلا حدود"
    return max(0, self.total_usage_limit - self.current_usage_count)
```

### 🎯 **المسارات (URLs):**
```python
# العروض الخاصة
path('offers/', views.special_offers_list, name='special_offers_list'),
path('offers/create/', views.special_offer_create, name='special_offer_create'),
path('offers/<int:pk>/', views.special_offer_detail, name='special_offer_detail'),
path('offers/<int:pk>/edit/', views.special_offer_edit, name='special_offer_edit'),
path('offers/<int:pk>/toggle-status/', views.special_offer_toggle_status, name='special_offer_toggle_status'),
path('offers/<int:pk>/delete/', views.special_offer_delete, name='special_offer_delete'),
```

### 🎨 **التصميم والواجهة:**

#### **🌈 نظام الألوان:**
- **الأساسي**: تدرج أحمر-برتقالي للعروض الخاصة
- **أنواع العروض**: ألوان مميزة لكل نوع
- **حالات العروض**: ألوان تعبر عن الحالة
- **التفاعل**: تأثيرات hover وانتقالات سلسة

#### **📱 التجاوب:**
- **الشاشات الكبيرة**: تخطيط شبكي 3 أعمدة
- **الشاشات المتوسطة**: عمودين للعروض
- **الشاشات الصغيرة**: ترتيب عمودي واحد

#### **🎭 التأثيرات:**
- **رفع البطاقات** عند التمرير
- **انتقالات سلسة** لجميع العناصر
- **ألوان ديناميكية** حسب نوع وحالة العرض
- **أيقونات معبرة** لكل نوع عرض

### 🚀 **الفوائد المحققة:**

#### **💼 للإدارة:**
- **إدارة شاملة** للعروض والحملات التسويقية
- **تتبع دقيق** لأداء العروض والاستخدام
- **مرونة عالية** في أنواع العروض والاستهداف
- **تحليلات متقدمة** لفعالية العروض

#### **🏪 لأصحاب المتاجر:**
- **أدوات تسويقية قوية** لجذب العملاء
- **عروض مستهدفة** لمنتجات ومناطق محددة
- **تحكم كامل** في شروط وأحكام العروض
- **تتبع النتائج** والعائد على الاستثمار

#### **👥 للعملاء:**
- **عروض متنوعة** تناسب احتياجاتهم
- **خصومات جذابة** على المنتجات المفضلة
- **شفافية كاملة** في شروط العروض
- **سهولة الاستخدام** مع أكواد الخصم

### 🎯 **سيناريوهات الاستخدام:**

#### **🎉 إنشاء عرض خصم موسمي:**
1. اذهب إلى العروض الخاصة
2. انقر على "إنشاء عرض جديد"
3. اختر "عرض موسمي"
4. حدد نسبة الخصم والفترة الزمنية
5. استهدف منتجات أو مناطق معينة
6. فعّل العرض

#### **🎁 عرض اشتري واحصل على:**
1. اختر نوع "اشتري واحصل على"
2. حدد الكميات (مثال: اشتري 2 احصل على 1)
3. استهدف منتجات محددة
4. حدد فترة العرض
5. أضف شروط وأحكام

#### **🚚 عرض توصيل مجاني:**
1. اختر نوع "توصيل مجاني"
2. حدد الحد الأدنى لقيمة الطلب
3. استهدف مناطق معينة
4. حدد فترة العرض
5. فعّل العرض

### 🎉 **الخلاصة:**

تم إنشاء نظام عروض خاصة شامل ومتطور يتميز بـ:

1. **تنوع كبير** في أنواع العروض (6 أنواع مختلفة)
2. **استهداف دقيق** للعملاء والمنتجات والمناطق
3. **إدارة متقدمة** للحالات والحدود والقيود
4. **واجهات جميلة** وسهلة الاستخدام
5. **تتبع شامل** للاستخدام والأداء
6. **مرونة عالية** في التخصيص والإعدادات
7. **تصميم متجاوب** مع جميع الأجهزة
8. **حماية متقدمة** من سوء الاستخدام

النظام الآن **جاهز للاستخدام المكثف** ويوفر أدوات تسويقية قوية لزيادة المبيعات وجذب العملاء! 🚀✨
