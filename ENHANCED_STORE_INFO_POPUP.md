# 💬 نافذة معلومات المتجر المحسنة

## 🌟 نظرة عامة
تم تطوير نافذة معلومات تفاعلية متطورة تعرض بيانات شاملة ومفصلة لكل متجر عند النقر على أيقونة الموقع في الخريطة.

## ✨ الميزات الجديدة

### 🎨 **تصميم متطور:**
- **رأس جذاب** مع صورة المتجر أو أيقونة بديلة
- **تدرجات لونية** جميلة ومتناسقة
- **تنظيم محترف** للمعلومات في أقسام واضحة
- **أيقونات معبرة** لكل نوع من المعلومات

### 📊 **معلومات شاملة:**

#### **🏪 المعلومات الأساسية:**
- اسم المتجر مع تصميم بارز
- نوع المتجر في شارة ملونة
- صورة المتجر (إن وجدت) أو أيقونة بديلة

#### **📞 معلومات الاتصال:**
- المنطقة مع أيقونة الموقع
- اسم المسؤول مع أيقونة المستخدم
- رقم الهاتف (قابل للنقر للاتصال)
- البريد الإلكتروني
- الإحداثيات الدقيقة (6 خانات عشرية)

#### **💰 المعلومات المالية:**
- إيجار المحل (بالريال اليمني) - إن وجد
- عرض مميز للأسعار مع تنسيق جذاب

#### **📝 الوصف والملاحظات:**
- وصف المتجر (إن وجد)
- ملاحظات إضافية (إن وجدت)

#### **🏷️ حالة المتجر:**
- نشط/غير نشط مع ألوان مميزة
- متجر مميز (إن كان كذلك)
- متوفر 24 ساعة (إن كان كذلك)

#### **🔧 معلومات النظام:**
- تاريخ ووقت الانضمام
- معرف المتجر الداخلي
- معرف Firebase
- اسم المستخدم
- آخر تسجيل دخول

### 🎛️ **أزرار العمليات المتقدمة:**

#### **الصف الأول:**
```
[👁️ عرض التفاصيل]  [✏️ تعديل]
```

#### **الصف الثاني:**
```
[🧭 الاتجاهات]      [🔍 تكبير]
```

#### **الصف الثالث:**
```
[📋 نسخ الإحداثيات]  [📤 مشاركة]
```

## 🎨 **التصميم البصري:**

### **🖼️ رأس النافذة:**
```
┌─────────────────────────────────────────┐
│ 🖼️ صورة المتجر (120px ارتفاع)           │
│ 🎨 تدرج لوني جميل كخلفية               │
│ 📍 أيقونة المتجر في الزاوية             │
│                                         │
│ 🏪 اسم المتجر (خط كبير وواضح)          │
│ 🏷️ [نوع المتجر] (شارة ملونة)           │
└─────────────────────────────────────────┘
```

### **📋 جسم النافذة:**
```
┌─────────────────────────────────────────┐
│ 📞 معلومات الاتصال                      │
│ ├─ 📍 المنطقة: [اسم المنطقة]            │
│ ├─ 👤 المسؤول: [اسم المسؤول]            │
│ ├─ 📞 الهاتف: [رقم قابل للنقر]          │
│ ├─ 📧 البريد: [البريد الإلكتروني]       │
│ ├─ 🌐 الإحداثيات: [lat, lng]           │
│ └─ 💰 الإيجار: [المبلغ ريال يمني]       │
│                                         │
│ 📝 الوصف (إن وجد)                      │
│ [نص الوصف في صندوق مميز]                │
│                                         │
│ 📝 ملاحظات (إن وجدت)                   │
│ [نص الملاحظات في صندوق مميز]            │
│                                         │
│ 🏷️ حالة المتجر                          │
│ [✅ نشط] [⭐ مميز] [🕐 24 ساعة]         │
│                                         │
│ ℹ️ معلومات النظام                       │
│ ├─ تاريخ الانضمام: [التاريخ والوقت]      │
│ ├─ معرف المتجر: [#ID]                  │
│ ├─ معرف Firebase: [STORE_ID]           │
│ ├─ اسم المستخدم: [username]            │
│ └─ آخر دخول: [التاريخ والوقت]           │
└─────────────────────────────────────────┘
```

### **🎛️ ذيل النافذة:**
```
┌─────────────────────────────────────────┐
│ [👁️ عرض التفاصيل]  [✏️ تعديل]          │
│ [🧭 الاتجاهات]      [🔍 تكبير]          │
│ [📋 نسخ الإحداثيات]  [📤 مشاركة]        │
└─────────────────────────────────────────┘
```

## 🛠️ **التقنيات المستخدمة:**

### **🎨 CSS المتقدم:**
```css
.store-info-popup {
    max-width: 380px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
}

.store-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.info-section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
}

.info-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.info-icon {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-action {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### **⚡ JavaScript التفاعلي:**
```javascript
// عرض معلومات المتجر
function showStoreInfo(store, marker) {
    const content = `
        <div class="store-info-popup">
            <!-- رأس النافذة مع الصورة -->
            <div class="popup-header">...</div>
            
            <!-- جسم النافذة مع المعلومات -->
            <div class="popup-body">...</div>
            
            <!-- ذيل النافذة مع الأزرار -->
            <div class="popup-footer">...</div>
        </div>
    `;
    
    marker.bindPopup(content, {
        maxWidth: 400,
        className: 'enhanced-popup'
    }).openPopup();
}

// الحصول على الاتجاهات
function getDirections(lat, lng, storeName) {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
    window.open(url, '_blank');
}

// نسخ الإحداثيات
function copyCoordinates(lat, lng) {
    const coordinates = `${lat}, ${lng}`;
    navigator.clipboard.writeText(coordinates);
}

// تكبير على المتجر
function zoomToStore(lat, lng) {
    map.setView([lat, lng], 18);
}

// مشاركة المتجر
function shareStore(storeName, lat, lng) {
    const shareData = {
        title: `متجر ${storeName}`,
        text: `شاهد موقع ${storeName} على الخريطة`,
        url: `https://www.google.com/maps?q=${lat},${lng}`
    };
    
    if (navigator.share) {
        navigator.share(shareData);
    } else {
        navigator.clipboard.writeText(shareData.url);
    }
}
```

## 🎯 **الوظائف المتقدمة:**

### **👁️ عرض التفاصيل:**
- ينقل المستخدم لصفحة تفاصيل المتجر الكاملة
- يحتفظ بسياق الخريطة للعودة

### **✏️ تعديل:**
- رابط مباشر لصفحة تعديل بيانات المتجر
- للمستخدمين المخولين فقط

### **🧭 الحصول على الاتجاهات:**
- يفتح خرائط جوجل مع الاتجاهات للمتجر
- يستخدم الإحداثيات الدقيقة

### **🔍 تكبير:**
- يكبر الخريطة على موقع المتجر (مستوى 18)
- يوسط العرض على المتجر

### **📋 نسخ الإحداثيات:**
- ينسخ الإحداثيات بتنسيق "lat, lng"
- يعرض إشعار تأكيد

### **📤 مشاركة:**
- يستخدم Web Share API إن كان متوفراً
- يقع على نسخ الرابط كبديل
- يشارك رابط خرائط جوجل للموقع

## 🎨 **نظام الألوان:**

### **🎨 الألوان الأساسية:**
```css
/* التدرج الرئيسي */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)

/* حالات المتجر */
.active: #d4edda (أخضر فاتح)
.inactive: #f8d7da (أحمر فاتح)
.featured: #fff3cd (أصفر فاتح)
.hours24: #d1ecf1 (أزرق فاتح)

/* الأزرار */
.btn-primary: linear-gradient(135deg, #667eea, #764ba2)
.btn-success: linear-gradient(135deg, #28a745, #20c997)
.btn-info: linear-gradient(135deg, #17a2b8, #6f42c1)
.btn-warning: linear-gradient(135deg, #ffc107, #fd7e14)
.btn-secondary: linear-gradient(135deg, #6c757d, #495057)
.btn-dark: linear-gradient(135deg, #343a40, #212529)
```

## 📱 **التجاوب والتفاعل:**

### **📱 تصميم متجاوب:**
- يتكيف مع أحجام الشاشات المختلفة
- أزرار بحجم مناسب للمس
- نص واضح وقابل للقراءة

### **⚡ تأثيرات تفاعلية:**
- تأثير hover للأزرار مع رفع وظلال
- انتقالات سلسة للألوان والحركة
- تكبير الصورة عند hover
- إشعارات تأكيد للعمليات

### **🔔 نظام الإشعارات:**
- إشعارات ملونة حسب نوع العملية
- اختفاء تلقائي بعد 5 ثوان
- موضع ثابت في أعلى يمين الشاشة

## 🚀 **الفوائد المحققة:**

### **✅ للمستخدمين:**
- **معلومات شاملة** في مكان واحد
- **تفاعل سهل** مع عمليات متنوعة
- **تصميم جذاب** ومريح للعين
- **وصول سريع** للوظائف المهمة

### **✅ للإدارة:**
- **عرض مفصل** لبيانات كل متجر
- **وصول مباشر** لصفحات التعديل
- **معلومات النظام** للمتابعة
- **تتبع حالة** المتاجر بصرياً

### **✅ للنظام:**
- **تجربة مستخدم** محسنة
- **تنظيم أفضل** للمعلومات
- **تفاعل متطور** مع الخريطة
- **أداء محسن** مع تحميل سريع

## 🎯 **كيفية الاستخدام:**

### **📍 عرض معلومات المتجر:**
1. اذهب إلى خريطة المتاجر
2. انقر على أيقونة أي متجر
3. ستظهر النافذة المحسنة فوراً
4. تصفح المعلومات في الأقسام المختلفة

### **🎛️ استخدام الأزرار:**
1. **عرض التفاصيل** - للانتقال لصفحة المتجر
2. **تعديل** - لتعديل بيانات المتجر
3. **الاتجاهات** - لفتح خرائط جوجل
4. **تكبير** - للتكبير على الموقع
5. **نسخ الإحداثيات** - لنسخ الموقع
6. **مشاركة** - لمشاركة موقع المتجر

### **📱 التفاعل المتقدم:**
- **النقر على الهاتف** - للاتصال مباشرة
- **النقر على الإحداثيات** - لنسخها
- **hover على الصورة** - لتكبيرها
- **النقر خارج النافذة** - لإغلاقها

## 🎉 **الخلاصة:**

تم إنشاء نافذة معلومات متطورة تشمل:

1. **🎨 تصميم عصري** مع تدرجات لونية جميلة
2. **📊 معلومات شاملة** منظمة في أقسام واضحة
3. **🎛️ أزرار تفاعلية** لعمليات متنوعة ومفيدة
4. **📱 تجاوب ممتاز** مع جميع الأجهزة
5. **⚡ أداء سريع** مع تحميل فوري
6. **🔔 إشعارات ذكية** لتأكيد العمليات

**النظام الآن يوفر تجربة تفاعلية غنية ومتطورة لعرض بيانات المتاجر على الخريطة!** 💬✨
