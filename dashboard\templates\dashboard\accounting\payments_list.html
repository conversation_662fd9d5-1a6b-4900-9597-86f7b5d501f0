{% extends 'dashboard/base.html' %}

{% block title %}قائمة الدفعات - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item active">قائمة الدفعات</li>
{% endblock %}

{% block extra_css %}
<style>
.payments-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.filter-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.stats-row {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.payment-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.payment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.payment-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.payment-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
}

.payment-method-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.method-cash { background: #d4edda; color: #155724; }
.method-bank_transfer { background: #cce5ff; color: #004085; }
.method-check { background: #fff3cd; color: #856404; }
.method-online { background: #e2e3e5; color: #383d41; }

.store-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.store-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.payment-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.filter-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الصفحة -->
<div class="payments-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">
                <i class="fas fa-credit-card me-2"></i>
                قائمة الدفعات
            </h2>
            <p class="mb-0 opacity-75">سجل شامل لجميع دفعات المتاجر والمستحقات</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'accounting_dashboard' %}" class="btn btn-light">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<div class="stats-row">
    <div class="row text-center">
        <div class="col-md-3">
            <h3 class="text-primary mb-0">{{ total_payments }}</h3>
            <p class="text-muted mb-0">إجمالي الدفعات</p>
        </div>
        <div class="col-md-3">
            <h3 class="text-success mb-0">{{ total_amount|floatformat:0 }}</h3>
            <p class="text-muted mb-0">إجمالي المبلغ (د.ع)</p>
        </div>
        <div class="col-md-3">
            <h3 class="text-info mb-0">
                {% if total_payments > 0 %}
                    {% if total_payments > 0 %}
                        {% widthratio total_amount total_payments 1 %}
                    {% else %}
                        0
                    {% endif %}
                {% else %}
                    0
                {% endif %}
            </h3>
            <p class="text-muted mb-0">متوسط الدفعة (د.ع)</p>
        </div>
        <div class="col-md-3">
            <h3 class="text-warning mb-0">
                {% for payment in payments %}
                    {% if forloop.first %}{{ payment.payment_date|date:"Y/m/d" }}{% endif %}
                {% empty %}
                    -
                {% endfor %}
            </h3>
            <p class="text-muted mb-0">آخر دفعة</p>
        </div>
    </div>
</div>

<!-- فلترة -->
<div class="filter-card">
    <h6 class="mb-3">
        <i class="fas fa-filter me-2"></i>
        فلترة الدفعات
    </h6>
    <form method="get" class="row align-items-end">
        <div class="col-md-4 mb-3">
            <label class="form-label">المتجر</label>
            <select name="store" class="form-select">
                <option value="">جميع المتاجر</option>
                {% for store in stores %}
                <option value="{{ store.pk }}" {% if selected_store == store.pk|stringformat:"s" %}selected{% endif %}>
                    {{ store.store_name }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4 mb-3">
            <label class="form-label">الفترة المحاسبية</label>
            <select name="period" class="form-select">
                <option value="">جميع الفترات</option>
                {% for period in periods %}
                <option value="{{ period.pk }}" {% if selected_period == period.pk|stringformat:"s" %}selected{% endif %}>
                    {{ period.name }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4 mb-3">
            <button type="submit" class="filter-btn w-100">
                <i class="fas fa-search me-2"></i>
                تطبيق الفلتر
            </button>
        </div>
    </form>
</div>

<!-- قائمة الدفعات -->
<div class="row">
    {% if payments %}
        {% for payment in payments %}
        <div class="col-lg-6 mb-3">
            <div class="payment-card">
                <div class="payment-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="store-info">
                            <div class="store-avatar">
                                {{ payment.store_account.store.store_name|first }}
                            </div>
                            <div>
                                <h6 class="mb-1">{{ payment.store_account.store.store_name }}</h6>
                                <small class="text-muted">{{ payment.store_account.period.name }}</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="payment-amount">{{ payment.amount|floatformat:0 }} د.ع</div>
                            <span class="payment-method-badge method-{{ payment.payment_method }}">
                                {{ payment.get_payment_method_display }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-2">
                            <small class="text-muted">تاريخ الدفع:</small>
                            <br>
                            <strong>{{ payment.payment_date|date:"Y/m/d H:i" }}</strong>
                        </div>
                        <div class="col-6 mb-2">
                            <small class="text-muted">أنشأ بواسطة:</small>
                            <br>
                            <strong>{{ payment.created_by.username }}</strong>
                        </div>
                        {% if payment.reference_number %}
                        <div class="col-6 mb-2">
                            <small class="text-muted">رقم المرجع:</small>
                            <br>
                            <strong>{{ payment.reference_number }}</strong>
                        </div>
                        {% endif %}
                        <div class="col-6 mb-2">
                            <small class="text-muted">تاريخ الإنشاء:</small>
                            <br>
                            <strong>{{ payment.created_date|date:"Y/m/d H:i" }}</strong>
                        </div>
                    </div>
                    
                    {% if payment.notes %}
                    <div class="mt-3">
                        <small class="text-muted">ملاحظات:</small>
                        <p class="mb-0">{{ payment.notes|truncatechars:100 }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="payment-meta">
                            <i class="fas fa-calendar me-1"></i>
                            {{ payment.payment_date|timesince }} مضت
                        </div>
                        <div>
                            <a href="{% url 'store_account_detail' payment.store_account.pk %}" 
                               class="btn btn-sm btn-outline-primary" title="تفاصيل الحساب">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-receipt fa-4x mb-3"></i>
                        <h5>لا توجد دفعات</h5>
                        {% if selected_store or selected_period %}
                            <p class="mb-4">لا توجد دفعات تطابق معايير البحث المحددة</p>
                            <a href="{% url 'payments_list' %}" class="btn btn-primary">
                                <i class="fas fa-refresh me-2"></i>
                                عرض جميع الدفعات
                            </a>
                        {% else %}
                            <p class="mb-4">لم يتم تسجيل أي دفعات بعد</p>
                            <a href="{% url 'accounting_dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للوحة التحكم
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- معلومات إضافية -->
{% if payments %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع طرق الدفع
                </h6>
            </div>
            <div class="card-body">
                {% regroup payments by payment_method as payment_methods %}
                {% for method in payment_methods %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="payment-method-badge method-{{ method.grouper }}">
                        {{ method.list.0.get_payment_method_display }}
                    </span>
                    <strong>{{ method.list|length }} دفعة</strong>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مفيدة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن فلترة الدفعات حسب المتجر أو الفترة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        جميع الدفعات مؤرشفة ومحفوظة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن الوصول لتفاصيل كل حساب
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_dashboard' %}" 
                           class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                            <br>
                            لوحة التحكم
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_periods_list' %}" 
                           class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <br>
                            الفترات المحاسبية
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_reports' %}" 
                           class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <br>
                            التقارير
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'commission_settings_list' %}" 
                           class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <br>
                            إعدادات العمولات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
