# 🚚 ربط تسعير التوصيل مع Firebase Firestore

## 🎯 نظرة عامة

تم إنشاء نظام متكامل لربط تسعير التوصيل مع Firebase Firestore بحيث يتم إرسال أي تسعير جديد أو تحديث تلقائياً إلى Firebase، مما يضمن المزامنة الفورية بين النظام المحلي والتطبيقات المحمولة لحساب تكلفة التوصيل بدقة.

## ✨ المميزات الرئيسية

### **🔄 المزامنة التلقائية:**
- **إرسال فوري** لتسعيرات التوصيل الجديدة إلى Firebase
- **تحديث تلقائي** عند تعديل التسعيرات
- **حذف متزامن** من Firebase عند الحذف المحلي
- **مزامنة جماعية** لجميع التسعيرات الموجودة

### **🛠️ إدارة شاملة:**
- **معالجة الأخطاء** مع رسائل واضحة للمستخدم
- **مؤشرات بصرية** لحالة المزامنة
- **تتبع دقيق** للعمليات الناجحة والفاشلة
- **واجهة سهلة** للمزامنة اليدوية

## 🏗️ البنية التقنية

### **1. 🔧 دوال Firebase Service:**

#### **📤 إرسال تسعير جديد:**
```python
def send_delivery_pricing_to_firestore(self, pricing_data: Dict[str, Any]) -> bool:
    """إرسال تسعير التوصيل إلى Firebase Firestore"""
    try:
        # تحضير بيانات التسعير
        pricing_payload = {
            'id': pricing_data.get('id'),
            'area_id': pricing_data.get('area_id'),
            'area_name': pricing_data.get('area_name', ''),
            'distance_from_center': float(pricing_data.get('distance_from_center', 0)),
            'delivery_cost': float(pricing_data.get('delivery_cost', 0)),
            'estimated_time_minutes': pricing_data.get('estimated_time_minutes', 30),
            'is_active': pricing_data.get('is_active', True),
            'priority': pricing_data.get('priority', 1),
            'special_instructions': pricing_data.get('special_instructions', ''),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'created_by': pricing_data.get('created_by', 'admin'),
            'coordinates': {
                'latitude': float(pricing_data.get('latitude', 0)) if pricing_data.get('latitude') else None,
                'longitude': float(pricing_data.get('longitude', 0)) if pricing_data.get('longitude') else None
            },
            'coverage_radius': float(pricing_data.get('coverage_radius', 5000)),
            'peak_hour_multiplier': float(pricing_data.get('peak_hour_multiplier', 1.0)),
            'minimum_order_amount': float(pricing_data.get('minimum_order_amount', 0)),
            'free_delivery_threshold': float(pricing_data.get('free_delivery_threshold', 0))
        }
        
        # إرسال إلى Firestore
        doc_ref = self.db.collection('delivery_pricing').document(str(pricing_data.get('id')))
        doc_ref.set(pricing_payload)
        
        return True
    except Exception as e:
        logger.error(f"خطأ في إرسال تسعير التوصيل إلى Firebase: {str(e)}")
        return False
```

#### **🔄 تحديث تسعير موجود:**
```python
def update_delivery_pricing_in_firestore(self, pricing_id: int, pricing_data: Dict[str, Any]) -> bool:
    """تحديث تسعير التوصيل في Firebase Firestore"""
    try:
        # تحضير بيانات التحديث
        update_payload = {
            'area_name': pricing_data.get('area_name', ''),
            'distance_from_center': float(pricing_data.get('distance_from_center', 0)),
            'delivery_cost': float(pricing_data.get('delivery_cost', 0)),
            'estimated_time_minutes': pricing_data.get('estimated_time_minutes', 30),
            'is_active': pricing_data.get('is_active', True),
            'updated_at': datetime.now().isoformat(),
            'coverage_radius': float(pricing_data.get('coverage_radius', 5000)),
            'peak_hour_multiplier': float(pricing_data.get('peak_hour_multiplier', 1.0)),
            'minimum_order_amount': float(pricing_data.get('minimum_order_amount', 0)),
            'free_delivery_threshold': float(pricing_data.get('free_delivery_threshold', 0))
        }
        
        # تحديث في Firestore
        doc_ref = self.db.collection('delivery_pricing').document(str(pricing_id))
        doc_ref.update(update_payload)
        
        return True
    except Exception as e:
        logger.error(f"خطأ في تحديث تسعير التوصيل في Firebase: {str(e)}")
        return False
```

#### **🗑️ حذف تسعير:**
```python
def delete_delivery_pricing_from_firestore(self, pricing_id: int) -> bool:
    """حذف تسعير التوصيل من Firebase Firestore"""
    try:
        doc_ref = self.db.collection('delivery_pricing').document(str(pricing_id))
        doc_ref.delete()
        return True
    except Exception as e:
        logger.error(f"خطأ في حذف تسعير التوصيل من Firebase: {str(e)}")
        return False
```

### **2. 🎮 تحديث Views:**

#### **🆕 إنشاء تسعير جديد:**
```python
@login_required
def delivery_pricing_create(request):
    """إنشاء تسعير توصيل جديد"""
    if request.method == 'POST':
        try:
            # إنشاء تسعير التوصيل محلياً
            pricing = DeliveryPricing.objects.create(
                distance_from=distance_from,
                distance_to=distance_to,
                price_per_delivery=price_per_delivery,
                description=description,
                is_active=is_active,
                created_by=request.user
            )

            # إرسال تسعير التوصيل إلى Firebase Firestore
            try:
                from .firebase_service import firebase_service
                
                # تحضير بيانات التسعير للإرسال
                pricing_data = {
                    'id': pricing.id,
                    'distance_from_center': distance_from,
                    'distance_to': distance_to,
                    'delivery_cost': float(price_per_delivery),
                    'description': description,
                    'is_active': is_active,
                    'created_by': request.user.username,
                    'distance_range_text': pricing.distance_range_text,
                    'estimated_time_minutes': 30,
                    'coverage_radius': distance_to,
                    'peak_hour_multiplier': 1.0,
                    'minimum_order_amount': 0,
                    'free_delivery_threshold': 0
                }
                
                # إرسال إلى Firebase
                success = firebase_service.send_delivery_pricing_to_firestore(pricing_data)
                if success:
                    messages.success(request, f'تم إنشاء تسعير التوصيل للنطاق {pricing.distance_range_text} وإرساله إلى Firebase بنجاح!')
                else:
                    messages.warning(request, f'تم إنشاء تسعير التوصيل للنطاق {pricing.distance_range_text} محلياً، لكن فشل إرساله إلى Firebase.')
                    
            except Exception as e:
                messages.warning(request, f'تم إنشاء تسعير التوصيل للنطاق {pricing.distance_range_text} محلياً، لكن حدث خطأ في إرساله إلى Firebase: {str(e)}')
            
            return redirect('delivery_pricing_list')
        except Exception as e:
            messages.error(request, f'خطأ في إنشاء تسعير التوصيل: {str(e)}')
    # ... باقي الكود
```

#### **✏️ تعديل تسعير:**
```python
@login_required
def delivery_pricing_edit(request, pricing_id):
    """تعديل تسعير التوصيل"""
    pricing = get_object_or_404(DeliveryPricing, id=pricing_id)

    if request.method == 'POST':
        try:
            # تحديث البيانات محلياً
            pricing.distance_from = int(request.POST.get('distance_from'))
            pricing.distance_to = int(request.POST.get('distance_to'))
            pricing.price_per_delivery = float(request.POST.get('price_per_delivery'))
            pricing.description = request.POST.get('description', '').strip()
            pricing.is_active = request.POST.get('is_active') == 'on'
            pricing.save()

            # تحديث تسعير التوصيل في Firebase Firestore
            try:
                from .firebase_service import firebase_service
                
                # تحضير بيانات التسعير المحدثة
                pricing_data = {
                    'distance_from_center': pricing.distance_from,
                    'distance_to': pricing.distance_to,
                    'delivery_cost': float(pricing.price_per_delivery),
                    'description': pricing.description,
                    'is_active': pricing.is_active,
                    'distance_range_text': pricing.distance_range_text,
                    'coverage_radius': pricing.distance_to,
                }
                
                # تحديث في Firebase
                success = firebase_service.update_delivery_pricing_in_firestore(pricing.id, pricing_data)
                if success:
                    messages.success(request, f'تم تحديث تسعير التوصيل للنطاق {pricing.distance_range_text} في النظام و Firebase بنجاح!')
                else:
                    messages.warning(request, f'تم تحديث تسعير التوصيل للنطاق {pricing.distance_range_text} محلياً، لكن فشل تحديثه في Firebase.')
                    
            except Exception as e:
                messages.warning(request, f'تم تحديث تسعير التوصيل للنطاق {pricing.distance_range_text} محلياً، لكن حدث خطأ في تحديثه في Firebase: {str(e)}')
            
            return redirect('delivery_pricing_list')
        except Exception as e:
            messages.error(request, f'خطأ في تحديث تسعير التوصيل: {str(e)}')
    # ... باقي الكود
```

#### **🗑️ حذف تسعير:**
```python
@login_required
def delivery_pricing_delete(request, pricing_id):
    """حذف تسعير التوصيل"""
    pricing = get_object_or_404(DeliveryPricing, id=pricing_id)

    if request.method == 'POST':
        range_text = pricing.distance_range_text
        pricing_id_for_firebase = pricing.id
        
        # حذف تسعير التوصيل من Firebase Firestore أولاً
        try:
            from .firebase_service import firebase_service
            firebase_success = firebase_service.delete_delivery_pricing_from_firestore(pricing_id_for_firebase)
        except Exception as e:
            firebase_success = False
            print(f"خطأ في حذف تسعير التوصيل من Firebase: {str(e)}")
        
        # حذف تسعير التوصيل من قاعدة البيانات المحلية
        pricing.delete()
        
        if firebase_success:
            messages.success(request, f'تم حذف تسعير التوصيل للنطاق {range_text} من النظام و Firebase بنجاح!')
        else:
            messages.warning(request, f'تم حذف تسعير التوصيل للنطاق {range_text} من النظام، لكن فشل حذفه من Firebase.')
        
        return redirect('delivery_pricing_list')
    # ... باقي الكود
```

#### **🔄 مزامنة جماعية:**
```python
@login_required
def sync_delivery_pricing_to_firebase(request):
    """مزامنة جميع تسعيرات التوصيل مع Firebase Firestore"""
    if request.method == 'POST':
        try:
            from .firebase_service import firebase_service
            
            # الحصول على جميع تسعيرات التوصيل
            pricing_list = DeliveryPricing.objects.all()
            success_count = 0
            error_count = 0
            
            for pricing in pricing_list:
                try:
                    # تحضير بيانات التسعير
                    pricing_data = {
                        'id': pricing.id,
                        'distance_from_center': pricing.distance_from,
                        'distance_to': pricing.distance_to,
                        'delivery_cost': float(pricing.price_per_delivery),
                        'description': pricing.description,
                        'is_active': pricing.is_active,
                        'created_by': pricing.created_by.username if pricing.created_by else 'admin',
                        'distance_range_text': pricing.distance_range_text,
                        'coverage_radius': pricing.distance_to,
                    }
                    
                    # إرسال إلى Firebase
                    if firebase_service.send_delivery_pricing_to_firestore(pricing_data):
                        success_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
                    print(f"خطأ في مزامنة تسعير التوصيل {pricing.id}: {str(e)}")
            
            if error_count == 0:
                messages.success(request, f'تم مزامنة جميع تسعيرات التوصيل ({success_count} تسعير) مع Firebase بنجاح!')
            else:
                messages.warning(request, f'تم مزامنة {success_count} تسعير بنجاح، فشل في مزامنة {error_count} تسعير.')
                
        except Exception as e:
            messages.error(request, f'حدث خطأ في المزامنة: {str(e)}')
    
    return redirect('delivery_pricing_list')
```

## 🎨 تحسينات الواجهة

### **🔘 زر المزامنة:**
```html
<button type="button" class="btn btn-success btn-sm" onclick="syncDeliveryPricingToFirebase()">
    <i class="fas fa-sync-alt"></i>
    مزامنة مع Firebase
</button>
```

### **☁️ مؤشر Firebase:**
```html
<!-- مؤشر Firebase -->
<small class="text-success" title="متزامن مع Firebase Firestore">
    <i class="fas fa-cloud"></i>
    Firebase
</small>
```

### **⚡ JavaScript للمزامنة:**
```javascript
function syncDeliveryPricingToFirebase() {
    if (confirm('هل أنت متأكد من مزامنة جميع تسعيرات التوصيل مع Firebase Firestore؟')) {
        // إظهار مؤشر التحميل
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المزامنة...';
        btn.disabled = true;
        
        // إرسال طلب المزامنة
        fetch('{% url "sync_delivery_pricing_to_firebase" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                throw new Error('فشل في المزامنة');
            }
        })
        .catch(error => {
            console.error('خطأ في المزامنة:', error);
            alert('حدث خطأ أثناء المزامنة. يرجى المحاولة مرة أخرى.');
            
            // إعادة تعيين الزر
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}
```

## 📊 بنية البيانات في Firebase

### **🗂️ مسار البيانات:**
```
/delivery_pricing/
  /{pricing_id}/
    - id: number
    - area_id: number (optional)
    - area_name: string
    - distance_from_center: number (meters)
    - delivery_cost: number (YER)
    - estimated_time_minutes: number
    - is_active: boolean
    - priority: number
    - special_instructions: string
    - created_at: string (ISO format)
    - updated_at: string (ISO format)
    - created_by: string
    - coordinates: object
      - latitude: number
      - longitude: number
    - coverage_radius: number (meters)
    - peak_hour_multiplier: number
    - minimum_order_amount: number
    - free_delivery_threshold: number
```

### **📋 مثال على البيانات:**
```json
{
  "delivery_pricing": {
    "1": {
      "id": 1,
      "area_id": null,
      "area_name": "النطاق القريب",
      "distance_from_center": 0,
      "delivery_cost": 500.0,
      "estimated_time_minutes": 15,
      "is_active": true,
      "priority": 1,
      "special_instructions": "",
      "created_at": "2024-01-01T10:00:00",
      "updated_at": "2024-01-01T10:00:00",
      "created_by": "admin",
      "coordinates": {
        "latitude": 15.3694,
        "longitude": 44.1910
      },
      "coverage_radius": 2000.0,
      "peak_hour_multiplier": 1.0,
      "minimum_order_amount": 0.0,
      "free_delivery_threshold": 0.0
    },
    "2": {
      "id": 2,
      "area_id": null,
      "area_name": "النطاق المتوسط",
      "distance_from_center": 2001,
      "delivery_cost": 750.0,
      "estimated_time_minutes": 25,
      "is_active": true,
      "priority": 2,
      "special_instructions": "",
      "created_at": "2024-01-01T10:05:00",
      "updated_at": "2024-01-01T10:05:00",
      "created_by": "admin",
      "coordinates": {
        "latitude": null,
        "longitude": null
      },
      "coverage_radius": 5000.0,
      "peak_hour_multiplier": 1.2,
      "minimum_order_amount": 0.0,
      "free_delivery_threshold": 10000.0
    }
  }
}
```

## 🎯 الفوائد والمميزات

### **✅ مزايا النظام:**

1. **🔄 مزامنة فورية:** تسعيرات التوصيل تصل للتطبيقات فوراً
2. **🛡️ معالجة أخطاء:** رسائل واضحة عند فشل المزامنة
3. **📊 تتبع دقيق:** معرفة حالة كل عملية مزامنة
4. **🔧 مرونة عالية:** إمكانية المزامنة الجماعية
5. **🎨 واجهة جميلة:** مؤشرات بصرية واضحة
6. **⚡ أداء ممتاز:** عمليات سريعة وفعالة
7. **🔒 أمان عالي:** معالجة آمنة للبيانات
8. **📱 تجاوب كامل:** يعمل على جميع الأجهزة

### **🚀 تحسينات تقنية:**

1. **📡 اتصال موثوق:** استخدام Firebase SDK الرسمي
2. **🔄 إعادة المحاولة:** معالجة ذكية للأخطاء
3. **📊 تسجيل شامل:** تتبع جميع العمليات
4. **🎯 تحسين الأداء:** إرسال البيانات المطلوبة فقط
5. **🛠️ سهولة الصيانة:** كود منظم وموثق
6. **📈 قابلية التوسع:** يدعم أي عدد من التسعيرات
7. **🔧 مرونة التطوير:** سهولة إضافة مميزات جديدة
8. **🎨 تجربة مستخدم:** واجهات سهلة ومفهومة

## 🎉 النتيجة النهائية

### **✅ تم ربط تسعير التوصيل مع Firebase بنجاح:**

- ✅ **إرسال تلقائي** لتسعيرات التوصيل الجديدة
- ✅ **تحديث فوري** عند التعديل
- ✅ **حذف متزامن** من Firebase
- ✅ **مزامنة جماعية** للتسعيرات الموجودة
- ✅ **معالجة أخطاء** شاملة
- ✅ **مؤشرات بصرية** واضحة
- ✅ **رسائل تفاعلية** للمستخدم
- ✅ **حساب دقيق** لتكلفة التوصيل

### **🎯 الآن يمكنك:**

- 🆕 **إنشاء تسعيرات جديدة** وإرسالها لـ Firebase تلقائياً
- ✏️ **تعديل التسعيرات** مع تحديث Firebase فوراً
- 🗑️ **حذف التسعيرات** من النظام و Firebase
- 🔄 **مزامنة جماعية** لجميع التسعيرات
- 📊 **مراقبة حالة المزامنة** بصرياً
- 🛠️ **معالجة الأخطاء** بذكاء
- 📱 **حساب تكلفة التوصيل** في التطبيقات فوراً
- 🎯 **تحديد أسعار دقيقة** حسب المسافة

**نظام ربط تسعير التوصيل مع Firebase Firestore جاهز ويعمل بكفاءة عالية!** 🔥✨
