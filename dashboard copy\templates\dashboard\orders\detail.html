{% extends 'dashboard/base.html' %}

{% block title %}تفاصيل الطلب #{{ order.id }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'orders_list' %}">الطلبات</a></li>
<li class="breadcrumb-item active">طلب #{{ order.id }}</li>
{% endblock %}

{% block extra_css %}
<style>
/* تحسين مظهر صفحة تفاصيل الطلب */
.order-detail-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.order-info-card {
    border: none;
    box-shadow: var(--shadow-sm);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
}

.order-info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.order-status-badge {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
}

.order-status-pending { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; }
.order-status-waiting_shipping { background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white; }
.order-status-shipped { background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%); color: white; }
.order-status-on_way { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; }
.order-status-delivered { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }
.order-status-no_answer { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; }
.order-status-postponed { background: linear-gradient(135deg, #64748b 0%, #475569 100%); color: white; }
.order-status-wrong_address { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; }

.order-timeline {
    position: relative;
    padding-right: 2rem;
}

.order-timeline::before {
    content: '';
    position: absolute;
    right: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
}

.timeline-item {
    position: relative;
    padding: 1rem 0;
    margin-right: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    right: -2.5rem;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid white;
    box-shadow: 0 0 0 3px var(--primary-color);
}

.timeline-item.active::before {
    background: var(--success-color);
    box-shadow: 0 0 0 3px var(--success-color);
}

.product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--glass-border);
}

.product-placeholder {
    width: 60px;
    height: 60px;
    background: var(--bg-light);
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
}

.order-summary-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
}

.total-amount {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--success-color);
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    transition: var(--transition-fast);
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-shopping-cart me-2"></i>
            تفاصيل الطلب #{{ order.id }}
        </h1>
        <p class="text-muted">معلومات شاملة عن الطلب وحالته</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{% url 'orders_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
            <a href="{% url 'orders_edit' order.pk %}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>
                تعديل الطلب
            </a>
            <a href="{% url 'orders_print' order.pk %}" class="btn btn-success" target="_blank">
                <i class="fas fa-print me-2"></i>
                طباعة
            </a>
        </div>
    </div>
</div>

<!-- معلومات الطلب الأساسية -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card order-info-card">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0 text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم الطلب</label>
                        <p class="fw-bold">#{{ order.id }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ الطلب</label>
                        <p class="fw-bold">{{ order.request_date|date:"Y/m/d H:i" }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">العميل</label>
                        <p class="fw-bold">
                            {% if order.custom %}
                                <a href="{% url 'customers_detail' order.custom.pk %}" class="text-decoration-none">
                                    {{ order.custom.name }}
                                </a>
                            {% else %}
                                غير محدد
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المنتج</label>
                        <p class="fw-bold">
                            {% if order.products %}
                                <a href="{% url 'products_detail' order.products.pk %}" class="text-decoration-none">
                                    {{ order.products.product_name }}
                                </a>
                            {% else %}
                                غير محدد
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة الطلب</label>
                        <p class="fw-bold">
                            <span class="order-status-badge order-status-{{ order.order_state|default:'pending' }}">
                                {{ order.get_order_state_display }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم الحوالة</label>
                        <p class="fw-bold">{{ order.order_hawalah_number }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">السعر</label>
                        <p class="fw-bold text-success">{{ order.price }} د.ع</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الكمية</label>
                        <p class="fw-bold">{{ order.count }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المبلغ الإجمالي</label>
                        <p class="fw-bold total-amount">{{ order.all_price }} د.ع</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ الاستلام</label>
                        <p class="fw-bold">{{ order.get_date|date:"Y/m/d H:i" }}</p>
                    </div>
                    {% if order.notes %}
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">ملاحظات</label>
                        <p class="fw-bold">{{ order.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card order-info-card">
            <div class="card-header bg-transparent border-0">
                <h5 class="mb-0 text-success">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                {% if order.custom %}
                <div class="mb-3">
                    <label class="form-label text-muted">اسم العميل</label>
                    <p class="fw-bold">{{ order.custom.name }}</p>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">رقم الهاتف</label>
                    <p class="fw-bold">
                        <a href="tel:{{ order.custom.phone_number }}" class="text-decoration-none">
                            <i class="fas fa-phone me-1"></i>
                            {{ order.custom.phone_number }}
                        </a>
                    </p>
                </div>
                <div class="mb-3">
                    <label class="form-label text-muted">العنوان</label>
                    <p class="fw-bold">{{ order.custom.place }}</p>
                </div>
                {% if order.custom.notes %}
                <div class="mb-3">
                    <label class="form-label text-muted">ملاحظات العميل</label>
                    <p class="fw-bold">{{ order.custom.notes }}</p>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-user-slash fa-3x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد معلومات عميل</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل المنتج -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-box me-2"></i>
            تفاصيل المنتج
        </h5>
        <span class="badge bg-primary">منتج واحد</span>
    </div>
    <div class="card-body">
        {% if order.products %}
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    {% if order.products.img %}
                        <img src="{{ order.products.img.url }}" alt="{{ order.products.product_name }}"
                             class="img-fluid rounded" style="max-height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center"
                             style="height: 200px;">
                            <i class="fas fa-image fa-4x text-muted"></i>
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-9">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">اسم المنتج</label>
                            <h4 class="fw-bold text-primary">{{ order.products.product_name }}</h4>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الفئة</label>
                            <p class="fw-bold">
                                {% if order.products.categ %}
                                    <span class="badge bg-info">{{ order.products.categ.categ_name }}</span>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">المتجر</label>
                            <p class="fw-bold">
                                {% if order.products.store %}
                                    <a href="{% url 'stores_detail' order.products.store.pk %}" class="text-decoration-none">
                                        {{ order.products.store.store_name }}
                                    </a>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الشركة المصنعة</label>
                            <p class="fw-bold">
                                {% if order.products.company %}
                                    {{ order.products.company.name }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </p>
                        </div>
                        {% if order.products.seo %}
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">وصف المنتج</label>
                            <p class="text-muted">{{ order.products.seo }}</p>
                        </div>
                        {% endif %}
                        <div class="col-12">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h6 class="text-muted mb-1">السعر الأساسي</h6>
                                        <h5 class="text-success mb-0">{{ order.products.price }} د.ع</h5>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h6 class="text-muted mb-1">الكمية المطلوبة</h6>
                                        <h5 class="text-info mb-0">{{ order.count }}</h5>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-3 bg-primary text-white rounded">
                                        <h6 class="mb-1">المجموع الكلي</h6>
                                        <h5 class="mb-0">{{ order.all_price }} د.ع</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد منتج مرتبط بهذا الطلب</h5>
                <p class="text-muted">يبدو أن هناك مشكلة في بيانات الطلب</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- إجراءات الطلب -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-cogs me-2"></i>
            إجراءات الطلب
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h6 class="text-muted mb-3">تغيير حالة الطلب:</h6>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="pending">
                            <button type="submit" class="btn btn-outline-warning btn-action w-100
                                    {% if order.order_state == 'pending' %}active{% endif %}">
                                <i class="fas fa-clock me-2"></i>
                                يتم التجهيز
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="waiting_shipping">
                            <button type="submit" class="btn btn-outline-info btn-action w-100
                                    {% if order.order_state == 'waiting_shipping' %}active{% endif %}">
                                <i class="fas fa-hourglass-half me-2"></i>
                                في انتظار الشحن
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="shipped">
                            <button type="submit" class="btn btn-outline-primary btn-action w-100
                                    {% if order.order_state == 'shipped' %}active{% endif %}">
                                <i class="fas fa-shipping-fast me-2"></i>
                                تم الشحن
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="on_way">
                            <button type="submit" class="btn btn-outline-secondary btn-action w-100
                                    {% if order.order_state == 'on_way' %}active{% endif %}">
                                <i class="fas fa-truck me-2"></i>
                                في الطريق إليك
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="delivered">
                            <button type="submit" class="btn btn-outline-success btn-action w-100
                                    {% if order.order_state == 'delivered' %}active{% endif %}">
                                <i class="fas fa-check-circle me-2"></i>
                                تم الاستلام
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="no_answer">
                            <button type="submit" class="btn btn-outline-danger btn-action w-100
                                    {% if order.order_state == 'no_answer' %}active{% endif %}">
                                <i class="fas fa-phone-slash me-2"></i>
                                لا يرد
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="postponed">
                            <button type="submit" class="btn btn-outline-dark btn-action w-100
                                    {% if order.order_state == 'postponed' %}active{% endif %}">
                                <i class="fas fa-pause me-2"></i>
                                تم التأجيل
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-2">
                        <form method="post" action="{% url 'orders_update_status' order.pk %}" class="d-inline w-100">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="wrong_address">
                            <button type="submit" class="btn btn-outline-danger btn-action w-100
                                    {% if order.order_state == 'wrong_address' %}active{% endif %}">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                عنوان خاطئ
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <h6 class="text-muted mb-3">إجراءات أخرى:</h6>
                <div class="d-grid gap-2">
                    <a href="{% url 'orders_edit' order.pk %}" class="btn btn-info btn-action">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الطلب
                    </a>
                    <a href="{% url 'orders_print' order.pk %}" class="btn btn-success btn-action" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        طباعة الطلب
                    </a>
                    <button type="button" class="btn btn-warning btn-action" data-bs-toggle="modal" data-bs-target="#addNoteModal">
                        <i class="fas fa-comment me-2"></i>
                        إضافة ملاحظة
                    </button>
                    <a href="{% url 'orders_history' order.pk %}" class="btn btn-secondary btn-action">
                        <i class="fas fa-history me-2"></i>
                        سجل التغييرات
                    </a>
                    <a href="{% url 'orders_delete' order.pk %}" class="btn btn-danger btn-action">
                        <i class="fas fa-trash me-2"></i>
                        حذف الطلب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة ملاحظة -->
<div class="modal fade" id="addNoteModal" tabindex="-1" aria-labelledby="addNoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addNoteModalLabel">
                    <i class="fas fa-comment me-2"></i>
                    إضافة ملاحظة للطلب #{{ order.id }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'orders_add_note' order.pk %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="noteText" class="form-label">الملاحظة</label>
                        <textarea class="form-control" id="noteText" name="note" rows="4"
                                  placeholder="اكتب ملاحظتك هنا..." required></textarea>
                        <div class="form-text">ستظهر هذه الملاحظة في سجل تغييرات الطلب</div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومة:</strong> سيتم حفظ الملاحظة مع اسم المستخدم والتاريخ تلقائياً
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>
                        حفظ الملاحظة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد تغيير الحالة
    const statusForms = document.querySelectorAll('form[action*="update-status"]');
    statusForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const statusInput = this.querySelector('input[name="status"]');
            const statusValue = statusInput.value;
            const statusText = this.querySelector('button').textContent.trim();

            if (!confirm(`هل أنت متأكد من تغيير حالة الطلب إلى "${statusText}"؟`)) {
                e.preventDefault();
            }
        });
    });

    // تركيز على textarea عند فتح modal الملاحظة
    const addNoteModal = document.getElementById('addNoteModal');
    if (addNoteModal) {
        addNoteModal.addEventListener('shown.bs.modal', function() {
            document.getElementById('noteText').focus();
        });
    }
});
</script>
{% endblock %}
