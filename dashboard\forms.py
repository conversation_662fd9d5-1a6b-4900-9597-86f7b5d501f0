from django import forms
from .models import *

class AreasForm(forms.ModelForm):
    class Meta:
        model = Areas
        fields = '__all__'
        widgets = {
            'area_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المنطقة'})
        }

class StoreTypeForm(forms.ModelForm):
    class Meta:
        model = StoreType
        fields = '__all__'
        widgets = {
            'store_type': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'نوع المتجر'})
        }

class CompaniesForm(forms.ModelForm):
    class Meta:
        model = Companies
        fields = '__all__'
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الشركة'})
        }

class StoresForm(forms.ModelForm):
    class Meta:
        model = Stores
        # إزالة username و email لأنهما سيتم توليدهما تلقائياً
        fields = ['first_name', 'last_name', 'area', 'store_type', 'store_name',
                 'lat', 'long', 'store_person_name', 'store_person_phone_number', 'store_picture',
                 'enable', 'notification', 'description', 'note', 'is_static_in_main_screen',
                 'is_found_24_for_food_stor', 'price_stor']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول (اختياري)'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العائلة (اختياري)'}),
            'area': forms.Select(attrs={'class': 'form-control'}),
            'store_type': forms.Select(attrs={'class': 'form-control'}),
            'store_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المتجر'}),
            'lat': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سيتم تحديثه من الخريطة'}),
            'long': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سيتم تحديثه من الخريطة'}),
            'store_person_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم مسؤول المتجر'}),
            'store_person_phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'store_picture': forms.FileInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المتجر (اختياري)'}),
            'note': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'ملاحظات (اختياري)'}),
            'price_stor': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '0 ريال يمني'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جعل بعض الحقول اختيارية
        self.fields['first_name'].required = False
        self.fields['last_name'].required = False
        self.fields['description'].required = False
        self.fields['note'].required = False
        self.fields['lat'].required = False
        self.fields['long'].required = False

class CategForm(forms.ModelForm):
    class Meta:
        model = Categ
        fields = '__all__'
        widgets = {
            'categ_name': forms.TextInput(attrs={'class': 'form-control'}),
            'categ_picture': forms.FileInput(attrs={'class': 'form-control'}),
            'note': forms.TextInput(attrs={'class': 'form-control'}),
        }

class FamliesForm(forms.ModelForm):
    class Meta:
        model = Famlies
        fields = '__all__'
        widgets = {
            'categ': forms.Select(attrs={'class': 'form-control'}),
            'family_name': forms.TextInput(attrs={'class': 'form-control'}),
            'note': forms.TextInput(attrs={'class': 'form-control'}),
        }

class ProductsForm(forms.ModelForm):
    class Meta:
        model = Products
        fields = ['store', 'categ', 'family', 'company', 'product_name', 'product_description',
                 'seo', 'price', 'product_picture', 'product_picture_backend', 'enable',
                 'notification', 'note', 'is_static_in_main_screen']
        widgets = {
            'store': forms.Select(attrs={'class': 'form-control'}),
            'categ': forms.Select(attrs={'class': 'form-control'}),
            'family': forms.Select(attrs={'class': 'form-control'}),
            'company': forms.Select(attrs={'class': 'form-control'}),
            'product_name': forms.TextInput(attrs={'class': 'form-control'}),
            'product_description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'seo': forms.TextInput(attrs={'class': 'form-control'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '0 ريال يمني'}),
            'product_picture': forms.FileInput(attrs={'class': 'form-control'}),
            'product_picture_backend': forms.FileInput(attrs={'class': 'form-control'}),
            'note': forms.TextInput(attrs={'class': 'form-control'}),
        }

class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = '__all__'
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'place': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.TextInput(attrs={'class': 'form-control'}),
        }

class OrderForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = ['custom', 'products', 'price', 'count', 'all_price', 'order_hawalah_number', 'order_state']
        widgets = {
            'custom': forms.Select(attrs={'class': 'form-control'}),
            'products': forms.Select(attrs={'class': 'form-control'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '0 ريال يمني'}),
            'count': forms.NumberInput(attrs={'class': 'form-control'}),
            'all_price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '0 ريال يمني'}),
            'order_hawalah_number': forms.TextInput(attrs={'class': 'form-control'}),
            'order_state': forms.TextInput(attrs={'class': 'form-control'}),
        }

class NotificationsForm(forms.ModelForm):
    class Meta:
        model = Notifications
        fields = '__all__'
        widgets = {
            'store': forms.Select(attrs={'class': 'form-control'}),
            'notifications_text': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'from_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'to_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'products': forms.TextInput(attrs={'class': 'form-control'}),
        }

class BasketsTestHeadersForm(forms.ModelForm):
    class Meta:
        model = BasketsTestHeaders
        fields = ['bill_numbers', 'order_hawalah_number', 'order_state', 'peson_name', 
                 'phone_number', 'city', 'place', 'enables']
        widgets = {
            'bill_numbers': forms.TextInput(attrs={'class': 'form-control'}),
            'order_hawalah_number': forms.TextInput(attrs={'class': 'form-control'}),
            'order_state': forms.TextInput(attrs={'class': 'form-control'}),
            'peson_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'place': forms.TextInput(attrs={'class': 'form-control'}),
            'enables': forms.NumberInput(attrs={'class': 'form-control'}),
        }

# نماذج النظام المحاسبي
class CommissionSettingsForm(forms.ModelForm):
    class Meta:
        model = CommissionSettings
        fields = ['store', 'commission_type', 'fixed_amount', 'percentage_rate',
                 'minimum_commission', 'maximum_commission', 'is_active']
        widgets = {
            'store': forms.Select(attrs={'class': 'form-select'}),
            'commission_type': forms.Select(attrs={'class': 'form-select'}),
            'fixed_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '1'}),
            'percentage_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'max': '100'}),
            'minimum_commission': forms.NumberInput(attrs={'class': 'form-control', 'step': '1'}),
            'maximum_commission': forms.NumberInput(attrs={'class': 'form-control', 'step': '1'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class AccountingPeriodForm(forms.ModelForm):
    class Meta:
        model = AccountingPeriod
        fields = ['name', 'period_type', 'start_date', 'end_date', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'period_type': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date >= end_date:
            raise forms.ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')

        return cleaned_data

class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ['amount', 'payment_method', 'payment_date', 'reference_number', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '1'}),
            'payment_method': forms.Select(attrs={'class': 'form-select'}),
            'payment_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.store_account = kwargs.pop('store_account', None)
        super().__init__(*args, **kwargs)

        if self.store_account:
            # تحديد الحد الأقصى للمبلغ بناءً على المبلغ المتبقي
            max_amount = self.store_account.remaining_amount
            self.fields['amount'].widget.attrs['max'] = str(max_amount)
            self.fields['amount'].help_text = f'الحد الأقصى: {max_amount} د.ع'

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if self.store_account and amount > self.store_account.remaining_amount:
            raise forms.ValidationError(
                f'المبلغ لا يمكن أن يتجاوز المبلغ المتبقي ({self.store_account.remaining_amount} د.ع)'
            )
        return amount

class AccountingReportForm(forms.Form):
    """نموذج تقارير المحاسبة"""
    REPORT_TYPES = [
        ('summary', 'ملخص عام'),
        ('detailed', 'تقرير مفصل'),
        ('payments', 'تقرير الدفعات'),
        ('commissions', 'تقرير العمولات'),
    ]

    report_type = forms.ChoiceField(
        choices=REPORT_TYPES,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='نوع التقرير'
    )

    period = forms.ModelChoiceField(
        queryset=AccountingPeriod.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الفترة المحاسبية',
        required=False
    )

    store = forms.ModelChoiceField(
        queryset=Stores.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='المتجر',
        required=False
    )

    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='من تاريخ',
        required=False
    )

    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='إلى تاريخ',
        required=False
    )

    include_settled = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='تضمين الحسابات المسددة',
        required=False,
        initial=True
    )

    include_unsettled = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='تضمين الحسابات غير المسددة',
        required=False,
        initial=True
    )

# نماذج العروض الخاصة
class SpecialOfferForm(forms.ModelForm):
    class Meta:
        model = SpecialOffer
        fields = [
            'title', 'description', 'offer_type', 'discount_percentage', 'discount_amount',
            'minimum_order_value', 'maximum_discount', 'buy_quantity', 'get_quantity',
            'start_date', 'end_date', 'target_audience', 'target_stores', 'target_areas',
            'target_products', 'usage_limit_per_customer', 'total_usage_limit',
            'is_featured', 'show_on_homepage', 'requires_coupon_code', 'coupon_code',
            'image', 'banner_image', 'terms_and_conditions', 'notes'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'عنوان العرض'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'وصف مفصل للعرض'}),
            'offer_type': forms.Select(attrs={'class': 'form-select'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'discount_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '1', 'min': '0'}),
            'minimum_order_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '1', 'min': '0'}),
            'maximum_discount': forms.NumberInput(attrs={'class': 'form-control', 'step': '1', 'min': '0'}),
            'buy_quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'get_quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'start_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'end_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'target_audience': forms.Select(attrs={'class': 'form-select'}),
            'target_stores': forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5'}),
            'target_areas': forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5'}),
            'target_products': forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5'}),
            'usage_limit_per_customer': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'total_usage_limit': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'show_on_homepage': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'requires_coupon_code': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'coupon_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود الخصم'}),
            'image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'banner_image': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'terms_and_conditions': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تخصيص التسميات
        self.fields['target_stores'].help_text = 'اختر المتاجر المستهدفة (اختياري)'
        self.fields['target_areas'].help_text = 'اختر المناطق المستهدفة (اختياري)'
        self.fields['target_products'].help_text = 'اختر المنتجات المستهدفة (اختياري)'
        self.fields['total_usage_limit'].help_text = '0 = بلا حدود'
        self.fields['coupon_code'].help_text = 'اتركه فارغاً للتوليد التلقائي'

    def clean(self):
        cleaned_data = super().clean()
        offer_type = cleaned_data.get('offer_type')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        requires_coupon_code = cleaned_data.get('requires_coupon_code')
        coupon_code = cleaned_data.get('coupon_code')

        # التحقق من التواريخ
        if start_date and end_date and start_date >= end_date:
            raise forms.ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')

        # التحقق من كود الخصم
        if requires_coupon_code and not coupon_code:
            # توليد كود تلقائي
            import random
            import string
            cleaned_data['coupon_code'] = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

        # التحقق من نوع العرض والحقول المطلوبة
        if offer_type == 'discount_percentage':
            if not cleaned_data.get('discount_percentage'):
                raise forms.ValidationError('نسبة الخصم مطلوبة لهذا النوع من العروض')
        elif offer_type == 'discount_fixed':
            if not cleaned_data.get('discount_amount'):
                raise forms.ValidationError('مبلغ الخصم مطلوب لهذا النوع من العروض')
        elif offer_type == 'buy_get':
            if not cleaned_data.get('buy_quantity') or not cleaned_data.get('get_quantity'):
                raise forms.ValidationError('كمية الشراء والحصول مطلوبة لهذا النوع من العروض')

        return cleaned_data

class OfferFilterForm(forms.Form):
    """نموذج فلترة العروض"""
    STATUS_CHOICES = [('', 'جميع الحالات')] + SpecialOffer.STATUS_CHOICES
    OFFER_TYPES = [('', 'جميع الأنواع')] + SpecialOffer.OFFER_TYPES

    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الحالة'
    )

    offer_type = forms.ChoiceField(
        choices=OFFER_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='نوع العرض'
    )

    store = forms.ModelChoiceField(
        queryset=Stores.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='المتجر',
        empty_label='جميع المتاجر'
    )

    is_featured = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='العروض المميزة فقط'
    )

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'البحث في العنوان أو الوصف'}),
        label='البحث'
    )


class RatingForm(forms.ModelForm):
    """نموذج إضافة تقييم"""
    class Meta:
        model = Rating
        fields = ['rating_type', 'rating_value', 'comment', 'rated_store', 'rated_customer', 'rated_delivery_person', 'order', 'is_public']
        widgets = {
            'rating_type': forms.Select(attrs={'class': 'form-control', 'id': 'rating_type'}),
            'rating_value': forms.Select(attrs={'class': 'form-control'}),
            'comment': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'اكتب تعليقك هنا...'}),
            'rated_store': forms.Select(attrs={'class': 'form-control', 'id': 'rated_store'}),
            'rated_customer': forms.Select(attrs={'class': 'form-control', 'id': 'rated_customer'}),
            'rated_delivery_person': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم عامل التوصيل', 'id': 'rated_delivery_person'}),
            'order': forms.Select(attrs={'class': 'form-control'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جعل الحقول اختيارية حسب نوع التقييم
        self.fields['rated_store'].required = False
        self.fields['rated_customer'].required = False
        self.fields['rated_delivery_person'].required = False
        self.fields['order'].required = False

        # إضافة خيارات فارغة
        self.fields['rated_store'].empty_label = "اختر متجر..."
        self.fields['rated_customer'].empty_label = "اختر عميل..."
        self.fields['order'].empty_label = "اختر طلب (اختياري)..."


class RatingResponseForm(forms.ModelForm):
    """نموذج رد الإدارة على التقييم"""
    class Meta:
        model = Rating
        fields = ['admin_response', 'is_verified']
        widgets = {
            'admin_response': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'رد الإدارة على التقييم...'}),
            'is_verified': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class RatingFilterForm(forms.Form):
    """نموذج تصفية التقييمات"""
    RATING_TYPE_CHOICES = [
        ('', 'جميع الأنواع'),
        ('store', 'تقييمات المتاجر'),
        ('customer', 'تقييمات العملاء'),
        ('delivery', 'تقييمات عمال التوصيل'),
    ]

    RATING_VALUE_CHOICES = [
        ('', 'جميع التقييمات'),
        ('5', '5 نجوم'),
        ('4', '4 نجوم'),
        ('3', '3 نجوم'),
        ('2', 'نجمتان'),
        ('1', 'نجمة واحدة'),
    ]

    rating_type = forms.ChoiceField(
        choices=RATING_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='نوع التقييم'
    )

    rating_value = forms.ChoiceField(
        choices=RATING_VALUE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='قيمة التقييم'
    )

    store = forms.ModelChoiceField(
        queryset=Stores.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='المتجر',
        empty_label='جميع المتاجر'
    )

    customer = forms.ModelChoiceField(
        queryset=Customer.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='العميل',
        empty_label='جميع العملاء'
    )

    is_verified = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='التقييمات الموثقة فقط'
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='من تاريخ'
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='إلى تاريخ'
    )

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'البحث في التعليقات...'}),
        label='البحث'
    )
