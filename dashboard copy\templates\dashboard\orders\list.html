{% extends 'dashboard/base.html' %}

{% block title %}الطلبات - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة الطلبات</h1>
        <p class="text-muted">عرض وإدارة جميع الطلبات في النظام</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-filter me-2"></i>
                تصفية حسب الحالة
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{% url 'orders_list' %}">جميع الطلبات</a></li>
                {% for state in order_states %}
                    {% if state %}
                        <li><a class="dropdown-item" href="?status={{ state }}">{{ state }}</a></li>
                    {% endif %}
                {% endfor %}
            </ul>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-shopping-cart me-2"></i>
            قائمة الطلبات
            {% if selected_status %}
                - {{ selected_status }}
            {% endif %}
            ({{ orders.count }})
        </h5>
    </div>
    <div class="card-body">
        {% if orders %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر الإجمالي</th>
                            <th>رقم الحوالة</th>
                            <th>الحالة</th>
                            <th>تاريخ الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td><strong>#{{ order.id }}</strong></td>
                            <td>
                                <div>
                                    <strong>{{ order.custom.name }}</strong><br>
                                    <small class="text-muted">{{ order.custom.phone_number }}</small>
                                </div>
                            </td>
                            <td>{{ order.products.product_name|truncatechars:30 }}</td>
                            <td>
                                <span class="badge bg-info">{{ order.count }}</span>
                            </td>
                            <td>
                                <strong>{{ order.all_price }} د.ع</strong>
                            </td>
                            <td>{{ order.order_hawalah_number|default:"غير محدد" }}</td>
                            <td>
                                {% if order.order_state %}
                                    {% if order.order_state == "مكتمل" %}
                                        <span class="badge bg-success">{{ order.order_state }}</span>
                                    {% elif order.order_state == "قيد التنفيذ" %}
                                        <span class="badge bg-warning">{{ order.order_state }}</span>
                                    {% elif order.order_state == "ملغي" %}
                                        <span class="badge bg-danger">{{ order.order_state }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ order.order_state }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-light text-dark">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    {{ order.request_date|date:"Y/m/d" }}<br>
                                    <small class="text-muted">{{ order.request_date|time:"H:i" }}</small>
                                </div>
                            </td>
                            <td>
                                <a href="{% url 'orders_detail' order.pk %}" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5>{{ orders.count }}</h5>
                            <small>إجمالي الطلبات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>{{ orders|length }}</h5>
                            <small>طلبات اليوم</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>0</h5>
                            <small>قيد التنفيذ</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h5>0</h5>
                            <small>مكتملة</small>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات</h5>
                <p class="text-muted">
                    {% if selected_status %}
                        لا توجد طلبات بحالة "{{ selected_status }}"
                    {% else %}
                        لم يتم تسجيل أي طلبات حتى الآن
                    {% endif %}
                </p>
                {% if selected_status %}
                    <a href="{% url 'orders_list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع الطلبات
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
