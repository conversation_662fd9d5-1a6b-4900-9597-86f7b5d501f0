{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تحليلات الطلبات{% endblock %}

{% block extra_css %}
<style>
.analytics-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
}

.analytics-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.analytics-label {
    font-size: 1rem;
    opacity: 0.9;
}

.chart-container {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.top-list {
    max-height: 400px;
    overflow-y: auto;
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.top-item:last-child {
    border-bottom: none;
}

.rank-badge {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

.period-selector {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.metric-card {
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.metric-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.metric-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #858796;
    font-size: 0.9rem;
}

.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.hourly-chart {
    height: 300px;
    max-height: 300px;
}

.daily-chart {
    height: 400px;
    max-height: 400px;
}

/* منع مشاكل التمرير */
body {
    overflow-x: hidden;
}

.container-fluid {
    max-width: 100%;
    overflow-x: hidden;
}

/* إصلاح مشاكل الرسوم البيانية */
.chart-container canvas {
    max-height: 100% !important;
    height: auto !important;
}

/* منع التمرير اللا نهائي */
html, body {
    height: auto;
    min-height: 100vh;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- تحذير خطأ Firebase -->
    {% if firebase_error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h4 class="alert-heading">
            <i class="fas fa-exclamation-triangle"></i>
            خطأ في الاتصال بـ Firebase
        </h4>
        <p>لا يمكن الاتصال بقاعدة بيانات Firebase لجلب التحليلات.</p>
        <hr>
        <p class="mb-0">يرجى التحقق من الاتصال وإعادة المحاولة.</p>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar text-primary"></i>
            تحليلات الطلبات
        </h1>
        <div>
            <a href="{% url 'orders_dashboard' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-chart-line"></i>
                العودة للوحة الرئيسية
            </a>
            <a href="{% url 'orders_history' %}" class="btn btn-secondary btn-sm ml-2">
                <i class="fas fa-history"></i>
                تاريخ الطلبات
            </a>
        </div>
    </div>

    <!-- اختيار الفترة -->
    <div class="period-selector">
        <h5 class="text-primary mb-3">
            <i class="fas fa-calendar"></i>
            اختيار فترة التحليل
        </h5>
        
        <form method="GET" class="row align-items-end">
            <div class="col-md-3">
                <label for="period" class="form-label">الفترة</label>
                <select name="period" id="period" class="form-control">
                    <option value="1" {% if period == "1" %}selected{% endif %}>اليوم</option>
                    <option value="7" {% if period == "7" %}selected{% endif %}>آخر 7 أيام</option>
                    <option value="30" {% if period == "30" %}selected{% endif %}>آخر 30 يوم</option>
                    <option value="90" {% if period == "90" %}selected{% endif %}>آخر 3 أشهر</option>
                    <option value="365" {% if period == "365" %}selected{% endif %}>آخر سنة</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    تحليل
                </button>
            </div>
            
            <div class="col-md-6 text-md-right">
                <small class="text-muted">
                    فترة التحليل: من {{ start_date }} إلى {{ end_date }}
                </small>
            </div>
        </form>
    </div>

    <!-- المؤشرات الرئيسية -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="analytics-card">
                <div class="analytics-number">{{ stats.total_orders|default:0 }}</div>
                <div class="analytics-label">إجمالي الطلبات</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="analytics-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="analytics-number">{{ stats.total_revenue|default:0|floatformat:0 }}</div>
                <div class="analytics-label">إجمالي الإيرادات (ريال)</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="analytics-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="analytics-number">{{ analytics.daily_average|floatformat:1 }}</div>
                <div class="analytics-label">متوسط الطلبات يومياً</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="analytics-card" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
                <div class="analytics-number">{{ stats.average_order_value|default:0|floatformat:0 }}</div>
                <div class="analytics-label">متوسط قيمة الطلب (ريال)</div>
            </div>
        </div>
    </div>

    <!-- مؤشرات إضافية -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ analytics.revenue_per_day|floatformat:0 }}</div>
                <div class="metric-label">متوسط الإيرادات يومياً (ريال)</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.orders_by_status.delivered|default:0 }}</div>
                <div class="metric-label">طلبات مكتملة</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.orders_by_status.pending|default:0 }}</div>
                <div class="metric-label">طلبات قيد الانتظار</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.orders_by_status.cancelled|default:0 }}</div>
                <div class="metric-label">طلبات ملغية</div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <!-- التوزيع الساعي -->
        <div class="col-lg-6">
            <div class="chart-container">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-clock"></i>
                    توزيع الطلبات حسب الساعة
                </h5>
                {% if analytics.hourly_distribution %}
                <canvas id="hourlyChart" class="hourly-chart"></canvas>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                    <p class="text-muted">لا توجد بيانات للعرض</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- التوزيع اليومي -->
        <div class="col-lg-6">
            <div class="chart-container">
                <h5 class="text-success mb-3">
                    <i class="fas fa-calendar-day"></i>
                    توزيع الطلبات حسب اليوم
                </h5>
                {% if analytics.daily_distribution %}
                <canvas id="dailyChart" class="daily-chart"></canvas>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-3x text-gray-300 mb-3"></i>
                    <p class="text-muted">لا توجد بيانات للعرض</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- القوائم الأعلى -->
    <div class="row mt-4">
        <!-- أكثر المتاجر طلباً -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-store"></i>
                        أكثر المتاجر طلباً
                    </h6>
                </div>
                <div class="card-body">
                    <div class="top-list">
                        {% for store, count in analytics.top_stores.items %}
                        <div class="top-item">
                            <div class="d-flex align-items-center">
                                <div class="rank-badge">{{ forloop.counter }}</div>
                                <span class="ml-3">{{ store }}</span>
                            </div>
                            <span class="badge badge-primary">{{ count }} طلب</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أكثر المنتجات طلباً -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-utensils"></i>
                        أكثر المنتجات طلباً
                    </h6>
                </div>
                <div class="card-body">
                    <div class="top-list">
                        {% for product, count in analytics.top_products.items %}
                        <div class="top-item">
                            <div class="d-flex align-items-center">
                                <div class="rank-badge">{{ forloop.counter }}</div>
                                <span class="ml-3">{{ product }}</span>
                            </div>
                            <span class="badge badge-success">{{ count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أكثر العملاء طلباً -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-users"></i>
                        أكثر العملاء طلباً
                    </h6>
                </div>
                <div class="card-body">
                    <div class="top-list">
                        {% for customer, count in analytics.top_customers.items %}
                        <div class="top-item">
                            <div class="d-flex align-items-center">
                                <div class="rank-badge">{{ forloop.counter }}</div>
                                <span class="ml-3">{{ customer }}</span>
                            </div>
                            <span class="badge badge-warning">{{ count }} طلب</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مناطق التوصيل -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-map-marker-alt"></i>
                        توزيع الطلبات حسب مناطق التوصيل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for area, count in analytics.delivery_areas.items %}
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                                <span>{{ area }}</span>
                                <span class="badge badge-info">{{ count }} طلب</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
// التحقق من تحميل Chart.js
if (typeof Chart === 'undefined') {
    console.error('Chart.js لم يتم تحميله بشكل صحيح');
    alert('خطأ في تحميل مكتبة الرسوم البيانية. يرجى إعادة تحميل الصفحة.');
}
</script>
<script>
// بيانات التوزيع الساعي
const hourlyData = {
    {% for hour, count in analytics.hourly_distribution.items %}
    {{ hour }}: {{ count }},
    {% empty %}
    // لا توجد بيانات
    {% endfor %}
};

// بيانات التوزيع اليومي
const dailyData = {
    {% for day, count in analytics.daily_distribution.items %}
    "{{ day }}": {{ count }},
    {% empty %}
    // لا توجد بيانات
    {% endfor %}
};

// التحقق من وجود البيانات
console.log('بيانات التوزيع الساعي:', hourlyData);
console.log('بيانات التوزيع اليومي:', dailyData);

// رسم بياني للتوزيع الساعي
try {
    const hourlyCtx = document.getElementById('hourlyChart');
    if (hourlyCtx) {
        const hourlyChart = new Chart(hourlyCtx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: Array.from({length: 24}, (_, i) => i + ':00'),
                datasets: [{
                    label: 'عدد الطلبات',
                    data: Array.from({length: 24}, (_, i) => hourlyData[i] || 0),
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
} catch (error) {
    console.error('خطأ في إنشاء الرسم البياني الساعي:', error);
}

// رسم بياني للتوزيع اليومي
try {
    const dailyCtx = document.getElementById('dailyChart');
    if (dailyCtx) {
        const dailyChart = new Chart(dailyCtx.getContext('2d'), {
            type: 'line',
            data: {
                labels: Object.keys(dailyData),
                datasets: [{
                    label: 'عدد الطلبات',
                    data: Object.values(dailyData),
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
} catch (error) {
    console.error('خطأ في إنشاء الرسم البياني اليومي:', error);
}

// تحديث تلقائي كل 5 دقائق (معطل مؤقتاً لتجنب مشاكل التمرير)
// setInterval(function() {
//     location.reload();
// }, 300000);
</script>
{% endblock %}
