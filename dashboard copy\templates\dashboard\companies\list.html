{% extends 'dashboard/base.html' %}

{% block title %}الشركات - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة الشركات</h1>
        <p class="text-muted">عرض وإدارة جميع الشركات في النظام</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="{% url 'companies_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة شركة جديدة
            </a>
            <button type="button"
                    class="btn btn-success bulk-add-btn"
                    data-entity="companies"
                    data-url="{% url 'bulk_add_companies' %}"
                    data-fields='[{"name": "name", "label": "اسم الشركة", "type": "text", "required": true, "placeholder": "أدخل اسم الشركة"}]'>
                <i class="fas fa-layer-group me-2"></i>
                إضافة متعددة
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-building me-2"></i>
            قائمة الشركات
        </h5>
    </div>
    <div class="card-body">
        {% if companies %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم الشركة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for company in companies %}
                        <tr>
                            <td>{{ company.id }}</td>
                            <td>{{ company.name }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'companies_edit' company.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'companies_delete' company.pk %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد شركات</h5>
                <p class="text-muted">لم يتم إضافة أي شركات حتى الآن</p>
                <a href="{% url 'companies_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول شركة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
