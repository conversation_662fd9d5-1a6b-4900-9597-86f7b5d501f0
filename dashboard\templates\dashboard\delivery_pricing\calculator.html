{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}حاسبة تكلفة التوصيل{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
      crossorigin="" />
<style>
#map {
    height: 400px;
    width: 100%;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
}

.calculator-form {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.result-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
}

.result-item {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 0.5rem 0;
}

.coordinate-display {
    background: #e9ecef;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
    margin-top: 0.5rem;
}

.map-instructions {
    background: #d1ecf1;
    color: #0c5460;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calculator text-primary"></i>
            حاسبة تكلفة التوصيل
        </h1>
        <a href="{% url 'delivery_pricing_list' %}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row">
        <!-- النموذج -->
        <div class="col-lg-4">
            <div class="calculator-form">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-cog"></i>
                    إعدادات الحساب
                </h5>
                
                <form method="POST" id="calculatorForm">
                    {% csrf_token %}
                    
                    <div class="form-group">
                        <label for="store_id">المتجر <span class="text-danger">*</span></label>
                        <select name="store_id" id="store_id" class="form-control" required>
                            <option value="">اختر المتجر</option>
                            {% for store in stores %}
                            <option value="{{ store.id }}" 
                                    data-lat="{{ store.lat }}" 
                                    data-lng="{{ store.long }}"
                                    data-name="{{ store.store_name }}">
                                {{ store.store_name }} - {{ store.area.area_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>موقع العميل <span class="text-danger">*</span></label>
                        <div class="coordinate-display" id="customerLocation">
                            انقر على الخريطة لتحديد موقع العميل
                        </div>
                        <input type="hidden" name="customer_lat" id="customer_lat">
                        <input type="hidden" name="customer_lng" id="customer_lng">
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-block" id="calculateBtn" disabled>
                        <i class="fas fa-calculator"></i>
                        احسب التكلفة
                    </button>
                </form>
            </div>

            <!-- تعليمات الاستخدام -->
            <div class="map-instructions">
                <h6><i class="fas fa-info-circle"></i> كيفية الاستخدام:</h6>
                <ol class="mb-0 small">
                    <li>اختر المتجر من القائمة</li>
                    <li>انقر على الخريطة لتحديد موقع العميل</li>
                    <li>اضغط "احسب التكلفة"</li>
                </ol>
            </div>
        </div>

        <!-- الخريطة -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-map"></i>
                        خريطة تحديد المواقع
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div id="map"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- نتيجة الحساب -->
    {% if result %}
    <div class="row mt-4">
        <div class="col-12">
            {% if result.success %}
            <div class="result-card">
                <h4 class="mb-3">
                    <i class="fas fa-check-circle"></i>
                    نتيجة الحساب
                </h4>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="result-item">
                            <h6>المتجر</h6>
                            <p class="mb-0">{{ result.store.store_name }}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="result-item">
                            <h6>المسافة</h6>
                            <p class="mb-0">{{ result.distance_km }} كم</p>
                            <small>({{ result.distance_meters }} متر)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="result-item">
                            <h6>تكلفة التوصيل</h6>
                            <p class="mb-0 h4">{{ result.price }} ريال</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="result-item">
                            <h6>الحالة</h6>
                            <p class="mb-0">
                                <i class="fas fa-shipping-fast"></i>
                                متاح للتوصيل
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="alert alert-warning">
                <h5>
                    <i class="fas fa-exclamation-triangle"></i>
                    غير متاح للتوصيل
                </h5>
                <p class="mb-2">المسافة: {{ result.distance_km }} كم ({{ result.distance_meters }} متر)</p>
                <p class="mb-0">لا يوجد تسعير محدد لهذه المسافة. يرجى إضافة نطاق تسعير مناسب.</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>
<script>
let map;
let storeMarker;
let customerMarker;
let selectedStore = null;

// تهيئة الخريطة
function initMap() {
    console.log('تهيئة خريطة الحاسبة...');
    
    if (typeof L === 'undefined') {
        console.error('مكتبة Leaflet غير محملة');
        alert('خطأ: مكتبة الخرائط غير محملة. يرجى إعادة تحميل الصفحة.');
        return;
    }
    
    try {
        map = L.map('map').setView([15.3694, 44.1910], 12); // صنعاء
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // النقر على الخريطة لتحديد موقع العميل
        map.on('click', function(e) {
            setCustomerLocation(e.latlng.lat, e.latlng.lng);
        });
        
        console.log('تم تهيئة خريطة الحاسبة بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة خريطة الحاسبة:', error);
        alert('خطأ في تحميل الخريطة. يرجى إعادة تحميل الصفحة.');
    }
}

// تحديد موقع العميل
function setCustomerLocation(lat, lng) {
    // إزالة العلامة السابقة
    if (customerMarker) {
        map.removeLayer(customerMarker);
    }
    
    // إضافة علامة جديدة
    customerMarker = L.marker([lat, lng], {
        icon: L.divIcon({
            html: '<div style="background: #dc3545; border-radius: 50%; width: 25px; height: 25px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;"><i class="fas fa-user" style="color: white; font-size: 10px;"></i></div>',
            iconSize: [25, 25],
            iconAnchor: [12, 12]
        })
    }).addTo(map);
    
    customerMarker.bindPopup(`
        <div style="text-align: center; padding: 10px;">
            <h6 style="margin: 0 0 5px 0;">موقع العميل</h6>
            <small class="text-muted">${lat.toFixed(6)}, ${lng.toFixed(6)}</small>
        </div>
    `);
    
    // تحديث الحقول المخفية
    document.getElementById('customer_lat').value = lat.toFixed(6);
    document.getElementById('customer_lng').value = lng.toFixed(6);
    
    // تحديث العرض
    document.getElementById('customerLocation').innerHTML = `
        <i class="fas fa-map-marker-alt text-danger"></i>
        ${lat.toFixed(6)}, ${lng.toFixed(6)}
    `;
    
    // تفعيل زر الحساب
    checkFormValidity();
}

// تحديد المتجر
function setStoreLocation(store) {
    selectedStore = store;
    
    // إزالة علامة المتجر السابقة
    if (storeMarker) {
        map.removeLayer(storeMarker);
    }
    
    // إضافة علامة المتجر
    storeMarker = L.marker([store.lat, store.lng], {
        icon: L.divIcon({
            html: '<div style="background: #28a745; border-radius: 50%; width: 30px; height: 30px; border: 3px solid white; box-shadow: 0 3px 8px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;"><i class="fas fa-store" style="color: white; font-size: 12px;"></i></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        })
    }).addTo(map);
    
    storeMarker.bindPopup(`
        <div style="text-align: center; padding: 10px;">
            <h6 style="margin: 0 0 5px 0;">${store.name}</h6>
            <small class="text-muted">المتجر</small>
        </div>
    `);
    
    // التركيز على المتجر
    map.setView([store.lat, store.lng], 14);
    
    // تفعيل زر الحساب
    checkFormValidity();
}

// التحقق من صحة النموذج
function checkFormValidity() {
    const storeId = document.getElementById('store_id').value;
    const customerLat = document.getElementById('customer_lat').value;
    const customerLng = document.getElementById('customer_lng').value;
    
    const isValid = storeId && customerLat && customerLng;
    document.getElementById('calculateBtn').disabled = !isValid;
}

// أحداث النموذج
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة الحاسبة...');
    
    // تأخير قصير للتأكد من تحميل كامل للصفحة
    setTimeout(() => {
        initMap();
    }, 100);
    
    // اختيار المتجر
    document.getElementById('store_id').addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            const lat = parseFloat(option.dataset.lat) || 15.3694;
            const lng = parseFloat(option.dataset.lng) || 44.1910;
            const name = option.dataset.name;
            
            console.log('تم اختيار المتجر:', name);
            console.log('إحداثيات المتجر:', lat, lng);
            
            setStoreLocation({
                id: option.value,
                lat: lat,
                lng: lng,
                name: name
            });
        } else {
            selectedStore = null;
            if (storeMarker) {
                map.removeLayer(storeMarker);
            }
        }
        
        checkFormValidity();
    });
    
    // التحقق الأولي
    checkFormValidity();
});
</script>
{% endblock %}
