# 📚 دليل المستخدم - النظام المحاسبي المتطور

## 🎯 مقدمة
مرحباً بك في النظام المحاسبي المتطور! هذا الدليل سيساعدك على استخدام جميع ميزات النظام بكفاءة عالية.

## 🚀 البدء السريع

### 1️⃣ **الوصول للنظام المحاسبي**
- انقر على **"المحاسبة"** في شريط التنقل العلوي
- ستصل إلى لوحة التحكم المحاسبية الرئيسية

### 2️⃣ **لوحة التحكم المحاسبية**
تعرض لوحة التحكم:
- **إحصائيات عامة**: إجمالي المتاجر، الفترات النشطة، العمولات، المبالغ المعلقة
- **الفترات الحديثة**: آخر 5 فترات محاسبية
- **الحسابات المعلقة**: المتاجر التي لديها مستحقات
- **الدفعات الحديثة**: آخر 10 دفعات
- **إجراءات سريعة**: روابط للوظائف الرئيسية

## ⚙️ إعدادات العمولات

### 📝 **إنشاء إعدادات عمولة جديدة**
1. اذهب إلى **"إعدادات العمولات"**
2. انقر على **"إضافة إعدادات جديدة"**
3. اختر **المتجر** من القائمة المنسدلة
4. حدد **نوع العمولة**:

#### 💰 **المبلغ الثابت (Fixed)**
- مناسب للمتاجر ذات الطلبات المنتظمة
- مثال: 5000 د.ع لكل طلب
- **الاستخدام**: أدخل المبلغ الثابت في حقل "المبلغ الثابت"

#### 📊 **النسبة المئوية (Percentage)**
- مناسب للطلبات متغيرة القيمة
- مثال: 3% من قيمة الطلب
- **الاستخدام**: أدخل النسبة في حقل "النسبة المئوية"

#### 📈 **النظام المتدرج (Tiered)**
- يحفز على زيادة الطلبات
- العمولة تقل مع زيادة عدد الطلبات:
  - **1-10 طلبات**: المبلغ الكامل
  - **11-50 طلب**: خصم 10% (90% من المبلغ)
  - **51-100 طلب**: خصم 20% (80% من المبلغ)
  - **أكثر من 100**: خصم 30% (70% من المبلغ)

### 🔧 **الحدود الاختيارية**
- **الحد الأدنى**: أقل مبلغ عمولة ممكن
- **الحد الأقصى**: أعلى مبلغ عمولة ممكن
- **التفعيل**: يمكن تعطيل الإعدادات مؤقتاً

## 📅 الفترات المحاسبية

### 🆕 **إنشاء فترة محاسبية جديدة**
1. اذهب إلى **"الفترات المحاسبية"**
2. انقر على **"إنشاء فترة جديدة"**
3. أدخل **اسم الفترة** (مثال: يناير 2024)
4. اختر **نوع الفترة**:
   - **أسبوعي**: فترة أسبوعية
   - **شهري**: فترة شهرية
   - **ربع سنوي**: فترة ربع سنوية
   - **مخصص**: فترة مخصصة
5. حدد **تاريخ البداية** و **تاريخ النهاية**
6. أضف **ملاحظات** إضافية (اختياري)

### 🧮 **حساب المستحقات**
1. اذهب إلى **تفاصيل الفترة المحاسبية**
2. انقر على **"حساب المستحقات"**
3. سيقوم النظام بـ:
   - حساب عدد الطلبات لكل متجر في الفترة
   - حساب قيمة الطلبات الإجمالية
   - تطبيق إعدادات العمولة لكل متجر
   - إنشاء حسابات المتاجر تلقائياً

### 📊 **حالات الفترات**
- **🟢 مفتوحة**: يمكن إضافة وتعديل الطلبات
- **🟡 مغلقة**: لا يمكن تعديل الطلبات
- **⚫ نهائية**: مؤرشفة ولا يمكن تعديلها

## 💳 إدارة الدفعات

### ➕ **إضافة دفعة جديدة**
1. اذهب إلى **تفاصيل حساب المتجر**
2. انقر على **"إضافة دفعة"**
3. اختر **طريقة الدفع**:
   - **💵 نقدي**: لا يحتاج رقم مرجع
   - **🏦 تحويل بنكي**: أضف رقم العملية
   - **📄 شيك**: أضف رقم الشيك
   - **🌐 دفع إلكتروني**: أضف رقم المعاملة
4. أدخل **المبلغ** (لا يمكن تجاوز المبلغ المتبقي)
5. حدد **تاريخ الدفع**
6. أضف **رقم المرجع** (اختياري)
7. أضف **ملاحظات** (اختياري)

### 🔄 **التحديث التلقائي**
عند إضافة دفعة، يقوم النظام تلقائياً بـ:
- تحديث المبلغ المدفوع
- حساب المبلغ المتبقي
- تحديث حالة التسديد
- تسجيل تاريخ آخر دفعة

## 📈 التقارير والإحصائيات

### 📊 **أنواع التقارير**
1. **ملخص عام**: إحصائيات شاملة
2. **تقرير مفصل**: تفاصيل كاملة للحسابات
3. **تقرير الدفعات**: سجل جميع الدفعات
4. **تقرير العمولات**: تحليل العمولات

### 🔍 **فلترة التقارير**
يمكن فلترة التقارير حسب:
- **الفترة المحاسبية**: اختيار فترة محددة
- **المتجر**: تقرير متجر واحد
- **النطاق الزمني**: من تاريخ إلى تاريخ
- **حالة التسديد**: مسدد/غير مسدد

### 🖨️ **طباعة التقارير**
- انقر على **"طباعة"** في أعلى التقرير
- سيتم إخفاء العناصر غير الضرورية للطباعة
- يمكن حفظ التقرير كـ PDF

## 🎯 سيناريوهات الاستخدام العملية

### 📋 **السيناريو الأول: إعداد النظام لأول مرة**
1. **إنشاء إعدادات العمولة** لكل متجر:
   ```
   متجر الإلكترونيات → مبلغ ثابت: 8000 د.ع
   متجر الملابس → نسبة مئوية: 2.5%
   متجر الطعام → نظام متدرج: 3000 د.ع أساسي
   ```

2. **إنشاء فترة محاسبية شهرية**:
   ```
   الاسم: يناير 2024
   النوع: شهري
   من: 2024-01-01
   إلى: 2024-01-31
   ```

3. **حساب المستحقات** بعد انتهاء الشهر

### 💰 **السيناريو الثاني: إدارة الدفعات اليومية**
1. **مراجعة الحسابات المعلقة** في لوحة التحكم
2. **إضافة دفعة نقدية**:
   ```
   المتجر: متجر الإلكترونيات
   المبلغ: 50,000 د.ع
   الطريقة: نقدي
   التاريخ: اليوم
   ```

3. **متابعة التحديث التلقائي** للحساب

### 📊 **السيناريو الثالث: إنتاج التقارير الشهرية**
1. **إنتاج تقرير ملخص** للشهر:
   ```
   النوع: ملخص عام
   الفترة: يناير 2024
   تضمين: جميع الحسابات
   ```

2. **طباعة التقرير** وحفظه
3. **مراجعة الأداء** واتخاذ القرارات

## ⚠️ نصائح مهمة

### ✅ **أفضل الممارسات**
- **تحديث منتظم**: راجع الحسابات أسبوعياً
- **نسخ احتياطية**: احتفظ بنسخ من التقارير المهمة
- **توثيق الدفعات**: أضف دائماً رقم المرجع والملاحظات
- **مراجعة الإعدادات**: راجع إعدادات العمولة دورياً

### 🚫 **أخطاء شائعة يجب تجنبها**
- **عدم حساب المستحقات**: تذكر حساب المستحقات بعد إنشاء الفترة
- **تداخل الفترات**: تأكد من عدم تداخل التواريخ
- **إعدادات خاطئة**: راجع إعدادات العمولة قبل الحساب
- **دفعات مكررة**: تحقق من عدم إدخال نفس الدفعة مرتين

### 🔧 **حل المشاكل الشائعة**

#### ❓ **لا تظهر المتاجر في القائمة**
- تأكد من وجود متاجر مسجلة في النظام
- تحقق من أن المتاجر مفعلة

#### ❓ **العمولة محسوبة بشكل خاطئ**
- راجع إعدادات العمولة للمتجر
- تأكد من أن الإعدادات مفعلة
- تحقق من الحد الأدنى والأقصى

#### ❓ **لا يمكن إضافة دفعة**
- تأكد من أن المبلغ لا يتجاوز المتبقي
- تحقق من أن الحساب غير مسدد بالكامل

## 📞 الدعم والمساعدة

### 🆘 **طلب المساعدة**
إذا واجهت أي مشكلة:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ
3. تواصل مع فريق الدعم الفني

### 📚 **موارد إضافية**
- **دليل النظام الكامل**: `ACCOUNTING_SYSTEM.md`
- **التحديثات**: تابع التحديثات الجديدة
- **التدريب**: احضر جلسات التدريب المتاحة

---

## 🎉 خلاصة

النظام المحاسبي المتطور يوفر:
- **حساب دقيق** للعمولات بأنواع متعددة
- **إدارة شاملة** للفترات والدفعات
- **تقارير متقدمة** للتحليل واتخاذ القرارات
- **واجهة سهلة** وبديهية للاستخدام
- **أتمتة كاملة** للعمليات الحسابية

**استمتع باستخدام النظام وحقق أقصى استفادة من ميزاته المتطورة!** 🚀✨
