# 🔄 تحديث بنية البيانات في Firebase

## 📊 البنية الجديدة للبيانات

### **🔥 هيكل Firestore الجديد:**
```
orders (Collection)
├── ORD_1750607432123 (Document)
│   ├── id: "ORD_1750607432123"
│   ├── userId: "user_1750598480494"
│   ├── userName: "يوسف الخالد"
│   ├── userPhone: "*********"
│   ├── userEmail: ""
│   ├── storeId: "store_5"
│   ├── storeName: "متجر التقنية"
│   ├── status: "cancelled"
│   ├── paymentMethod: "cash"
│   ├── total: 920.0
│   ├── subtotal: 800.0
│   ├── tax: 120.0
│   ├── discount: 0.0
│   ├── deliveryFee: 0.0
│   ├── createdAt: DatetimeWithNanoseconds(...)
│   ├── updatedAt: "2025-06-22T19:04:30.558678"
│   ├── deliveryAddress: {
│   │   ├── fullAddress: "سعوان عمارة الجعدبي, سعوان, صنعاء"
│   │   ├── city: "سعوان"
│   │   ├── district: "صنعاء"
│   │   ├── street: "سعوان عمارة الجعدبي"
│   │   ├── buildingNumber: "1"
│   │   ├── apartmentNumber: ""
│   │   ├── latitude: null
│   │   ├── longitude: null
│   │   ├── landmark: null
│   │   └── additionalInfo: null
│   │   }
│   ├── items: [
│   │   {
│   │       ├── id: "5_1750607416922"
│   │       ├── name: "هاتف ذكي"
│   │       ├── description: "هاتف ذكي حديث"
│   │       ├── price: 800.0
│   │       ├── quantity: 1
│   │       ├── category: "إلكترونيات"
│   │       ├── imageUrl: "images/5.png"
│   │       ├── storeId: "store_5"
│   │       └── storeName: "متجر التقنية"
│   │   }
│   │   ]
│   ├── statusHistory: [...]
│   ├── estimatedDeliveryMinutes: 30.0
│   ├── deliveryTime: "asap"
│   ├── scheduledTime: null
│   ├── deliveryPersonId: null
│   ├── deliveryPersonName: null
│   ├── deliveryPersonPhone: null
│   ├── notes: null
│   ├── orderSource: "mobile_app"
│   ├── platform: "flutter"
│   └── appVersion: "1.0.0"
├── ORD_1750607432124 (Document)
│   └── ...
└── ...
```

## 🔄 التغييرات المطبقة في Firebase Service

### **1. 📥 دالة get_all_orders:**

#### **قبل التحديث:**
```python
def get_all_orders(self) -> Dict[str, Any]:
    # كان يجلب مستند واحد يحتوي على جميع الطلبات
    doc_ref = self.db.collection('orders').document('store_orders')
    doc = doc_ref.get()
    return doc.to_dict().get('store_orders', {})
```

#### **بعد التحديث:**
```python
def get_all_orders(self) -> Dict[str, Any]:
    # يجلب جميع المستندات من مجموعة orders
    orders_ref = self.db.collection('orders')
    docs = orders_ref.stream()
    
    # تنظيم الطلبات حسب المتجر
    organized_orders = {}
    
    for doc in docs:
        order_data = doc.to_dict()
        if order_data:
            store_name = order_data.get('storeName', 'متجر غير محدد')
            order_id = order_data.get('id', doc.id)
            
            if store_name not in organized_orders:
                organized_orders[store_name] = {}
            
            organized_orders[store_name][order_id] = order_data
    
    return organized_orders
```

### **2. 📅 دالة get_orders_by_date_range:**

#### **التحديثات:**
```python
def get_orders_by_date_range(self, start_date: datetime, end_date: datetime):
    # التعامل مع DatetimeWithNanoseconds و string
    created_at = order_data.get('createdAt')
    if created_at:
        if hasattr(created_at, 'replace'):
            # إذا كان DatetimeWithNanoseconds
            order_date = created_at.replace(tzinfo=None)
        else:
            # إذا كان string
            order_date = datetime.fromisoformat(str(created_at).replace('Z', '+00:00'))
            order_date = order_date.replace(tzinfo=None)
```

### **3. 📊 دالة get_orders_statistics:**

#### **التحديثات الرئيسية:**
```python
# الإيرادات - استخدام total بدلاً من storeSubtotal
total_amount = order_data.get('total', 0)
stats['total_revenue'] += total_amount

# العملاء - استخدام userName بدلاً من customerName
customer_name = order_data.get('userName', 'غير محدد')
stats['top_customers'][customer_name] += 1

# المنتجات - استخدام name بدلاً من productName
for item in items:
    product_name = item.get('name', 'غير محدد')
    quantity = item.get('quantity', 1)
    stats['top_products'][product_name] += quantity

# التوزيع الزمني - استخدام createdAt بدلاً من orderDate
created_at = order_data.get('createdAt')
if hasattr(created_at, 'replace'):
    order_date = created_at.replace(tzinfo=None)
else:
    order_date = datetime.fromisoformat(str(created_at).replace('Z', '+00:00'))
```

### **4. 🎨 دالة format_order_data:**

#### **التحديثات:**
```python
# تحويل التواريخ - التعامل مع DatetimeWithNanoseconds
date_fields = ['createdAt', 'updatedAt']
for field in date_fields:
    if field in formatted_order and formatted_order[field]:
        date_value = formatted_order[field]
        if hasattr(date_value, 'replace'):
            # إذا كان DatetimeWithNanoseconds
            date_obj = date_value.replace(tzinfo=None)
        else:
            # إذا كان string
            date_obj = datetime.fromisoformat(str(date_value).replace('Z', '+00:00'))

# إضافة معلومات العميل
formatted_order['customerName'] = formatted_order.get('userName', 'غير محدد')
formatted_order['customerPhone'] = formatted_order.get('userPhone', 'غير محدد')
formatted_order['customerId'] = formatted_order.get('userId', 'غير محدد')

# إضافة معلومات المبلغ
formatted_order['storeSubtotal'] = formatted_order.get('total', 0)
```

### **5. 🔄 دالة listen_to_orders_changes:**

#### **التحديث:**
```python
def listen_to_orders_changes(self, callback):
    def on_snapshot(col_snapshot, changes, read_time):
        for change in changes:
            if change.type.name == 'ADDED' or change.type.name == 'MODIFIED':
                callback(change.document.to_dict())
    
    # الاستماع للتغييرات في مجموعة الطلبات
    collection_ref = self.db.collection('orders')
    collection_watch = collection_ref.on_snapshot(on_snapshot)
    
    return collection_watch
```

## 📋 مقارنة الحقول

### **🔄 تغييرات أسماء الحقول:**

| **الحقل القديم** | **الحقل الجديد** | **الوصف** |
|------------------|------------------|------------|
| `customerName` | `userName` | اسم العميل |
| `customerPhone` | `userPhone` | هاتف العميل |
| `customerId` | `userId` | معرف العميل |
| `storeSubtotal` | `total` | إجمالي المبلغ |
| `orderDate` | `createdAt` | تاريخ إنشاء الطلب |
| `productName` | `name` | اسم المنتج |

### **🆕 حقول جديدة:**

| **الحقل** | **النوع** | **الوصف** |
|-----------|-----------|------------|
| `subtotal` | `float` | المبلغ الفرعي |
| `tax` | `float` | الضريبة |
| `discount` | `float` | الخصم |
| `deliveryFee` | `float` | رسوم التوصيل |
| `userEmail` | `string` | بريد العميل الإلكتروني |
| `estimatedDeliveryMinutes` | `float` | وقت التوصيل المتوقع |
| `deliveryTime` | `string` | نوع التوصيل |
| `scheduledTime` | `datetime` | وقت التوصيل المجدول |
| `deliveryPersonId` | `string` | معرف عامل التوصيل |
| `deliveryPersonName` | `string` | اسم عامل التوصيل |
| `deliveryPersonPhone` | `string` | هاتف عامل التوصيل |
| `statusHistory` | `array` | تاريخ تغييرات الحالة |
| `orderSource` | `string` | مصدر الطلب |
| `platform` | `string` | المنصة |
| `appVersion` | `string` | إصدار التطبيق |
| `notes` | `string` | ملاحظات |

### **📍 تحسينات العنوان:**

| **الحقل** | **النوع** | **الوصف** |
|-----------|-----------|------------|
| `fullAddress` | `string` | العنوان الكامل |
| `city` | `string` | المدينة |
| `district` | `string` | المنطقة |
| `street` | `string` | الشارع |
| `buildingNumber` | `string` | رقم المبنى |
| `apartmentNumber` | `string` | رقم الشقة |
| `latitude` | `float` | خط العرض |
| `longitude` | `float` | خط الطول |
| `landmark` | `string` | معلم مرجعي |
| `additionalInfo` | `string` | معلومات إضافية |

## 🎯 الفوائد من البنية الجديدة

### **✅ المميزات:**

1. **🔄 مرونة أكبر:** كل طلب مستند منفصل
2. **⚡ أداء أفضل:** استعلامات أسرع
3. **📊 تفاصيل أكثر:** معلومات شاملة عن كل طلب
4. **🔍 فلترة محسنة:** إمكانية فلترة متقدمة
5. **📈 تحليلات أدق:** بيانات أكثر تفصيلاً
6. **🔄 تزامن أفضل:** تحديثات فورية لكل طلب
7. **📱 دعم متعدد المنصات:** معلومات المنصة والإصدار
8. **🚚 تتبع التوصيل:** معلومات عامل التوصيل
9. **📍 عناوين دقيقة:** إحداثيات GPS
10. **📋 تاريخ الحالات:** تتبع كامل للتغييرات

### **🛠️ التحسينات التقنية:**

1. **🔥 Firebase Queries:** استعلامات أكثر كفاءة
2. **📊 Real-time Updates:** تحديثات فورية لكل طلب
3. **🔍 Advanced Filtering:** فلترة متقدمة بالحقول
4. **📈 Better Analytics:** تحليلات أكثر دقة
5. **🎨 Rich UI:** واجهات أكثر تفصيلاً

## 🎉 النتيجة النهائية

### **✅ تم تحديث النظام بنجاح:**

- ✅ **تحديث جميع الدوال** للتعامل مع البنية الجديدة
- ✅ **دعم DatetimeWithNanoseconds** من Firebase
- ✅ **تنظيم الطلبات** حسب المتاجر تلقائياً
- ✅ **معالجة الحقول الجديدة** بشكل صحيح
- ✅ **تحسين الأداء** والاستعلامات
- ✅ **دعم التحديثات الفورية** لكل طلب
- ✅ **تحليلات محسنة** مع البيانات الجديدة

### **🚀 النظام الآن يدعم:**

- 📊 **عرض الطلبات** بالبنية الجديدة
- 📈 **إحصائيات دقيقة** مع الحقول الجديدة
- 🔍 **فلترة متقدمة** بجميع الحقول
- 📄 **تفاصيل شاملة** لكل طلب
- 🔄 **تزامن مباشر** مع Firebase
- 📱 **معلومات المنصة** والإصدار
- 🚚 **تتبع التوصيل** الكامل
- 📍 **عناوين دقيقة** مع الإحداثيات

**النظام محدث ويعمل بكفاءة عالية مع البنية الجديدة!** 🔥✨
