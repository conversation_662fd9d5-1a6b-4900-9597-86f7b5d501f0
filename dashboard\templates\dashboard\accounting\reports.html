{% extends 'dashboard/base.html' %}

{% block title %}تقارير المحاسبة - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item active">التقارير</li>
{% endblock %}

{% block extra_css %}
<style>
.reports-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.report-form-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.report-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.report-type-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.report-type-card:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.report-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.report-type-card.selected::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.report-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.report-results {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.results-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1.5rem;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.summary-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    border-left: 3px solid;
}

.summary-card.accounts { border-left-color: #007bff; }
.summary-card.orders { border-left-color: #28a745; }
.summary-card.revenue { border-left-color: #17a2b8; }
.summary-card.commission { border-left-color: #ffc107; }
.summary-card.paid { border-left-color: #28a745; }
.summary-card.remaining { border-left-color: #dc3545; }

.summary-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.summary-label {
    color: #6c757d;
    font-size: 0.8rem;
}

.account-row {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    transition: background 0.3s ease;
}

.account-row:hover {
    background: #f8f9fa;
}

.account-row:last-child {
    border-bottom: none;
}

.settlement-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.settled { background: #d4edda; color: #155724; }
.unsettled { background: #fff3cd; color: #856404; }

.generate-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    color: white;
}

.print-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.print-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    color: white;
}

@media print {
    .no-print { display: none !important; }
    .report-results { box-shadow: none; }
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الصفحة -->
<div class="reports-header no-print">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">
                <i class="fas fa-chart-bar me-2"></i>
                تقارير المحاسبة
            </h2>
            <p class="mb-0 opacity-75">إنتاج تقارير شاملة ومفصلة للحسابات والدفعات</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'accounting_dashboard' %}" class="btn btn-light">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- نموذج التقرير -->
<div class="report-form-card no-print">
    <h6 class="mb-3">
        <i class="fas fa-filter me-2"></i>
        إعدادات التقرير
    </h6>
    
    <form method="post">
        {% csrf_token %}
        
        <!-- نوع التقرير -->
        <div class="mb-4">
            <label class="form-label">نوع التقرير</label>
            <div class="report-type-selector">
                <div class="report-type-card" data-type="summary">
                    <div class="report-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h6>ملخص عام</h6>
                    <p class="small text-muted mb-0">إحصائيات شاملة</p>
                </div>
                <div class="report-type-card" data-type="detailed">
                    <div class="report-icon">
                        <i class="fas fa-list-alt"></i>
                    </div>
                    <h6>تقرير مفصل</h6>
                    <p class="small text-muted mb-0">تفاصيل كاملة</p>
                </div>
                <div class="report-type-card" data-type="payments">
                    <div class="report-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h6>تقرير الدفعات</h6>
                    <p class="small text-muted mb-0">سجل الدفعات</p>
                </div>
                <div class="report-type-card" data-type="commissions">
                    <div class="report-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h6>تقرير العمولات</h6>
                    <p class="small text-muted mb-0">تحليل العمولات</p>
                </div>
            </div>
            {{ form.report_type }}
        </div>

        <!-- الفلاتر -->
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="{{ form.period.id_for_label }}" class="form-label">الفترة المحاسبية</label>
                {{ form.period }}
            </div>
            <div class="col-md-3 mb-3">
                <label for="{{ form.store.id_for_label }}" class="form-label">المتجر</label>
                {{ form.store }}
            </div>
            <div class="col-md-3 mb-3">
                <label for="{{ form.start_date.id_for_label }}" class="form-label">من تاريخ</label>
                {{ form.start_date }}
            </div>
            <div class="col-md-3 mb-3">
                <label for="{{ form.end_date.id_for_label }}" class="form-label">إلى تاريخ</label>
                {{ form.end_date }}
            </div>
        </div>

        <!-- خيارات إضافية -->
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="form-check">
                    {{ form.include_settled }}
                    <label class="form-check-label" for="{{ form.include_settled.id_for_label }}">
                        تضمين الحسابات المسددة
                    </label>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="form-check">
                    {{ form.include_unsettled }}
                    <label class="form-check-label" for="{{ form.include_unsettled.id_for_label }}">
                        تضمين الحسابات غير المسددة
                    </label>
                </div>
            </div>
        </div>

        <!-- زر الإنتاج -->
        <div class="text-center">
            <button type="submit" class="generate-btn">
                <i class="fas fa-chart-line me-2"></i>
                إنتاج التقرير
            </button>
        </div>
    </form>
</div>

<!-- نتائج التقرير -->
{% if show_report and report_data %}
<div class="report-results">
    <div class="results-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-1">
                    {% if report_data.report_type == 'summary' %}
                        تقرير ملخص عام
                    {% elif report_data.report_type == 'detailed' %}
                        تقرير مفصل
                    {% elif report_data.report_type == 'payments' %}
                        تقرير الدفعات
                    {% else %}
                        تقرير العمولات
                    {% endif %}
                </h4>
                <p class="mb-0 opacity-75">
                    تاريخ الإنتاج: {{ "now"|date:"Y/m/d H:i" }}
                </p>
            </div>
            <div class="no-print">
                <button onclick="window.print()" class="print-btn">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>

    <div class="p-4">
        <!-- الإحصائيات الموجزة -->
        <div class="summary-stats">
            <div class="summary-card accounts">
                <div class="summary-number text-primary">{{ report_data.total_accounts }}</div>
                <div class="summary-label">حساب متجر</div>
            </div>
            <div class="summary-card orders">
                <div class="summary-number text-success">{{ report_data.total_orders }}</div>
                <div class="summary-label">طلب</div>
            </div>
            <div class="summary-card revenue">
                <div class="summary-number text-info">{{ report_data.total_revenue|floatformat:0 }}</div>
                <div class="summary-label">إيرادات (د.ع)</div>
            </div>
            <div class="summary-card commission">
                <div class="summary-number text-warning">{{ report_data.total_commission|floatformat:0 }}</div>
                <div class="summary-label">عمولات (د.ع)</div>
            </div>
            <div class="summary-card paid">
                <div class="summary-number text-success">{{ report_data.total_paid|floatformat:0 }}</div>
                <div class="summary-label">مدفوع (د.ع)</div>
            </div>
            <div class="summary-card remaining">
                <div class="summary-number text-danger">{{ report_data.total_remaining|floatformat:0 }}</div>
                <div class="summary-label">متبقي (د.ع)</div>
            </div>
        </div>

        <!-- تفاصيل الحسابات -->
        {% if report_data.accounts %}
        <div class="mt-4">
            <h6 class="mb-3">
                <i class="fas fa-list me-2"></i>
                تفاصيل الحسابات
            </h6>
            
            {% for account in report_data.accounts %}
            <div class="account-row">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <strong>{{ account.store.store_name }}</strong>
                        <br>
                        <small class="text-muted">{{ account.period.name }}</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <strong>{{ account.orders_count }}</strong>
                        <br>
                        <small class="text-muted">طلب</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <strong>{{ account.total_orders_value|floatformat:0 }}</strong>
                        <br>
                        <small class="text-muted">قيمة (د.ع)</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <strong>{{ account.commission_amount|floatformat:0 }}</strong>
                        <br>
                        <small class="text-muted">عمولة (د.ع)</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <strong>{{ account.remaining_amount|floatformat:0 }}</strong>
                        <br>
                        <small class="text-muted">متبقي (د.ع)</small>
                    </div>
                    <div class="col-md-1 text-center">
                        <span class="settlement-badge {% if account.is_settled %}settled{% else %}unsettled{% endif %}">
                            {% if account.is_settled %}مسدد{% else %}معلق{% endif %}
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% endif %}

        <!-- معلومات الفلاتر المطبقة -->
        <div class="mt-4 pt-3 border-top">
            <h6 class="mb-2">الفلاتر المطبقة:</h6>
            <div class="row">
                {% if report_data.filters.period %}
                <div class="col-md-3">
                    <small class="text-muted">الفترة:</small>
                    <br>
                    <strong>{{ report_data.filters.period.name }}</strong>
                </div>
                {% endif %}
                {% if report_data.filters.store %}
                <div class="col-md-3">
                    <small class="text-muted">المتجر:</small>
                    <br>
                    <strong>{{ report_data.filters.store.store_name }}</strong>
                </div>
                {% endif %}
                {% if report_data.filters.start_date %}
                <div class="col-md-3">
                    <small class="text-muted">من تاريخ:</small>
                    <br>
                    <strong>{{ report_data.filters.start_date }}</strong>
                </div>
                {% endif %}
                {% if report_data.filters.end_date %}
                <div class="col-md-3">
                    <small class="text-muted">إلى تاريخ:</small>
                    <br>
                    <strong>{{ report_data.filters.end_date }}</strong>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const reportTypeSelect = document.getElementById('{{ form.report_type.id_for_label }}');
    const typeCards = document.querySelectorAll('.report-type-card');
    
    // تحديد النوع المحدد حالياً
    function updateSelectedType() {
        const selectedType = reportTypeSelect.value;
        
        typeCards.forEach(card => {
            card.classList.remove('selected');
            if (card.dataset.type === selectedType) {
                card.classList.add('selected');
            }
        });
    }
    
    // ربط النقر على البطاقات
    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            reportTypeSelect.value = this.dataset.type;
            updateSelectedType();
        });
    });
    
    // ربط تغيير القائمة المنسدلة
    reportTypeSelect.addEventListener('change', updateSelectedType);
    
    // تهيئة أولية
    updateSelectedType();
});
</script>
{% endblock %}
