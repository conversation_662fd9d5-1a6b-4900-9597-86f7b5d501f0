# Generated by Django 4.1.4 on 2025-07-09 15:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dashboard', '0005_stores_account_created_date_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='basketstest',
            name='all_price',
            field=models.DecimalField(decimal_places=0, max_digits=10, verbose_name='السعر الإجمالي (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='commissionsettings',
            name='fixed_amount',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='المبلغ الثابت (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='commissionsettings',
            name='maximum_commission',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأقصى للعمولة (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='commissionsettings',
            name='minimum_commission',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأدنى للعمولة (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='order',
            name='all_price',
            field=models.DecimalField(decimal_places=0, max_digits=10, verbose_name='السعر الإجمالي (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='order',
            name='price',
            field=models.DecimalField(decimal_places=0, max_digits=10, verbose_name='السعر (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='products',
            name='price',
            field=models.IntegerField(default=0, verbose_name='السعر (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='specialoffer',
            name='discount_amount',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='مبلغ الخصم (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='specialoffer',
            name='maximum_discount',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأقصى للخصم (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='specialoffer',
            name='minimum_order_value',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأدنى لقيمة الطلب (ريال يمني)'),
        ),
        migrations.AlterField(
            model_name='stores',
            name='price_stor',
            field=models.IntegerField(default=0, verbose_name='إيجار المحل (ريال يمني)'),
        ),
        migrations.CreateModel(
            name='DeliveryCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('center_lat', models.FloatField(help_text='خط العرض لمركز النطاق الدائري', verbose_name='خط العرض للمركز')),
                ('center_lng', models.FloatField(help_text='خط الطول لمركز النطاق الدائري', verbose_name='خط الطول للمركز')),
                ('radius_km', models.FloatField(help_text='نصف القطر للنطاق الدائري بالكيلومتر', verbose_name='نصف القطر (كيلومتر)')),
                ('delivery_cost', models.DecimalField(decimal_places=2, help_text='تكلفة المشوار بالريال اليمني', max_digits=10, verbose_name='تكلفة المشوار')),
                ('description', models.TextField(blank=True, help_text='وصف اختياري للنطاق (مثل: وسط المدينة، الأحياء الشمالية، إلخ)', null=True, verbose_name='وصف النطاق')),
                ('is_active', models.BooleanField(default=True, help_text='هل هذا النطاق نشط للتوصيل؟', verbose_name='نشط')),
                ('priority', models.IntegerField(default=1, help_text='أولوية النطاق (1 = أعلى أولوية، 10 = أقل أولوية)', verbose_name='الأولوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_costs', to='dashboard.stores', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'تكلفة المشوار',
                'verbose_name_plural': 'تكاليف المشاوير',
                'ordering': ['store', 'priority', '-created_at'],
            },
        ),
    ]
