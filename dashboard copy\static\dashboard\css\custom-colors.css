/* نظام ألوان احترافي وجاد لشركة زاد */

:root {
    /* الألوان الأساسية الجميلة والحيوية */
    --primary-color: #2563eb;        /* أزرق حيوي وجميل */
    --primary-light: #60a5fa;        /* أزرق فاتح مشرق */
    --primary-dark: #1d4ed8;         /* أزرق عميق */
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);

    --secondary-color: #475569;      /* رمادي أزرق قوي */
    --secondary-light: #64748b;      /* رمادي متوسط */
    --secondary-dark: #334155;       /* رمادي داكن */
    --secondary-gradient: linear-gradient(135deg, #475569 0%, #334155 100%);

    --accent-color: #ef4444;         /* أحمر حيوي ومشرق */
    --accent-light: #f87171;         /* أحمر فاتح */
    --accent-dark: #dc2626;          /* أحمر داكن */
    --accent-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

    --success-color: #10b981;        /* أخضر زمردي جميل */
    --success-light: #34d399;        /* أخضر فاتح مشرق */
    --success-dark: #059669;         /* أخضر داكن */
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);

    --warning-color: #f59e0b;        /* برتقالي ذهبي مشرق */
    --warning-light: #fbbf24;        /* برتقالي فاتح */
    --warning-dark: #d97706;         /* برتقالي داكن */
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

    --info-color: #06b6d4;           /* تركوازي مشرق وجميل */
    --info-light: #22d3ee;           /* تركوازي فاتح */
    --info-dark: #0891b2;            /* تركوازي داكن */
    --info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);

    --neutral-color: #1f2937;        /* رمادي محايد قوي */
    --neutral-light: #475569;        /* رمادي فاتح */
    --neutral-dark: #0f172a;         /* رمادي داكن جداً */
    --neutral-gradient: linear-gradient(135deg, #1f2937 0%, #0f172a 100%);

    /* ألوان النصوص البارزة والواضحة */
    --text-primary: #0f172a;         /* نص أساسي داكن جداً وبارز */
    --text-secondary: #1e293b;       /* نص ثانوي قوي */
    --text-muted: #475569;           /* نص خافت لكن واضح */
    --text-light: #ffffff;           /* نص أبيض */
    --text-accent: #2563eb;          /* نص ملون بارز */

    /* ألوان الخلفية الاحترافية */
    --bg-primary: #f9fafb;           /* خلفية رئيسية نظيفة */
    --bg-secondary: #f3f4f6;         /* خلفية ثانوية */
    --bg-tertiary: #e5e7eb;          /* خلفية ثالثية */
    --bg-light: #ffffff;             /* خلفية بيضاء نقية */
    --bg-dark: #111827;              /* خلفية داكنة */

    /* ألوان الزجاج والشفافية الاحترافية */
    --glass-bg: rgba(255, 255, 255, 0.98);
    --glass-bg-secondary: rgba(249, 250, 251, 0.95);
    --glass-bg-dark: rgba(17, 24, 39, 0.95);
    --glass-border: rgba(30, 58, 138, 0.15);
    --glass-border-light: rgba(229, 231, 235, 0.8);
    --glass-hover: rgba(255, 255, 255, 1);

    /* الظلال الاحترافية */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

    /* ظلال ملونة احترافية */
    --shadow-primary: 0 4px 14px rgba(30, 58, 138, 0.15);
    --shadow-secondary: 0 4px 14px rgba(100, 116, 139, 0.15);
    --shadow-success: 0 4px 14px rgba(5, 150, 105, 0.15);
    --shadow-warning: 0 4px 14px rgba(217, 119, 6, 0.15);
    --shadow-danger: 0 4px 14px rgba(220, 38, 38, 0.15);

    /* متغيرات التصميم الاحترافي */
    --border-radius-xs: 4px;
    --border-radius-sm: 6px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* خطوط رسمية */
    --font-family-primary: 'Tajawal', 'Cairo', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    --font-size-xxxl: 2rem;
}

/* خلفية بيضاء نظيفة مع نصوص بارزة */
body {
    background: #ffffff !important;
    font-family: var(--font-family-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 500;
    font-size: 15px;
}

/* إزالة أي خلفيات متحركة */
body::before,
body::after {
    display: none !important;
}

/* ضمان الخلفية البيضاء في جميع العناصر الرئيسية */
html {
    background: #ffffff !important;
}

.main-content {
    background: #ffffff !important;
}

.container,
.container-fluid {
    background: transparent !important;
}

/* إزالة أي تأثيرات خلفية متحركة */
* {
    animation: none !important;
}

/* استثناء للرسوم المتحركة المطلوبة فقط */
.animate__animated,
[data-aos] {
    animation: inherit !important;
}

/* إزالة خلفيات الصفحات */
.page-header {
    background: var(--primary-gradient) !important;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    display: none !important;
}

/* تحسين ألوان النصوص والعناوين */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
    font-weight: 700 !important;
}

.card-title {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
}

.text-muted {
    color: var(--text-muted) !important;
    font-weight: 500 !important;
}

.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700 !important;
}

/* تحسين ألوان الأزرار */
.btn-primary {
    background: var(--primary-gradient) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
}

.btn-success {
    background: var(--success-gradient) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
}

.btn-warning {
    background: var(--warning-gradient) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
}

.btn-danger {
    background: var(--accent-gradient) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
}

.btn-info {
    background: var(--info-gradient) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3) !important;
}

/* خلفية احترافية للهيدر */
.page-header {
    background: var(--primary-gradient);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid var(--glass-border);
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, transparent 100%);
}

/* نمط احترافي للمحتوى الرئيسي */
.main-content {
    background: transparent;
    min-height: calc(100vh - 80px);
}

/* بطاقات احترافية وجادة */
.card {
    background: var(--bg-light);
    border: 1px solid var(--glass-border-light);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    position: relative;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    border-color: var(--glass-border);
}

.card-header {
    background: var(--bg-light);
    color: var(--neutral-dark);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 2px solid var(--bg-tertiary);
    font-weight: 600;
    font-size: var(--font-size-lg);
    position: relative;
}

.card-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-gradient);
}

.card-body {
    padding: var(--spacing-xl);
    position: relative;
    background: var(--bg-light);
}

.card-title {
    color: var(--neutral-dark);
    font-weight: 600;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

.card-text {
    color: var(--neutral-color);
    line-height: 1.6;
}

/* بطاقات الإحصائيات الاحترافية */
.stats-card {
    background: var(--bg-light);
    border: 1px solid var(--glass-border-light);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-xs);
}

.stats-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    border-color: var(--glass-border);
}

.stats-card-primary {
    border-right: 4px solid var(--primary-color);
}

.stats-card-primary:hover {
    box-shadow: var(--shadow-primary);
}

.stats-card-secondary {
    border-right: 4px solid var(--secondary-color);
}

.stats-card-secondary:hover {
    box-shadow: var(--shadow-secondary);
}

.stats-card-success {
    border-right: 4px solid var(--success-color);
}

.stats-card-success:hover {
    box-shadow: var(--shadow-success);
}

.stats-card-warning {
    border-right: 4px solid var(--warning-color);
}

.stats-card-warning:hover {
    box-shadow: var(--shadow-warning);
}

.stats-card-info {
    border-right: 4px solid var(--info-color);
}

.stats-card-danger {
    border-right: 4px solid var(--accent-color);
}

.stats-card-danger:hover {
    box-shadow: var(--shadow-danger);
}

/* محتوى بطاقات الإحصائيات */
.stats-content {
    flex: 1;
    text-align: right;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-left: var(--spacing-lg);
}

.stats-icon.primary {
    background: rgba(30, 58, 138, 0.1);
    color: var(--primary-color);
}

.stats-icon.secondary {
    background: rgba(100, 116, 139, 0.1);
    color: var(--secondary-color);
}

.stats-icon.success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.stats-icon.warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.stats-icon.info {
    background: rgba(8, 145, 178, 0.1);
    color: var(--info-color);
}

.stats-icon.danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--accent-color);
}

/* بطاقات الإحصائيات الرئيسية الملونة */
.main-stats-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 0;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 200px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: none;
}

.main-stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* خلفية الأيقونة */
.main-stats-background {
    position: absolute;
    top: -20px;
    left: -20px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.1;
}

.main-stats-background i {
    font-size: 3rem;
}

/* محتوى البطاقة */
.main-stats-content {
    padding: var(--spacing-xl);
    height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.main-stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.main-stats-icon i {
    font-size: 1.8rem;
    color: white;
}

.main-stats-info {
    flex: 1;
}

.main-stats-number {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.main-stats-label {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.main-stats-description {
    font-size: 0.85rem;
    opacity: 0.7;
    line-height: 1.3;
}

/* ذيل البطاقة */
.main-stats-footer {
    background: rgba(0, 0, 0, 0.02);
    padding: var(--spacing-md) var(--spacing-xl);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.main-stats-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition-fast);
}

.main-stats-link:hover {
    transform: translateX(-3px);
}

/* ألوان البطاقات المختلفة */

/* بطاقة المتاجر - أخضر */
.stores-card {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.stores-card .main-stats-background {
    background: rgba(255, 255, 255, 0.1);
}

.stores-card .main-stats-icon {
    background: rgba(255, 255, 255, 0.2);
}

.stores-card .main-stats-link {
    color: rgba(255, 255, 255, 0.9);
}

.stores-card .main-stats-link:hover {
    color: white;
}

/* بطاقة المنتجات - أزرق */
.products-card {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.products-card .main-stats-background {
    background: rgba(255, 255, 255, 0.1);
}

.products-card .main-stats-icon {
    background: rgba(255, 255, 255, 0.2);
}

.products-card .main-stats-link {
    color: rgba(255, 255, 255, 0.9);
}

.products-card .main-stats-link:hover {
    color: white;
}

/* بطاقة العملاء - بنفسجي */
.customers-card {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.customers-card .main-stats-background {
    background: rgba(255, 255, 255, 0.1);
}

.customers-card .main-stats-icon {
    background: rgba(255, 255, 255, 0.2);
}

.customers-card .main-stats-link {
    color: rgba(255, 255, 255, 0.9);
}

.customers-card .main-stats-link:hover {
    color: white;
}

/* بطاقة الطلبات - برتقالي */
.orders-card {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.orders-card .main-stats-background {
    background: rgba(255, 255, 255, 0.1);
}

.orders-card .main-stats-icon {
    background: rgba(255, 255, 255, 0.2);
}

.orders-card .main-stats-link {
    color: rgba(255, 255, 255, 0.9);
}

.orders-card .main-stats-link:hover {
    color: white;
}

/* تأثيرات إضافية للبطاقات */
.main-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition-normal);
}

.main-stats-card:hover::before {
    opacity: 1;
}

/* تحريك الأرقام */
.main-stats-number {
    position: relative;
    overflow: hidden;
}

.main-stats-number::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
    transition: var(--transition-normal);
}

.main-stats-card:hover .main-stats-number::after {
    width: 100%;
}

/* تأثير النبض للأيقونات */
@keyframes pulse-icon {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.main-stats-card:hover .main-stats-icon {
    animation: pulse-icon 2s infinite;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-stats-card {
        height: 180px;
        margin-bottom: var(--spacing-lg);
    }

    .main-stats-content {
        padding: var(--spacing-lg);
        height: 120px;
    }

    .main-stats-number {
        font-size: 2rem;
    }

    .main-stats-icon {
        width: 50px;
        height: 50px;
    }

    .main-stats-icon i {
        font-size: 1.5rem;
    }
}

/* قواعد النصوص السوداء للخلفيات البيضاء والباهتة */

/* البطاقات والكروت */
.card {
    color: #1a1a1a !important;
}

.card-header {
    color: #1a1a1a !important;
}

.card-body {
    color: #1a1a1a !important;
}

.card-title {
    color: #1a1a1a !important;
}

.card-text {
    color: #1a1a1a !important;
}

/* الجداول */
.table {
    color: #1a1a1a !important;
}

.table td {
    color: #1a1a1a !important;
}

.table tbody tr {
    color: #1a1a1a !important;
}

/* النصوص العامة */
body {
    color: #1a1a1a !important;
}

p {
    color: #1a1a1a !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #1a1a1a !important;
}

/* التسميات والعناوين */
.form-label {
    color: #1a1a1a !important;
}

.fw-bold {
    color: #1a1a1a !important;
}

/* النصوص في الخلفيات البيضاء */
.bg-white,
.bg-light,
.bg-transparent {
    color: #1a1a1a !important;
}

.bg-white *,
.bg-light *,
.bg-transparent * {
    color: #1a1a1a !important;
}

/* استثناءات للعناصر الملونة */
.text-primary,
.text-success,
.text-warning,
.text-danger,
.text-info,
.text-secondary {
    color: inherit !important;
}

/* استثناءات للبطاقات الملونة */
.stores-card,
.stores-card *,
.products-card,
.products-card *,
.customers-card,
.customers-card *,
.orders-card,
.orders-card * {
    color: white !important;
}

/* استثناءات للشريط الجانبي - تحسين ألوان النصوص */
.sidebar {
    background: var(--primary-gradient);
}

/* عناوين الأقسام تبقى بيضاء */
.sidebar .nav-section-title,
.sidebar .nav-section-title * {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* الروابط غير النشطة تكون بلون أسود فاتح */
.sidebar .nav-link {
    color: #2a2a2a !important;
    background: rgba(255, 255, 255, 0.9) !important;
    margin: 4px 12px !important;
    border-radius: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.sidebar .nav-link span {
    color: #2a2a2a !important;
}

.sidebar .nav-link small {
    color: #555555 !important;
}

.sidebar .nav-link i {
    color: inherit !important;
}

/* الروابط عند التمرير */
.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 1) !important;
    color: #1a1a1a !important;
    transform: translateX(-8px);
    border-color: rgba(255, 255, 255, 0.4) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.sidebar .nav-link:hover span {
    color: #1a1a1a !important;
}

.sidebar .nav-link:hover small {
    color: #444444 !important;
}

/* الروابط النشطة تبقى بيضاء */
.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    font-weight: 600;
    border-color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
}

.sidebar .nav-link.active span {
    color: white !important;
}

.sidebar .nav-link.active small {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .nav-link.active i {
    color: white !important;
}

/* تحسين إضافي للشريط الجانبي */
.sidebar .nav-link .d-flex {
    align-items: center !important;
    margin-bottom: 4px !important;
}

.sidebar .nav-link i {
    width: 20px !important;
    text-align: center !important;
    margin-left: 12px !important;
    font-size: 1.1rem !important;
}

/* تحسين مؤشر التنقل */
.nav-indicator {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 0;
    background: #1a1a1a;
    transition: var(--transition-normal);
    border-radius: 0 3px 3px 0;
}

.sidebar .nav-link:hover .nav-indicator {
    height: 70%;
    background: #1a1a1a;
}

.sidebar .nav-link.active .nav-indicator {
    height: 70%;
    background: white;
}

/* تحسين التباعد */
.sidebar .nav-section {
    margin-bottom: 24px !important;
}

.sidebar .nav-section:last-child {
    margin-bottom: 16px !important;
}

/* تحسين عناوين الأقسام */
.sidebar .nav-section-title {
    font-size: 0.8rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding-bottom: 8px !important;
    margin-bottom: 16px !important;
}

/* تحسين الانتقالات */
.sidebar .nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sidebar .nav-link:hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* تحسين الظلال */
.sidebar .nav-link {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.sidebar .nav-link:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

.sidebar .nav-link.active {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
}

/* تحسين النصوص الصغيرة */
.sidebar .nav-link small {
    font-size: 0.7rem !important;
    line-height: 1.2 !important;
    margin-top: 2px !important;
    margin-right: 32px !important;
    opacity: 0.8 !important;
}

.sidebar .nav-link:hover small {
    opacity: 1 !important;
}

.sidebar .nav-link.active small {
    opacity: 0.9 !important;
}

/* شريط التنقل العلوي */
.top-nav-bar {
    margin-bottom: var(--spacing-lg);
}

.top-nav-links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.top-nav-link {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: white;
    color: #1a1a1a !important;
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.top-nav-link i {
    margin-left: 6px;
    font-size: 0.9rem;
    color: #666666;
}

.top-nav-link span {
    color: #1a1a1a !important;
}

.top-nav-link:hover {
    background: var(--primary-color);
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    border-color: var(--primary-color);
}

.top-nav-link:hover i {
    color: white !important;
}

.top-nav-link:hover span {
    color: white !important;
}

.top-nav-link.active {
    background: var(--primary-color);
    color: white !important;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.top-nav-link.active i {
    color: white !important;
}

.top-nav-link.active span {
    color: white !important;
}

/* إجراءات شريط التنقل العلوي */
.top-nav-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.welcome-text {
    font-size: 0.85rem;
    color: #1a1a1a !important;
    font-weight: 600;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .top-nav-links {
        justify-content: center;
        gap: 6px;
    }

    .top-nav-link {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .top-nav-link span {
        display: none;
    }

    .top-nav-link i {
        margin-left: 0;
        font-size: 1rem;
    }

    .welcome-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .top-nav-links {
        gap: 4px;
    }

    .top-nav-link {
        padding: 6px 8px;
    }
}

/* تحسين breadcrumb */
.breadcrumb {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 0 !important;
}

.breadcrumb-item {
    color: #1a1a1a !important;
}

.breadcrumb-item a {
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

.breadcrumb-item a:hover {
    color: var(--primary-dark) !important;
}

.breadcrumb-item.active {
    color: #666666 !important;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: #666666 !important;
}

/* استثناءات للأزرار الملونة */
.btn-primary,
.btn-primary *,
.btn-success,
.btn-success *,
.btn-warning,
.btn-warning *,
.btn-danger,
.btn-danger *,
.btn-info,
.btn-info *,
.btn-secondary,
.btn-secondary * {
    color: white !important;
}

/* استثناءات للبادجات */
.badge {
    color: white !important;
}

/* استثناءات للنوافذ المنبثقة */
.modal-header,
.modal-header * {
    color: white !important;
}

/* استثناءات لرؤوس الجداول */
.table thead th,
.table thead th * {
    color: white !important;
}

/* تحسين النصوص في المحتوى الرئيسي */
.main-content {
    color: #1a1a1a !important;
}

.main-content .card {
    color: #1a1a1a !important;
}

.main-content .card-body {
    color: #1a1a1a !important;
}

.main-content .table {
    color: #1a1a1a !important;
}

/* تحسين النصوص في النماذج */
.form-control {
    color: #1a1a1a !important;
}

.form-select {
    color: #1a1a1a !important;
}

/* تحسين النصوص في القوائم */
.list-group-item {
    color: #1a1a1a !important;
}

/* تحسين النصوص في التنبيهات */
.alert {
    color: #1a1a1a !important;
}

.alert-light {
    color: #1a1a1a !important;
}

/* تحسين النصوص في التبويبات */
.nav-tabs .nav-link {
    color: #1a1a1a !important;
}

.tab-content {
    color: #1a1a1a !important;
}

/* تحسين النصوص في الأكورديون */
.accordion-body {
    color: #1a1a1a !important;
}

.accordion-header {
    color: #1a1a1a !important;
}

/* تحسين النصوص في البطاقات المخصصة */
.store-info-card {
    color: #1a1a1a !important;
}

.store-info-card * {
    color: #1a1a1a !important;
}

.stats-card.primary,
.stats-card.info,
.stats-card.success,
.stats-card.warning {
    color: white !important;
}

.stats-card.primary *,
.stats-card.info *,
.stats-card.success *,
.stats-card.warning * {
    color: white !important;
}

/* تحسين إضافي للنصوص السوداء */

/* النصوص في الصفحات */
.container,
.container-fluid {
    color: #1a1a1a !important;
}

/* النصوص في الصفوف والأعمدة */
.row {
    color: #1a1a1a !important;
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-md, .col-lg, .col-xl, .col-xxl {
    color: #1a1a1a !important;
}

/* النصوص في العناصر المختلفة */
.breadcrumb {
    color: #1a1a1a !important;
}

.breadcrumb-item {
    color: #1a1a1a !important;
}

.breadcrumb-item a {
    color: #007bff !important;
}

/* النصوص في القوائم المنسدلة */
.dropdown-menu {
    color: #1a1a1a !important;
}

.dropdown-item {
    color: #1a1a1a !important;
}

/* النصوص في الصفحات المقسمة */
.pagination {
    color: #1a1a1a !important;
}

.page-link {
    color: #007bff !important;
}

/* النصوص في الشرائح */
.carousel-caption {
    color: white !important;
}

/* النصوص في البطاقات المتقدمة */
.card-deck .card,
.card-group .card,
.card-columns .card {
    color: #1a1a1a !important;
}

/* النصوص في الجداول المتقدمة */
.table-responsive {
    color: #1a1a1a !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    color: #1a1a1a !important;
}

.table-hover tbody tr:hover {
    color: #1a1a1a !important;
}

/* النصوص في النماذج المتقدمة */
.input-group {
    color: #1a1a1a !important;
}

.input-group-text {
    color: #1a1a1a !important;
}

/* النصوص في الأزرار المخططة */
.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-success,
.btn-outline-warning,
.btn-outline-danger,
.btn-outline-info,
.btn-outline-light,
.btn-outline-dark {
    color: #1a1a1a !important;
}

/* النصوص في الروابط */
a {
    color: #007bff !important;
}

a:hover {
    color: #0056b3 !important;
}

/* النصوص في العناصر المخصصة */
.text-decoration-none {
    color: #1a1a1a !important;
}

/* النصوص في الحاويات المخصصة */
.content-wrapper {
    color: #1a1a1a !important;
}

.page-content {
    color: #1a1a1a !important;
}

/* النصوص في العناصر التفاعلية */
.clickable {
    color: #1a1a1a !important;
}

.hoverable:hover {
    color: #1a1a1a !important;
}

/* تحسين النصوص في البطاقات الخاصة */
.order-status-card {
    color: #1a1a1a !important;
}

.order-status-card * {
    color: #1a1a1a !important;
}

/* استثناءات للعناصر الملونة الخاصة */
.order-status-card .order-status-icon {
    color: white !important;
}

.order-status-card .order-status-number {
    color: #1a1a1a !important;
}

.order-status-card .order-status-label {
    color: #1a1a1a !important;
}

/* تحسين النصوص في الصفحات المخصصة */
.dashboard-page {
    color: #1a1a1a !important;
}

.dashboard-page * {
    color: #1a1a1a !important;
}

/* استثناءات للعناصر الملونة في الصفحات */
.dashboard-page .sidebar,
.dashboard-page .sidebar * {
    color: rgba(255, 255, 255, 0.9) !important;
}

.dashboard-page .btn-primary,
.dashboard-page .btn-success,
.dashboard-page .btn-warning,
.dashboard-page .btn-danger,
.dashboard-page .btn-info,
.dashboard-page .btn-secondary {
    color: white !important;
}

.dashboard-page .badge {
    color: white !important;
}

/* تحسين النصوص في العناصر الديناميكية */
.dynamic-content {
    color: #1a1a1a !important;
}

.ajax-content {
    color: #1a1a1a !important;
}

/* تحسين النصوص في العناصر المحملة */
.loaded-content {
    color: #1a1a1a !important;
}

.rendered-content {
    color: #1a1a1a !important;
}

/* قواعد خاصة للنصوص في الجداول */
.table tbody td {
    color: #1a1a1a !important;
}

.table tbody td strong {
    color: #1a1a1a !important;
}

.table tbody td small {
    color: #666666 !important;
}

.table tbody td .text-muted {
    color: #666666 !important;
}

/* قواعد خاصة للنصوص في البطاقات */
.card .fw-bold {
    color: #1a1a1a !important;
}

.card .text-muted {
    color: #666666 !important;
}

.card small {
    color: #666666 !important;
}

/* قواعد خاصة للنصوص في النماذج */
.form-label {
    color: #1a1a1a !important;
    font-weight: 600 !important;
}

.form-control::placeholder {
    color: #999999 !important;
}

/* قواعد خاصة للنصوص في القوائم */
.list-group-item {
    color: #1a1a1a !important;
}

.list-group-item .text-muted {
    color: #666666 !important;
}

/* قواعد خاصة للنصوص في التنبيهات */
.alert {
    color: #1a1a1a !important;
}

.alert .alert-heading {
    color: #1a1a1a !important;
}

/* قواعد خاصة للنصوص في الأكورديون */
.accordion-button {
    color: #1a1a1a !important;
}

.accordion-body {
    color: #1a1a1a !important;
}

/* قواعد خاصة للنصوص في التبويبات */
.nav-tabs .nav-link {
    color: #1a1a1a !important;
}

.nav-tabs .nav-link.active {
    color: #1a1a1a !important;
}

.tab-pane {
    color: #1a1a1a !important;
}

/* قواعد خاصة للنصوص في البطاقات المخصصة */
.store-info-card .form-label {
    color: #666666 !important;
}

.store-info-card .fw-bold {
    color: #1a1a1a !important;
}

/* قواعد خاصة للنصوص في صفحة تفاصيل المتجر */
.store-detail-page {
    color: #1a1a1a !important;
}

.store-detail-page .card {
    color: #1a1a1a !important;
}

.store-detail-page .table {
    color: #1a1a1a !important;
}

/* قواعد خاصة للنصوص في الصفحات المختلفة */
.areas-page,
.companies-page,
.store-types-page,
.categories-page,
.customers-page,
.products-page,
.orders-page,
.stores-page {
    color: #1a1a1a !important;
}

.areas-page *,
.companies-page *,
.store-types-page *,
.categories-page *,
.customers-page *,
.products-page *,
.orders-page *,
.stores-page * {
    color: #1a1a1a !important;
}

/* استثناءات للعناصر الملونة في الصفحات */
.areas-page .btn-primary,
.companies-page .btn-primary,
.store-types-page .btn-primary,
.categories-page .btn-primary,
.customers-page .btn-primary,
.products-page .btn-primary,
.orders-page .btn-primary,
.stores-page .btn-primary {
    color: white !important;
}

.areas-page .badge,
.companies-page .badge,
.store-types-page .badge,
.categories-page .badge,
.customers-page .badge,
.products-page .badge,
.orders-page .badge,
.stores-page .badge {
    color: white !important;
}

/* تحسين النصوص في العناصر المتحركة */
.animated-element {
    color: #1a1a1a !important;
}

.fade-in {
    color: #1a1a1a !important;
}

.slide-in {
    color: #1a1a1a !important;
}

/* تحسين النصوص في العناصر المخفية والظاهرة */
.show {
    color: #1a1a1a !important;
}

.collapse.show {
    color: #1a1a1a !important;
}

/* تحسين النصوص في العناصر التفاعلية المتقدمة */
.interactive-element {
    color: #1a1a1a !important;
}

.interactive-element:hover {
    color: #1a1a1a !important;
}

.interactive-element:focus {
    color: #1a1a1a !important;
}

.interactive-element:active {
    color: #1a1a1a !important;
}

/* بطاقات حالات الطلبات المخصصة */
.order-status-card {
    background: var(--bg-light);
    border: 1px solid var(--glass-border-light);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-xs);
    margin-bottom: var(--spacing-md);
}

.order-status-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.order-status-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--status-color);
}

.order-status-content {
    flex: 1;
    text-align: right;
}

.order-status-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.order-status-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

.order-status-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-left: var(--spacing-md);
    color: white;
}

/* ألوان مخصصة جميلة وحيوية لحالات الطلبات */
.status-pending {
    --status-color: #f59e0b;
}

.status-pending .order-status-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.status-waiting_shipping {
    --status-color: #06b6d4;
}

.status-waiting_shipping .order-status-icon {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.status-shipped {
    --status-color: #2563eb;
}

.status-shipped .order-status-icon {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.status-on_way {
    --status-color: #8b5cf6;
}

.status-on_way .order-status-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.status-delivered {
    --status-color: #10b981;
}

.status-delivered .order-status-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.status-no_answer {
    --status-color: #ef4444;
}

.status-no_answer .order-status-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-postponed {
    --status-color: #6b7280;
}

.status-postponed .order-status-icon {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.status-wrong_address {
    --status-color: #ef4444;
}

.status-wrong_address .order-status-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* جداول احترافية وجميلة */
.table {
    background: var(--bg-light);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-xs);
    color: var(--text-primary);
}

.table thead th {
    background: var(--primary-gradient);
    color: var(--text-light);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.table tbody td {
    color: var(--text-primary);
    font-weight: 500;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--neutral-200);
}

.table tbody tr:hover {
    background: var(--bg-secondary);
}

/* تحسين النماذج */
.form-control {
    border: 2px solid var(--neutral-200);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    font-weight: 500;
    color: var(--text-primary);
    background: var(--bg-light);
    transition: var(--transition-normal);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: var(--bg-light);
    color: var(--text-primary);
}

.form-label {
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

/* أزرار احترافية وجادة */
.btn {
    border-radius: var(--border-radius-xs);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: var(--transition-normal);
    border: 1px solid transparent;
    font-size: var(--font-size-sm);
    text-transform: none;
    letter-spacing: 0.025em;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-primary);
    color: white;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-secondary);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: var(--success-dark);
    border-color: var(--success-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-success);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: var(--warning-dark);
    border-color: var(--warning-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-warning);
    color: white;
}

.btn-danger {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.btn-danger:hover {
    background: var(--accent-dark);
    border-color: var(--accent-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-danger);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
    border-color: var(--info-color);
}

.btn-info:hover {
    background: var(--info-dark);
    border-color: var(--info-dark);
    transform: translateY(-1px);
    color: white;
}

/* أزرار outline احترافية */
.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-primary);
}

.btn-outline-secondary {
    border: 1px solid var(--secondary-color);
    color: var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

/* مجموعات الأزرار */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius-xs) 0 0 var(--border-radius-xs);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius-xs) var(--border-radius-xs) 0;
}

.btn-group .btn:only-child {
    border-radius: var(--border-radius-xs);
}

/* تأثيرات النصوص المتدرجة */
.text-gradient-red {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.text-gradient-purple {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.text-gradient-teal {
    background: var(--teal-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* تأثيرات الحدود المتدرجة */
.border-gradient-red {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--primary-gradient) border-box;
}

.border-gradient-purple {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--secondary-gradient) border-box;
}

/* تأثيرات الظلال المتحركة */
.shadow-glow-red {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
    animation: glowRed 3s ease-in-out infinite alternate;
}

@keyframes glowRed {
    0% { box-shadow: 0 0 20px rgba(255, 107, 107, 0.3); }
    100% { box-shadow: 0 0 40px rgba(255, 107, 107, 0.6); }
}

.shadow-glow-purple {
    box-shadow: 0 0 20px rgba(165, 94, 234, 0.3);
    animation: glowPurple 3s ease-in-out infinite alternate;
}

@keyframes glowPurple {
    0% { box-shadow: 0 0 20px rgba(165, 94, 234, 0.3); }
    100% { box-shadow: 0 0 40px rgba(165, 94, 234, 0.6); }
}

/* تأثيرات الخلفيات المتحركة */
.bg-animated-red {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff6b6b, #ee5a24);
    background-size: 400% 400%;
    animation: gradientMove 4s ease infinite;
}

.bg-animated-purple {
    background: linear-gradient(45deg, #a55eea, #8b5cf6, #a55eea, #8b5cf6);
    background-size: 400% 400%;
    animation: gradientMove 4s ease infinite;
}

@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثيرات الأيقونات الملونة */
.icon-gradient-red {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5em;
}

.icon-gradient-purple {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5em;
}

/* تأثيرات البطاقات المحسنة */
.card-enhanced {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.card-enhanced:hover {
    transform: translateY(-10px) scale(1.02);
    background: var(--glass-hover);
}

/* جداول رسمية ومنظمة */
.table {
    background: var(--bg-light);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-lg);
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: var(--spacing-lg);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--bg-secondary);
    vertical-align: middle;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--bg-primary);
    transform: none;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* نماذج رسمية ومحسنة */
.form-control {
    border: 2px solid var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    background: var(--bg-light);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: var(--transition-normal);
    font-size: var(--font-size-md);
    color: var(--dark-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(44, 62, 80, 0.1);
    background: var(--bg-light);
    outline: none;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

/* تحسين select و textarea */
.form-select {
    border: 2px solid var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    background: var(--bg-light);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: var(--transition-normal);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(44, 62, 80, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* تأثيرات التنبيهات الملونة */
.alert-enhanced {
    border: none;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.alert-success-enhanced {
    background: linear-gradient(135deg, rgba(38, 222, 129, 0.2), rgba(32, 191, 107, 0.2));
    border-left: 4px solid #26de81;
}

.alert-danger-enhanced {
    background: linear-gradient(135deg, rgba(252, 92, 101, 0.2), rgba(235, 51, 73, 0.2));
    border-left: 4px solid #fc5c65;
}

.alert-warning-enhanced {
    background: linear-gradient(135deg, rgba(254, 211, 48, 0.2), rgba(247, 183, 49, 0.2));
    border-left: 4px solid #fed330;
}

.alert-info-enhanced {
    background: linear-gradient(135deg, rgba(55, 66, 250, 0.2), rgba(47, 53, 66, 0.2));
    border-left: 4px solid #3742fa;
}

/* حل مشاكل التداخل والتخطيط */
.container-fluid {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
}

.row {
    margin-left: calc(-1 * var(--spacing-sm));
    margin-right: calc(-1 * var(--spacing-sm));
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
}

/* تحسين الشريط الجانبي */
.sidebar {
    background: var(--primary-gradient);
    border-right: 1px solid var(--glass-border);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 320px;
    z-index: 1000;
    overflow-y: auto;
    transition: var(--transition-normal);
    box-shadow: -2px 0 15px rgba(0, 0, 0, 0.15);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: var(--spacing-md) var(--spacing-lg);
    margin: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-normal);
    display: flex;
    align-items: flex-start;
    text-decoration: none;
    flex-direction: column;
    min-height: 65px;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(-8px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.sidebar .nav-link .d-flex {
    display: flex !important;
    align-items: center;
    width: 100%;
}

.sidebar .nav-link i {
    margin-left: var(--spacing-md);
    width: 24px;
    text-align: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.sidebar .nav-link span:not(.text-white-50) {
    font-weight: 600;
    font-size: 0.95rem;
}

.sidebar .nav-link small {
    font-size: 0.75rem;
    margin-top: 2px;
    line-height: 1.2;
    opacity: 0.8;
    margin-right: 44px;
}

/* مؤشر التنقل */
.nav-indicator {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 0;
    background: var(--text-light);
    transition: var(--transition-normal);
    border-radius: 0 3px 3px 0;
}

.sidebar .nav-link:hover .nav-indicator,
.sidebar .nav-link.active .nav-indicator {
    height: 70%;
}

/* تحسين عناوين الأقسام */
.nav-section-title {
    font-size: 0.85rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: var(--spacing-sm);
}

.nav-section-title i {
    font-size: 1rem;
    opacity: 0.9;
}

/* تحسين المسافات */
.nav-section {
    margin-bottom: var(--spacing-xl) !important;
}

.nav-section:last-child {
    margin-bottom: var(--spacing-lg) !important;
}

/* تحسين المحتوى الرئيسي */
.main-content {
    margin-right: 320px;
    padding: var(--spacing-lg);
    min-height: 100vh;
    transition: var(--transition-normal);
}

/* تحسين الهيدر */
.navbar {
    background: #ffffff;
    border-bottom: 1px solid var(--glass-border-light);
    padding: var(--spacing-md) var(--spacing-lg);
    position: sticky;
    top: 0;
    z-index: 999;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين البطاقات لمنع التداخل */
.card-deck .card {
    margin-bottom: var(--spacing-lg);
}

.card-columns .card {
    margin-bottom: var(--spacing-lg);
    break-inside: avoid;
}

/* تحسين المودال */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    border-top: 1px solid var(--bg-secondary);
    padding: var(--spacing-lg) var(--spacing-xl);
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border-left-color: var(--success-color);
    color: var(--success-dark);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-left-color: var(--accent-color);
    color: var(--accent-dark);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border-left-color: var(--warning-color);
    color: var(--warning-dark);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: var(--info-dark);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
    }

    .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .stats-card {
        height: auto;
        padding: var(--spacing-lg);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .table {
        font-size: var(--font-size-sm);
    }

    .table thead th,
    .table tbody td {
        padding: var(--spacing-sm);
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .stats-card {
        margin-bottom: var(--spacing-md);
    }

    .btn-group .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }

    .card-header {
        padding: var(--spacing-md);
        font-size: var(--font-size-md);
    }
}

/* إصلاح مشاكل z-index */
.navbar {
    z-index: 1030;
}

.sidebar {
    z-index: 1020;
}

.modal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

.dropdown-menu {
    z-index: 1000;
}

/* تحسين التمرير */
.sidebar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* تحسين الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }

    .main-content {
        margin-right: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
