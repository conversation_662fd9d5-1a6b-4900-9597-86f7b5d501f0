{% extends 'dashboard/base.html' %}

{% block title %}أنواع المتاجر - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة أنواع المتاجر</h1>
        <p class="text-muted">عرض وإدارة جميع أنواع المتاجر في النظام</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="{% url 'store_types_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة نوع متجر جديد
            </a>
            <button type="button"
                    class="btn btn-success bulk-add-btn"
                    data-entity="store_types"
                    data-url="{% url 'bulk_add_store_types' %}"
                    data-fields='[{"name": "store_type", "label": "نوع المتجر", "type": "text", "required": true, "placeholder": "أدخل نوع المتجر"}]'>
                <i class="fas fa-layer-group me-2"></i>
                إضافة متعددة
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-store me-2"></i>
            قائمة أنواع المتاجر
        </h5>
    </div>
    <div class="card-body">
        {% if store_types %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>نوع المتجر</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for store_type in store_types %}
                        <tr>
                            <td>{{ store_type.id }}</td>
                            <td>{{ store_type.store_type }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'store_types_edit' store_type.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'store_types_delete' store_type.pk %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-store fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أنواع متاجر</h5>
                <p class="text-muted">لم يتم إضافة أي أنواع متاجر حتى الآن</p>
                <a href="{% url 'store_types_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول نوع متجر
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
