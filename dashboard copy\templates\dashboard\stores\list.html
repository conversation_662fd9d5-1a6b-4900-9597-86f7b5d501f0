{% extends 'dashboard/base.html' %}

{% block title %}المتاجر - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">المتاجر</li>
{% endblock %}

{% block extra_css %}
<style>
.stores-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.store-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.store-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.create-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.create-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    color: white;
}

.store-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-enabled { background: rgba(40, 167, 69, 0.9); color: white; }
.status-disabled { background: rgba(220, 53, 69, 0.9); color: white; }

.store-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 12px;
}

.store-placeholder {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-action {
    padding: 0.5rem;
    border-radius: 8px;
    border: none;
    transition: all 0.3s ease;
}

.btn-view { background: #e3f2fd; color: #1976d2; }
.btn-edit { background: #e8f5e8; color: #2e7d32; }
.btn-delete { background: #ffebee; color: #c62828; }

.btn-action:hover {
    transform: scale(1.1);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #28a745;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الصفحة -->
<div class="stores-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">
                <i class="fas fa-store me-2"></i>
                إدارة المتاجر
            </h2>
            <p class="mb-0 opacity-75">عرض وإدارة جميع المتاجر المسجلة في النظام</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex gap-2 justify-content-end">
                <a href="{% url 'stores_map' %}" class="btn btn-info">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    عرض الخريطة
                </a>
                <a href="{% url 'stores_create' %}" class="create-btn">
                    <i class="fas fa-plus me-2"></i>
                    إضافة متجر جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <h3 class="text-success mb-0">{{ stores.count }}</h3>
            <p class="text-muted mb-0">إجمالي المتاجر</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="border-left-color: #007bff;">
            <h3 class="text-primary mb-0">{{ stores|length }}</h3>
            <p class="text-muted mb-0">متاجر نشطة</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="border-left-color: #ffc107;">
            <h3 class="text-warning mb-0">0</h3>
            <p class="text-muted mb-0">متاجر معطلة</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="border-left-color: #17a2b8;">
            <h3 class="text-info mb-0">0</h3>
            <p class="text-muted mb-0">طلبات اليوم</p>
        </div>
    </div>
</div>

<!-- قائمة المتاجر -->
<div class="row">
    {% if stores %}
        {% for store in stores %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="store-card">
                <div class="card-body position-relative">
                    <span class="store-status {% if store.enable %}status-enabled{% else %}status-disabled{% endif %}">
                        {% if store.enable %}مفعل{% else %}معطل{% endif %}
                    </span>

                    <div class="d-flex align-items-start mb-3 mt-3">
                        {% if store.store_picture %}
                            <img src="{{ store.store_picture.url }}" alt="{{ store.store_name }}" class="store-image me-3">
                        {% else %}
                            <div class="store-placeholder me-3">
                                <i class="fas fa-store fa-lg"></i>
                            </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">{{ store.store_name }}</h6>
                            <small class="text-muted">{{ store.store_type.store_type }}</small>
                        </div>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ store.area.area_name }}
                        </small>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            {{ store.store_person_name }}
                        </small>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-phone me-1"></i>
                            {{ store.store_person_phone_number }}
                        </small>
                    </div>

                    <!-- الشارات -->
                    <div class="mb-3">
                        {% if store.is_static_in_main_screen %}
                            <span class="badge bg-warning me-1">مثبت</span>
                        {% endif %}
                        {% if store.is_found_24_for_food_stor %}
                            <span class="badge bg-info me-1">24 ساعة</span>
                        {% endif %}
                        {% if store.price_stor > 0 %}
                            <span class="badge bg-secondary me-1">{{ store.price_stor }} ريال يمني</span>
                        {% endif %}
                    </div>
                </div>

                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="action-buttons">
                            <a href="{% url 'stores_detail' store.pk %}"
                               class="btn btn-action btn-view" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'stores_edit' store.pk %}"
                               class="btn btn-action btn-edit" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'stores_delete' store.pk %}"
                               class="btn btn-action btn-delete" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>

                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ store.date_joined|date:"Y/m/d" }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-store fa-4x mb-3"></i>
                        <h5>لا توجد متاجر مسجلة</h5>
                        <p class="mb-4">ابدأ بإضافة أول متجر إلى النظام</p>
                        <a href="{% url 'stores_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول متجر
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
