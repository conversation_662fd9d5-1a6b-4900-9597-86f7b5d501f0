{% extends 'dashboard/base.html' %}

{% block title %}{{ offer.title }} - تفاصيل العرض{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'special_offers_list' %}">العروض الخاصة</a></li>
<li class="breadcrumb-item active">{{ offer.title }}</li>
{% endblock %}

{% block extra_css %}
<style>
.offer-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.offer-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-active { background: rgba(40, 167, 69, 0.9); }
.status-draft { background: rgba(108, 117, 125, 0.9); }
.status-paused { background: rgba(255, 193, 7, 0.9); }
.status-expired { background: rgba(220, 53, 69, 0.9); }
.status-cancelled { background: rgba(220, 53, 69, 0.9); }

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
}

.stat-card.usage { border-left-color: #007bff; }
.stat-card.discount { border-left-color: #28a745; }
.stat-card.customers { border-left-color: #17a2b8; }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.offer-details-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.detail-section {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.detail-value {
    color: #6c757d;
}

.offer-type-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.type-discount_percentage { background: #cce5ff; color: #004085; }
.type-discount_fixed { background: #d4edda; color: #155724; }
.type-buy_get { background: #fff3cd; color: #856404; }
.type-free_delivery { background: #e2e3e5; color: #383d41; }
.type-bundle { background: #f8d7da; color: #721c24; }
.type-seasonal { background: #d1ecf1; color: #0c5460; }

.usage-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #007bff;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-action {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
    color: white;
}

.offer-image {
    max-width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
}

.progress-bar-custom {
    height: 12px;
    border-radius: 6px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<!-- رأس العرض -->
<div class="offer-header">
    <span class="offer-status status-{{ offer.status }}">
        {{ offer.get_status_display }}
    </span>
    
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">{{ offer.title }}</h2>
            <p class="mb-1 opacity-75">{{ offer.description }}</p>
            <div class="mt-3">
                <span class="offer-type-badge type-{{ offer.offer_type }}">
                    {{ offer.get_offer_type_display }}
                </span>
                {% if offer.is_featured %}
                <span class="badge bg-warning text-dark ms-2">
                    <i class="fas fa-star me-1"></i>
                    مميز
                </span>
                {% endif %}
            </div>
        </div>
        <div class="col-md-4 text-end">
            <div class="action-buttons">
                <a href="{% url 'special_offer_edit' offer.pk %}" class="quick-action">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <button type="button" class="quick-action" data-bs-toggle="modal" data-bs-target="#statusModal">
                    <i class="fas fa-toggle-on me-2"></i>
                    تغيير الحالة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<div class="stats-grid">
    <div class="stat-card usage">
        <div class="stat-number text-primary">{{ usage_stats.total_usage|default:0 }}</div>
        <div class="stat-label">إجمالي الاستخدامات</div>
    </div>
    <div class="stat-card discount">
        <div class="stat-number text-success">{{ usage_stats.total_discount|default:0|floatformat:0 }}</div>
        <div class="stat-label">إجمالي الخصم (د.ع)</div>
    </div>
    <div class="stat-card customers">
        <div class="stat-number text-info">{{ usage_stats.unique_customers|default:0 }}</div>
        <div class="stat-label">عملاء مختلفين</div>
    </div>
    <div class="stat-card">
        <div class="stat-number text-warning">{{ offer.get_remaining_uses }}</div>
        <div class="stat-label">استخدامات متبقية</div>
    </div>
</div>

<!-- تفاصيل العرض -->
<div class="row">
    <div class="col-lg-8">
        <div class="offer-details-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل العرض
                </h5>
            </div>
            
            <!-- المعلومات الأساسية -->
            <div class="detail-section">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-label">نوع العرض:</div>
                        <div class="detail-value">{{ offer.get_offer_type_display }}</div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-label">الجمهور المستهدف:</div>
                        <div class="detail-value">{{ offer.get_target_audience_display }}</div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الخصم -->
            {% if offer.offer_type == 'discount_percentage' or offer.offer_type == 'discount_fixed' %}
            <div class="detail-section">
                <div class="row">
                    {% if offer.offer_type == 'discount_percentage' %}
                    <div class="col-md-4">
                        <div class="detail-label">نسبة الخصم:</div>
                        <div class="detail-value">{{ offer.discount_percentage }}%</div>
                    </div>
                    {% endif %}
                    {% if offer.offer_type == 'discount_fixed' %}
                    <div class="col-md-4">
                        <div class="detail-label">مبلغ الخصم:</div>
                        <div class="detail-value">{{ offer.discount_amount|floatformat:0 }} د.ع</div>
                    </div>
                    {% endif %}
                    {% if offer.maximum_discount > 0 %}
                    <div class="col-md-4">
                        <div class="detail-label">الحد الأقصى للخصم:</div>
                        <div class="detail-value">{{ offer.maximum_discount|floatformat:0 }} د.ع</div>
                    </div>
                    {% endif %}
                    {% if offer.minimum_order_value > 0 %}
                    <div class="col-md-4">
                        <div class="detail-label">الحد الأدنى للطلب:</div>
                        <div class="detail-value">{{ offer.minimum_order_value|floatformat:0 }} د.ع</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- تفاصيل اشتري واحصل على -->
            {% if offer.offer_type == 'buy_get' %}
            <div class="detail-section">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-label">اشتري كمية:</div>
                        <div class="detail-value">{{ offer.buy_quantity }}</div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-label">احصل على كمية:</div>
                        <div class="detail-value">{{ offer.get_quantity }}</div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الفترة الزمنية -->
            <div class="detail-section">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-label">تاريخ البداية:</div>
                        <div class="detail-value">{{ offer.start_date|date:"Y/m/d H:i" }}</div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-label">تاريخ النهاية:</div>
                        <div class="detail-value">{{ offer.end_date|date:"Y/m/d H:i" }}</div>
                    </div>
                </div>
                
                <!-- شريط التقدم الزمني -->
                <div class="mt-3">
                    <div class="detail-label">التقدم الزمني:</div>
                    <div class="progress-bar-custom">
                        {% now "U" as current_timestamp %}
                        {% with offer.start_date|date:"U"|add:0 as start_timestamp %}
                        {% with offer.end_date|date:"U"|add:0 as end_timestamp %}
                        {% with end_timestamp|sub:start_timestamp as total_duration %}
                        {% with current_timestamp|sub:start_timestamp as elapsed_duration %}
                        {% if total_duration > 0 %}
                            {% widthratio elapsed_duration total_duration 100 as progress_percentage %}
                            <div class="progress-fill" style="width: {{ progress_percentage|default:0 }}%"></div>
                        {% endif %}
                        {% endwith %}
                        {% endwith %}
                        {% endwith %}
                        {% endwith %}
                    </div>
                    <small class="text-muted">
                        {% if offer.end_date > "now"|date:"c" %}
                            ينتهي خلال {{ offer.end_date|timeuntil }}
                        {% else %}
                            انتهى منذ {{ offer.end_date|timesince }}
                        {% endif %}
                    </small>
                </div>
            </div>

            <!-- الحدود والقيود -->
            <div class="detail-section">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-label">حد الاستخدام لكل عميل:</div>
                        <div class="detail-value">{{ offer.usage_limit_per_customer }}</div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-label">حد الاستخدام الإجمالي:</div>
                        <div class="detail-value">
                            {% if offer.total_usage_limit > 0 %}
                                {{ offer.total_usage_limit }}
                            {% else %}
                                بلا حدود
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- كود الخصم -->
            {% if offer.requires_coupon_code %}
            <div class="detail-section">
                <div class="detail-label">كود الخصم:</div>
                <div class="detail-value">
                    <code class="bg-light p-2 rounded">{{ offer.coupon_code }}</code>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('{{ offer.coupon_code }}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
            {% endif %}

            <!-- الشروط والأحكام -->
            {% if offer.terms_and_conditions %}
            <div class="detail-section">
                <div class="detail-label">الشروط والأحكام:</div>
                <div class="detail-value">{{ offer.terms_and_conditions|linebreaks }}</div>
            </div>
            {% endif %}

            <!-- الملاحظات -->
            {% if offer.notes %}
            <div class="detail-section">
                <div class="detail-label">ملاحظات داخلية:</div>
                <div class="detail-value">{{ offer.notes|linebreaks }}</div>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="col-lg-4">
        <!-- الصور -->
        {% if offer.image or offer.banner_image %}
        <div class="offer-details-card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-images me-2"></i>
                    الصور
                </h6>
            </div>
            <div class="card-body">
                {% if offer.image %}
                <div class="mb-3">
                    <div class="detail-label">صورة العرض:</div>
                    <img src="{{ offer.image.url }}" class="offer-image" alt="صورة العرض">
                </div>
                {% endif %}
                {% if offer.banner_image %}
                <div class="mb-3">
                    <div class="detail-label">صورة البانر:</div>
                    <img src="{{ offer.banner_image.url }}" class="offer-image" alt="صورة البانر">
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- الاستهداف -->
        <div class="offer-details-card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bullseye me-2"></i>
                    الاستهداف
                </h6>
            </div>
            <div class="card-body">
                {% if offer.target_stores.exists %}
                <div class="mb-3">
                    <div class="detail-label">المتاجر المستهدفة:</div>
                    <div class="detail-value">
                        {% for store in offer.target_stores.all %}
                            <span class="badge bg-primary me-1">{{ store.store_name }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                {% if offer.target_areas.exists %}
                <div class="mb-3">
                    <div class="detail-label">المناطق المستهدفة:</div>
                    <div class="detail-value">
                        {% for area in offer.target_areas.all %}
                            <span class="badge bg-success me-1">{{ area.area_name }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                {% if offer.target_products.exists %}
                <div class="mb-3">
                    <div class="detail-label">المنتجات المستهدفة:</div>
                    <div class="detail-value">
                        {% for product in offer.target_products.all %}
                            <span class="badge bg-info me-1">{{ product.product_name }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                {% if not offer.target_stores.exists and not offer.target_areas.exists and not offer.target_products.exists %}
                <p class="text-muted">لا يوجد استهداف محدد - ينطبق على الجميع</p>
                {% endif %}
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="offer-details-card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <div class="detail-label">أنشأ بواسطة:</div>
                    <div class="detail-value">{{ offer.created_by.username }}</div>
                </div>
                <div class="mb-2">
                    <div class="detail-label">تاريخ الإنشاء:</div>
                    <div class="detail-value">{{ offer.created_date|date:"Y/m/d H:i" }}</div>
                </div>
                <div class="mb-2">
                    <div class="detail-label">آخر تحديث:</div>
                    <div class="detail-value">{{ offer.updated_date|date:"Y/m/d H:i" }}</div>
                </div>
                <div class="mb-2">
                    <div class="detail-label">عرض في الصفحة الرئيسية:</div>
                    <div class="detail-value">
                        {% if offer.show_on_homepage %}
                            <span class="badge bg-success">نعم</span>
                        {% else %}
                            <span class="badge bg-secondary">لا</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث الاستخدامات -->
{% if recent_usage %}
<div class="row mt-4">
    <div class="col-12">
        <div class="offer-details-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    أحدث الاستخدامات
                </h5>
            </div>
            <div class="card-body">
                {% for usage in recent_usage %}
                <div class="usage-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ usage.customer.customer_name }}</strong>
                            <br>
                            <small class="text-muted">
                                {% if usage.order %}
                                    طلب #{{ usage.order.id }}
                                {% endif %}
                                - {{ usage.usage_date|date:"Y/m/d H:i" }}
                            </small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success">{{ usage.discount_amount|floatformat:0 }} د.ع</strong>
                            <br>
                            <small class="text-muted">خصم مطبق</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Modal تغيير الحالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير حالة العرض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'special_offer_toggle_status' offer.pk %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة:</label>
                        <select name="status" class="form-select">
                            {% for value, label in offer.STATUS_CHOICES %}
                            <option value="{{ value }}" {% if value == offer.status %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تغيير الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('تم نسخ الكود: ' + text);
    });
}
</script>
{% endblock %}
