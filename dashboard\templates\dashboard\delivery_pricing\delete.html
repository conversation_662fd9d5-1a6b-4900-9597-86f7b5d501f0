{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}حذف نطاق التسعير{% endblock %}

{% block extra_css %}
<style>
.delete-warning {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.info-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e3e6f0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #5a5c69;
}

.info-value {
    color: #858796;
}

.distance-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: bold;
}

.price-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: bold;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-trash text-danger"></i>
            حذف نطاق التسعير
        </h1>
        <a href="{% url 'delivery_pricing_list' %}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- تحذير الحذف -->
            <div class="delete-warning">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h4 class="mb-3">تحذير: حذف نطاق التسعير</h4>
                <p class="mb-0">
                    هل أنت متأكد من رغبتك في حذف هذا النطاق؟ 
                    <br>
                    <strong>هذا الإجراء لا يمكن التراجع عنه!</strong>
                </p>
            </div>

            <!-- معلومات النطاق المراد حذفه -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-info-circle"></i>
                        معلومات النطاق المراد حذفه
                    </h6>
                </div>
                <div class="card-body">
                    <!-- معلومات النطاق -->
                    <div class="info-card">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-route"></i>
                            معلومات النطاق
                        </h6>
                        
                        <div class="info-row">
                            <span class="info-label">نطاق المسافة:</span>
                            <span class="distance-badge">{{ pricing.distance_range_km }}</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">المسافة بالمتر:</span>
                            <span class="info-value">{{ pricing.distance_range_text }}</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">سعر التوصيل:</span>
                            <span class="price-badge">{{ pricing.price_per_delivery }} ريال يمني</span>
                        </div>
                        
                        {% if pricing.description %}
                        <div class="info-row">
                            <span class="info-label">الوصف:</span>
                            <span class="info-value">{{ pricing.description }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="info-row">
                            <span class="info-label">الحالة:</span>
                            <span class="status-badge {% if pricing.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {% if pricing.is_active %}
                                <i class="fas fa-check-circle"></i> نشط
                                {% else %}
                                <i class="fas fa-pause-circle"></i> غير نشط
                                {% endif %}
                            </span>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="info-card">
                        <h6 class="text-secondary mb-3">
                            <i class="fas fa-cog"></i>
                            معلومات النظام
                        </h6>
                        
                        <div class="info-row">
                            <span class="info-label">تاريخ الإنشاء:</span>
                            <span class="info-value">{{ pricing.created_at|date:"Y/m/d H:i" }}</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">آخر تحديث:</span>
                            <span class="info-value">{{ pricing.updated_at|date:"Y/m/d H:i" }}</span>
                        </div>
                        
                        {% if pricing.created_by %}
                        <div class="info-row">
                            <span class="info-label">أنشئ بواسطة:</span>
                            <span class="info-value">{{ pricing.created_by.username }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- تأثير الحذف -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle"></i>
                            تأثير حذف هذا النطاق:
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم حذف النطاق نهائياً من النظام</li>
                            <li>لن يتمكن النظام من حساب تكلفة التوصيل للمسافات في هذا النطاق</li>
                            <li>ستحتاج لإنشاء نطاق جديد إذا أردت استعادة التسعير لهذه المسافات</li>
                            <li>قد يؤثر على حساب تكلفة الطلبات الجديدة</li>
                        </ul>
                    </div>

                    <!-- أزرار التأكيد -->
                    <div class="row">
                        <div class="col-md-6">
                            <form method="POST" onsubmit="return confirmDelete()">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger btn-block btn-lg">
                                    <i class="fas fa-trash"></i>
                                    نعم، احذف النطاق نهائياً
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'delivery_pricing_list' %}" class="btn btn-secondary btn-block btn-lg">
                                <i class="fas fa-times"></i>
                                لا، إلغاء العملية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    return confirm(
        'هل أنت متأكد من رغبتك في حذف نطاق التسعير هذا؟\n\n' +
        'النطاق: {{ pricing.distance_range_km }}\n' +
        'السعر: {{ pricing.price_per_delivery }} ريال\n\n' +
        'هذا الإجراء لا يمكن التراجع عنه!'
    );
}
</script>
{% endblock %}
