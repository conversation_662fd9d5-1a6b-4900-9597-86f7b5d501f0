{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تاريخ الطلبات{% endblock %}

{% block extra_css %}
<style>
.filter-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.order-row {
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 0;
    transition: background-color 0.3s ease;
}

.order-row:hover {
    background-color: #f8f9fc;
}

.order-row:last-child {
    border-bottom: none;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-new { background: #e3f2fd; color: #1976d2; }
.status-pending { background: #fff3e0; color: #f57c00; }
.status-accepted { background: #e8f5e8; color: #388e3c; }
.status-preparing { background: #fff8e1; color: #f9a825; }
.status-ready { background: #e1f5fe; color: #0288d1; }
.status-out_for_delivery { background: #f3e5f5; color: #7b1fa2; }
.status-delivered { background: #e8f5e8; color: #2e7d32; }
.status-cancelled { background: #ffebee; color: #d32f2f; }
.status-rejected { background: #fce4ec; color: #c2185b; }

.stats-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.export-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* فلاتر سريعة */
.quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.quick-filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    background: white;
    color: #5a5c69;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    text-decoration: none;
}

.quick-filter-btn:hover {
    background: #f8f9fc;
    border-color: #d1d3e2;
    text-decoration: none;
    color: #5a5c69;
}

.quick-filter-btn.active {
    background: #4e73df;
    color: white;
    border-color: #4e73df;
}

.quick-filter-btn.active:hover {
    color: white;
}

/* إخفاء الصفوف المفلترة */
.order-row.filtered-out {
    display: none !important;
}

@media (max-width: 768px) {
    .quick-filters {
        flex-direction: column;
    }

    .quick-filter-btn {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-history text-primary"></i>
            تاريخ الطلبات
        </h1>
        <div>
            <a href="{% url 'orders_dashboard' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-chart-line"></i>
                العودة للوحة الرئيسية
            </a>
            <button class="export-btn btn-sm ml-2" onclick="exportToCSV()">
                <i class="fas fa-download"></i>
                تصدير CSV
            </button>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-card">
        <h5 class="text-primary mb-3">
            <i class="fas fa-filter"></i>
            فلاتر البحث
        </h5>
        
        <form method="GET" class="row">
            <div class="col-md-3 mb-3">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" name="start_date" id="start_date" class="form-control" 
                       value="{{ start_date }}">
            </div>
            
            <div class="col-md-3 mb-3">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" name="end_date" id="end_date" class="form-control" 
                       value="{{ end_date }}">
            </div>
            
            <div class="col-md-3 mb-3">
                <label for="store" class="form-label">المتجر</label>
                <select name="store" id="store" class="form-control">
                    <option value="">جميع المتاجر</option>
                    {% for store in all_stores %}
                    <option value="{{ store }}" {% if store == current_store %}selected{% endif %}>
                        {{ store }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3 mb-3">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-control">
                    <option value="">جميع الحالات</option>
                    {% for status in all_statuses %}
                    <option value="{{ status }}" {% if status == current_status %}selected{% endif %}>
                        {% if status == 'new' %}جديد
                        {% elif status == 'pending' %}قيد الانتظار
                        {% elif status == 'accepted' %}مقبول
                        {% elif status == 'preparing' %}قيد التحضير
                        {% elif status == 'ready' %}جاهز
                        {% elif status == 'out_for_delivery' %}في الطريق
                        {% elif status == 'delivered' %}تم التوصيل
                        {% elif status == 'cancelled' %}ملغي
                        {% elif status == 'rejected' %}مرفوض
                        {% else %}{{ status }}
                        {% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    بحث
                </button>
                <a href="{% url 'orders_history' %}" class="btn btn-secondary ml-2">
                    <i class="fas fa-times"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- ملخص الإحصائيات -->
    <div class="stats-summary">
        <div class="row text-center">
            <div class="col-md-3">
                <h4>{{ stats.total_orders|default:0 }}</h4>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
            <div class="col-md-3">
                <h4>{{ stats.total_revenue|default:0|floatformat:0 }} ريال</h4>
                <p class="mb-0">إجمالي الإيرادات</p>
            </div>
            <div class="col-md-3">
                <h4>{{ stats.average_order_value|default:0|floatformat:0 }} ريال</h4>
                <p class="mb-0">متوسط قيمة الطلب</p>
            </div>
            <div class="col-md-3">
                <h4>{{ stats.orders_by_status.delivered|default:0 }}</h4>
                <p class="mb-0">طلبات مكتملة</p>
            </div>
        </div>
    </div>

    <!-- فلاتر سريعة للحالات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-filter"></i>
                فلترة سريعة حسب الحالة
            </h6>
        </div>
        <div class="card-body">
            <div class="quick-filters">
                <button class="quick-filter-btn active" data-status="all" onclick="quickFilterByStatus('all')">
                    <i class="fas fa-list"></i>
                    الكل ({{ orders|length }})
                </button>

                {% for status in all_statuses %}
                <button class="quick-filter-btn status-{{ status }}" data-status="{{ status }}" onclick="quickFilterByStatus('{{ status }}')">
                    <i class="fas fa-{% if status == 'new' %}plus{% elif status == 'pending' %}clock{% elif status == 'accepted' %}check{% elif status == 'preparing' %}utensils{% elif status == 'ready' %}bell{% elif status == 'out_for_delivery' %}truck{% elif status == 'delivered' %}check-circle{% elif status == 'cancelled' %}times{% elif status == 'rejected' %}ban{% else %}circle{% endif %}"></i>
                    {% if status == 'new' %}جديد
                    {% elif status == 'pending' %}قيد الانتظار
                    {% elif status == 'accepted' %}مقبول
                    {% elif status == 'preparing' %}قيد التحضير
                    {% elif status == 'ready' %}جاهز
                    {% elif status == 'out_for_delivery' %}في الطريق
                    {% elif status == 'delivered' %}تم التوصيل
                    {% elif status == 'cancelled' %}ملغي
                    {% elif status == 'rejected' %}مرفوض
                    {% else %}{{ status }}
                    {% endif %}
                </button>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- قائمة الطلبات -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i>
                نتائج البحث (<span id="visibleOrdersCount">{{ orders|length }}</span> طلب)
            </h6>
        </div>
        <div class="card-body p-0">
            {% for order in orders %}
            <div class="order-row" data-status="{{ order.status }}">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <strong>{{ order.store_name }}</strong>
                        <br>
                        <small class="text-muted">#{{ order.order_id }}</small>
                    </div>
                    
                    <div class="col-md-2">
                        <span class="status-badge status-{{ order.status }}">
                            {{ order.status_arabic }}
                        </span>
                    </div>
                    
                    <div class="col-md-2">
                        <strong>{{ order.customerName }}</strong>
                        <br>
                        <small class="text-muted">{{ order.customerPhone }}</small>
                    </div>
                    
                    <div class="col-md-2">
                        <strong>{{ order.storeSubtotal }} ريال</strong>
                        <br>
                        <small class="text-muted">{{ order.payment_method_arabic }}</small>
                    </div>
                    
                    <div class="col-md-2">
                        <small>{{ order.createdAt_formatted }}</small>
                        <br>
                        <small class="text-muted">{{ order.delivery_area }}</small>
                    </div>
                    
                    <div class="col-md-2 text-right">
                        <a href="{% url 'order_details' order.store_name order.order_id %}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i>
                            تفاصيل
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">لا توجد نتائج</h5>
                <p class="text-gray-500">لم يتم العثور على طلبات تطابق معايير البحث</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    {% if stats.orders_by_store %}
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-store"></i>
                        أكثر المتاجر طلباً
                    </h6>
                </div>
                <div class="card-body">
                    {% for store, count in stats.orders_by_store.items %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ store }}</span>
                        <span class="badge badge-primary">{{ count }} طلب</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-utensils"></i>
                        أكثر المنتجات طلباً
                    </h6>
                </div>
                <div class="card-body">
                    {% for product, count in stats.top_products.items %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ product }}</span>
                        <span class="badge badge-success">{{ count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// تصدير البيانات إلى CSV
function exportToCSV() {
    const orders = [
        {% for order in orders %}
        {
            store: "{{ order.store_name }}",
            orderId: "{{ order.order_id }}",
            customer: "{{ order.customerName }}",
            phone: "{{ order.customerPhone }}",
            status: "{{ order.status_arabic }}",
            total: {{ order.storeSubtotal }},
            payment: "{{ order.payment_method_arabic }}",
            date: "{{ order.createdAt_formatted }}",
            area: "{{ order.delivery_area }}"
        },
        {% endfor %}
    ];
    
    if (orders.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // إنشاء محتوى CSV
    let csvContent = "المتجر,رقم الطلب,العميل,الهاتف,الحالة,المبلغ,طريقة الدفع,التاريخ,المنطقة\n";
    
    orders.forEach(order => {
        csvContent += `"${order.store}","${order.orderId}","${order.customer}","${order.phone}","${order.status}",${order.total},"${order.payment}","${order.date}","${order.area}"\n`;
    });
    
    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `orders_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// فلترة سريعة حسب الحالة
function quickFilterByStatus(status) {
    // تحديث الأزرار
    document.querySelectorAll('.quick-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    const activeBtn = document.querySelector(`[data-status="${status}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // فلترة الصفوف
    const orderRows = document.querySelectorAll('.order-row');
    let visibleCount = 0;

    orderRows.forEach(row => {
        const orderStatus = row.getAttribute('data-status');

        if (status === 'all' || orderStatus === status) {
            row.classList.remove('filtered-out');
            row.style.display = 'block';
            visibleCount++;
        } else {
            row.classList.add('filtered-out');
            row.style.display = 'none';
        }
    });

    // تحديث عداد الطلبات المرئية
    const countElement = document.getElementById('visibleOrdersCount');
    if (countElement) {
        countElement.textContent = visibleCount;
    }
}

// تحديد تاريخ اليوم كحد أقصى
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').max = today;
    document.getElementById('end_date').max = today;
});
</script>
{% endblock %}
