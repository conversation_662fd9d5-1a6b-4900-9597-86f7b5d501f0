<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة تحكم نظام توصيل الطلبات{% endblock %}</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Sweet Alert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Custom Colors CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'dashboard/css/custom-colors.css' %}">
    
    <style>
        /* تم نقل متغيرات الألوان إلى ملف CSS منفصل */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* تم نقل أنماط body إلى ملف CSS منفصل */

        /* تم إزالة الرسوم المتحركة للخلفية */

        /* Glassmorphism Effect */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
        }

        /* خلفية بيضاء نظيفة */
        body {
            background: #ffffff !important;
        }

        /* Sidebar Mega Design */
        .sidebar {
            min-height: 100vh;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border-left: 1px solid var(--glass-border);
            box-shadow: var(--shadow-heavy);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            opacity: 0.1;
            z-index: -1;
        }

        .sidebar-header {
            padding: 30px 20px;
            text-align: center;
            border-bottom: 1px solid var(--glass-border);
            background: var(--glass-bg);
        }

        .sidebar-logo {
            font-size: 2rem;
            font-weight: 900;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 18px 25px;
            margin: 8px 15px;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            font-weight: 500;
            font-size: 1.1rem;
            backdrop-filter: blur(10px);
            border: 1px solid transparent;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: var(--transition);
            z-index: -1;
        }

        .sidebar .nav-link:hover::before,
        .sidebar .nav-link.active::before {
            left: 0;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            transform: translateX(-10px) scale(1.05);
            box-shadow: var(--shadow-light);
            border-color: var(--glass-border);
        }

        .sidebar .nav-link i {
            margin-left: 15px;
            width: 25px;
            font-size: 1.2rem;
            transition: var(--transition);
        }

        .sidebar .nav-link:hover i {
            transform: rotate(360deg) scale(1.2);
        }

        /* Main Content Area */
        .main-content {
            padding: 30px;
            min-height: 100vh;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Mega Cards */
        .card {
            border: none;
            border-radius: var(--border-radius);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            overflow: hidden;
            position: relative;
            border: 1px solid var(--glass-border);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            padding: 25px 30px;
            font-weight: 700;
            font-size: 1.3rem;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .card-body {
            padding: 30px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }

        /* Mega Buttons */
        .btn {
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: none;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: var(--transition);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.6);
            color: white;
            background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
        }

        .btn-secondary {
            background: var(--secondary-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(165, 94, 234, 0.4);
            border: none;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
            color: white;
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(38, 222, 129, 0.4);
            border: none;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #00c851 0%, #00695c 100%);
            color: white;
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(254, 211, 48, 0.4);
            border: none;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #ffb300 0%, #ff8f00 100%);
            color: white;
        }

        .btn-danger {
            background: var(--danger-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(252, 92, 101, 0.4);
            border: none;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
            color: white;
        }

        .btn-info {
            background: var(--info-gradient);
            color: white;
            box-shadow: 0 5px 15px rgba(55, 66, 250, 0.4);
            border: none;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
            color: white;
        }

        /* Mega Tables */
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-light);
        }

        .table thead th {
            background: var(--primary-gradient);
            color: white;
            border: none;
            font-weight: 700;
            font-size: 1.1rem;
            padding: 20px 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .table tbody tr {
            transition: var(--transition);
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .table tbody tr:hover {
            background: var(--glass-bg);
            transform: scale(1.01);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table tbody td {
            padding: 18px 15px;
            vertical-align: middle;
            font-weight: 500;
        }

        /* Mega Navigation */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border-bottom: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
            padding: 20px 0;
        }

        .navbar-brand {
            font-weight: 900;
            font-size: 1.8rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 600;
            padding: 10px 20px !important;
            border-radius: 10px;
            transition: var(--transition);
            margin: 0 5px;
        }

        .navbar-nav .nav-link:hover {
            background: var(--glass-bg);
            transform: translateY(-2px);
            color: white !important;
        }

        /* Mega Alerts */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 20px 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(67, 233, 123, 0.2), rgba(56, 249, 215, 0.2));
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(250, 112, 154, 0.2), rgba(254, 225, 64, 0.2));
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 235, 59, 0.2));
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.2), rgba(0, 242, 254, 0.2));
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        /* Mega Stats Cards */
        .stats-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            box-shadow: var(--shadow-light);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            opacity: 0.1;
            z-index: -1;
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: var(--shadow-heavy);
        }

        .stats-card .stats-number {
            font-size: 3.5rem;
            font-weight: 900;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
            margin-bottom: 10px;
            animation: numberGlow 3s ease-in-out infinite alternate;
        }

        @keyframes numberGlow {
            0% { text-shadow: 0 0 20px rgba(255, 107, 107, 0.5); }
            100% { text-shadow: 0 0 40px rgba(255, 107, 107, 0.8), 0 0 60px rgba(165, 94, 234, 0.4); }
        }

        .stats-card .stats-label {
            font-size: 1.3rem;
            font-weight: 600;
            color: rgba(255,255,255,0.9);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stats-card .stats-icon {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 3rem;
            opacity: 0.3;
            color: white;
        }

        /* Mega Forms */
        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid var(--glass-border);
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            font-size: 1.1rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
            background: white;
            transform: scale(1.02);
        }

        .form-label {
            font-weight: 700;
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Mega Badges */
        .badge {
            border-radius: 10px;
            padding: 8px 15px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Loading Animation */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 1000;
                width: 280px;
                transition: var(--transition);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                padding: 15px;
            }

            .stats-card .stats-number {
                font-size: 2.5rem;
            }

            .card-body {
                padding: 20px;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--glass-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-gradient);
        }

        /* Navigation Sections */
        .nav-section-title {
            font-size: 0.85rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
        }

        .nav-indicator {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background: white;
            border-radius: 2px;
            transition: var(--transition);
        }

        .nav-link.active .nav-indicator,
        .nav-link:hover .nav-indicator {
            height: 60%;
        }

        /* Floating Action Button */
        .fab-container {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .fab-main {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-gradient);
            border: none;
            box-shadow: var(--shadow-heavy);
            transition: var(--transition);
            animation: pulse 2s infinite;
        }

        .fab-main:hover {
            transform: scale(1.1) rotate(45deg);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.6);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
            100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
        }

        /* Breadcrumb */
        .breadcrumb {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            margin-bottom: 0;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
            color: rgba(255,255,255,0.7);
        }

        .breadcrumb-item a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: var(--transition);
        }

        .breadcrumb-item a:hover {
            color: white;
            transform: translateX(-2px);
        }

        /* Page Header */
        .page-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-light);
        }

        .page-header h1 {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            margin-bottom: 10px;
        }

        .page-header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        /* Particles Animation */
        .particles-container {
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            pointer-events: none;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Enhanced Dropdown */
        .dropdown-menu {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            padding: 15px 0;
        }

        .dropdown-item {
            color: rgba(255,255,255,0.9);
            padding: 12px 20px;
            transition: var(--transition);
            border-radius: 0;
        }

        .dropdown-item:hover {
            background: var(--glass-bg);
            color: white;
            transform: translateX(-5px);
        }

        .dropdown-header {
            color: rgba(255,255,255,0.7);
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.85rem;
            padding: 10px 20px 15px;
            border-bottom: 1px solid var(--glass-border);
            margin-bottom: 10px;
        }

        /* Modal Enhancements */
        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
        }

        .modal-header {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            border-bottom: none;
            padding: 25px 30px;
        }

        .modal-body {
            padding: 30px;
            background: rgba(255,255,255,0.95);
        }

        /* Sidebar Footer */
        .sidebar-footer {
            margin-top: auto;
            padding: 20px;
        }

        .sidebar-footer .glass {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* Enhanced Input Groups */
        .input-group .form-control {
            background: rgba(255,255,255,0.9);
            border: 1px solid var(--glass-border);
            border-radius: 15px 0 0 15px;
        }

        .input-group .btn {
            border-radius: 0 15px 15px 0;
            border: 1px solid var(--glass-border);
            border-left: none;
        }

        /* Notification Badge */
        .badge {
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-3px);
            }
            60% {
                transform: translateY(-1px);
            }
        }

        /* Loading States */
        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- شريط تحميل بسيط وسريع -->
    <div id="page-loader" class="page-loader">
        <div class="loader-bar"></div>
    </div>

    <style>
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            z-index: 9999;
            background: rgba(255, 255, 255, 0.1);
        }

        .loader-bar {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            animation: loadProgress 1s ease-out forwards;
        }

        @keyframes loadProgress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .page-loader.loaded {
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }
    </style>

    <!-- Floating Particles Background -->
    <div class="particles-container position-fixed w-100 h-100" style="z-index: -2;">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid">
            <button class="btn btn-outline-light d-lg-none me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>

            <a class="navbar-brand animate__animated animate__fadeInDown" href="{% url 'dashboard_home' %}">
                <i class="fas fa-rocket me-2"></i>
                نظام توصيل الطلبات المتطور
            </a>

            <!-- Search Bar -->
            <div class="d-none d-md-flex flex-grow-1 mx-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في النظام..." id="globalSearch">
                    <button class="btn btn-outline-light" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- Notifications & User Menu -->
            <div class="navbar-nav d-flex flex-row">
                <!-- Notifications -->
                <div class="nav-item dropdown me-3">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            3
                            <span class="visually-hidden">إشعارات جديدة</span>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end glass" style="width: 300px;">
                        <li class="dropdown-header">الإشعارات الحديثة</li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-shopping-cart me-2"></i>طلب جديد #1234</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>عميل جديد انضم</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-store me-2"></i>متجر جديد مُضاف</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
                    </ul>
                </div>

                <!-- User Menu -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="rounded-circle bg-light me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                            <i class="fas fa-user text-primary"></i>
                        </div>
                        <span class="d-none d-md-inline">{{ user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end glass">
                        <li class="dropdown-header">مرحباً {{ user.get_full_name|default:user.username }}</li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><a class="dropdown-item" href="/admin/"><i class="fas fa-tools me-2"></i>لوحة الإدارة</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <!-- Mega Sidebar -->
            <div class="col-lg-3 col-xl-2 px-0">
                <div class="sidebar" id="sidebar">
                    <!-- Sidebar Header -->
                    <div class="sidebar-header">
                        <div class="sidebar-logo animate__animated animate__fadeInDown">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h6 class="text-white-50 mb-0">لوحة التحكم المتطورة</h6>
                    </div>

                    <!-- Navigation Menu -->
                    <nav class="nav flex-column p-3">
                        <!-- Dashboard -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                <span class="fw-bold">الرئيسية</span>
                            </h6>
                            <a class="nav-link" href="{% url 'dashboard_home' %}" data-aos="fade-left" data-aos-delay="100">
                                <i class="fas fa-home"></i>
                                <span>لوحة المعلومات</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- Management -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-cogs me-2"></i>
                                <span class="fw-bold">إدارة البيانات الأساسية</span>
                            </h6>
                            <a class="nav-link" href="{% url 'areas_list' %}" data-aos="fade-left" data-aos-delay="200">
                                <div class="d-flex">
                                    <i class="fas fa-map-marker-alt text-warning"></i>
                                    <span>المناطق</span>
                                </div>
                                <small class="text-white-50">إدارة المناطق الجغرافية</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'store_types_list' %}" data-aos="fade-left" data-aos-delay="250">
                                <div class="d-flex">
                                    <i class="fas fa-store text-info"></i>
                                    <span>أنواع المتاجر</span>
                                </div>
                                <small class="text-white-50">تصنيفات المتاجر</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'companies_list' %}" data-aos="fade-left" data-aos-delay="300">
                                <div class="d-flex">
                                    <i class="fas fa-building text-primary"></i>
                                    <span>الشركات</span>
                                </div>
                                <small class="text-white-50">إدارة الشركات المصنعة</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'stores_list' %}" data-aos="fade-left" data-aos-delay="350">
                                <div class="d-flex">
                                    <i class="fas fa-shop text-success"></i>
                                    <span>المتاجر</span>
                                </div>
                                <small class="text-white-50">جميع المتاجر المسجلة</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- Products -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-boxes me-2"></i>
                                <span class="fw-bold">إدارة المنتجات</span>
                            </h6>
                            <a class="nav-link" href="{% url 'categories_list' %}" data-aos="fade-left" data-aos-delay="400">
                                <div class="d-flex">
                                    <i class="fas fa-tags text-warning"></i>
                                    <span>الفئات</span>
                                </div>
                                <small class="text-white-50">تصنيفات المنتجات</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'products_list' %}" data-aos="fade-left" data-aos-delay="450">
                                <div class="d-flex">
                                    <i class="fas fa-box text-info"></i>
                                    <span>المنتجات</span>
                                </div>
                                <small class="text-white-50">كتالوج المنتجات</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- Orders & Customers -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-shopping-cart me-2"></i>
                                <span class="fw-bold">المبيعات والطلبات</span>
                            </h6>
                            <a class="nav-link" href="{% url 'customers_list' %}" data-aos="fade-left" data-aos-delay="500">
                                <div class="d-flex">
                                    <i class="fas fa-users text-primary"></i>
                                    <span>العملاء</span>
                                </div>
                                <small class="text-white-50">قاعدة بيانات العملاء</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'orders_list' %}" data-aos="fade-left" data-aos-delay="550">
                                <div class="d-flex">
                                    <i class="fas fa-shopping-cart text-success"></i>
                                    <span>الطلبات</span>
                                </div>
                                <small class="text-white-50">إدارة جميع الطلبات</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'baskets_list' %}" data-aos="fade-left" data-aos-delay="600">
                                <div class="d-flex">
                                    <i class="fas fa-basket-shopping text-warning"></i>
                                    <span>السلال</span>
                                </div>
                                <small class="text-white-50">سلال التسوق النشطة</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- Order Status -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-clipboard-list me-2"></i>
                                <span class="fw-bold">حالات الطلبات</span>
                            </h6>
                            <a class="nav-link" href="{% url 'orders_list' %}?status=pending" data-aos="fade-left" data-aos-delay="650">
                                <div class="d-flex">
                                    <i class="fas fa-clock text-warning"></i>
                                    <span>يتم التجهيز</span>
                                </div>
                                <small class="text-white-50">الطلبات قيد التجهيز</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'orders_list' %}?status=waiting_shipping" data-aos="fade-left" data-aos-delay="700">
                                <div class="d-flex">
                                    <i class="fas fa-hourglass-half text-info"></i>
                                    <span>في انتظار الشحن</span>
                                </div>
                                <small class="text-white-50">جاهزة للشحن</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'orders_list' %}?status=shipped" data-aos="fade-left" data-aos-delay="750">
                                <div class="d-flex">
                                    <i class="fas fa-shipping-fast text-primary"></i>
                                    <span>تم الشحن</span>
                                </div>
                                <small class="text-white-50">في طريق التوصيل</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'orders_list' %}?status=delivered" data-aos="fade-left" data-aos-delay="800">
                                <div class="d-flex">
                                    <i class="fas fa-check-circle text-success"></i>
                                    <span>تم الاستلام</span>
                                </div>
                                <small class="text-white-50">الطلبات المكتملة</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- Communications -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-bullhorn me-2"></i>
                                <span class="fw-bold">التواصل والإشعارات</span>
                            </h6>
                            <a class="nav-link" href="{% url 'notifications_list' %}" data-aos="fade-left" data-aos-delay="850">
                                <div class="d-flex">
                                    <i class="fas fa-bell text-warning"></i>
                                    <span>الإشعارات</span>
                                </div>
                                <small class="text-white-50">رسائل النظام</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- Reports & Analytics -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-chart-bar me-2"></i>
                                <span class="fw-bold">التقارير والإحصائيات</span>
                            </h6>
                            <a class="nav-link" href="{% url 'dashboard_home' %}#stats" data-aos="fade-left" data-aos-delay="900">
                                <div class="d-flex">
                                    <i class="fas fa-chart-pie text-info"></i>
                                    <span>إحصائيات عامة</span>
                                </div>
                                <small class="text-white-50">نظرة شاملة على النظام</small>
                                <div class="nav-indicator"></div>
                            </a>
                            <a class="nav-link" href="{% url 'dashboard_home' %}#orders-stats" data-aos="fade-left" data-aos-delay="950">
                                <div class="d-flex">
                                    <i class="fas fa-chart-line text-success"></i>
                                    <span>تقارير الطلبات</span>
                                </div>
                                <small class="text-white-50">تحليل حالات الطلبات</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>

                        <!-- System -->
                        <div class="nav-section mb-4">
                            <h6 class="nav-section-title text-white mb-3 px-3">
                                <i class="fas fa-cog me-2"></i>
                                <span class="fw-bold">إدارة النظام</span>
                            </h6>
                            <a class="nav-link" href="/admin/" target="_blank" data-aos="fade-left" data-aos-delay="1000">
                                <div class="d-flex">
                                    <i class="fas fa-tools text-danger"></i>
                                    <span>لوحة الإدارة</span>
                                </div>
                                <small class="text-white-50">إعدادات متقدمة</small>
                                <div class="nav-indicator"></div>
                            </a>
                        </div>
                    </nav>

                    <!-- Sidebar Footer -->
                    <div class="sidebar-footer p-3 mt-auto">
                        <div class="glass p-3 rounded text-center">
                            <i class="fas fa-chart-line fa-2x text-white mb-2"></i>
                            <h6 class="text-white mb-1">إحصائيات اليوم</h6>
                            <small class="text-white-50">{{ total_orders|default:0 }} طلب جديد</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9 col-xl-10">
                <div class="main-content">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb glass p-3 rounded">
                            <li class="breadcrumb-item">
                                <a href="{% url 'dashboard_home' %}" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>الرئيسية
                                </a>
                            </li>
                            {% block breadcrumb %}{% endblock %}
                        </ol>
                    </nav>

                    <!-- Top Navigation Bar -->
                    <nav class="top-nav-bar mb-4">
                        <div class="glass p-3 rounded">
                            <div class="row align-items-center">
                                <div class="col-md-10">
                                    <div class="top-nav-links">
                                        <a href="{% url 'dashboard_home' %}" class="top-nav-link">
                                            <i class="fas fa-home"></i>
                                            <span>الرئيسية</span>
                                        </a>
                                        <a href="{% url 'products_list' %}" class="top-nav-link">
                                            <i class="fas fa-box"></i>
                                            <span>المنتجات</span>
                                        </a>
                                        <a href="{% url 'customers_list' %}" class="top-nav-link">
                                            <i class="fas fa-users"></i>
                                            <span>العملاء</span>
                                        </a>
                                        <a href="{% url 'orders_list' %}" class="top-nav-link">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span>الطلبات</span>
                                        </a>
                                        <a href="{% url 'stores_list' %}" class="top-nav-link">
                                            <i class="fas fa-store"></i>
                                            <span>المتاجر</span>
                                        </a>
                                        <a href="{% url 'stores_map' %}" class="top-nav-link">
                                            <i class="fas fa-map-marked-alt"></i>
                                            <span>خريطة المتاجر</span>
                                        </a>
                                        <a href="{% url 'areas_list' %}" class="top-nav-link">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>المناطق</span>
                                        </a>
                                        <a href="{% url 'categories_list' %}" class="top-nav-link">
                                            <i class="fas fa-tags"></i>
                                            <span>الفئات</span>
                                        </a>
                                        <a href="{% url 'companies_list' %}" class="top-nav-link">
                                            <i class="fas fa-building"></i>
                                            <span>الشركات</span>
                                        </a>
                                        <a href="{% url 'store_types_list' %}" class="top-nav-link">
                                            <i class="fas fa-store-alt"></i>
                                            <span>أنواع المتاجر</span>
                                        </a>
                                        <a href="{% url 'notifications_list' %}" class="top-nav-link">
                                            <i class="fas fa-bell"></i>
                                            <span>الإشعارات</span>
                                        </a>
                                        <a href="{% url 'accounting_dashboard' %}" class="top-nav-link">
                                            <i class="fas fa-calculator"></i>
                                            <span>المحاسبة</span>
                                        </a>
                                        <a href="{% url 'special_offers_list' %}" class="top-nav-link">
                                            <i class="fas fa-tags"></i>
                                            <span>العروض الخاصة</span>
                                        </a>
                                        <a href="{% url 'delivery_pricing_list' %}" class="top-nav-link">
                                            <i class="fas fa-calculator"></i>
                                            <span>تسعير التوصيل</span>
                                        </a>
                                        <a href="{% url 'orders_dashboard' %}" class="top-nav-link">
                                            <i class="fas fa-chart-line"></i>
                                            <span>متابعة الطلبات</span>
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="top-nav-actions">
                                        <span class="welcome-text">{{ user.username }}</span>
                                        <a href="{% url 'admin:logout' %}" class="btn btn-outline-danger btn-sm ms-2" title="تسجيل الخروج">
                                            <i class="fas fa-sign-out-alt"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </nav>

                    <!-- Messages with Animation -->
                    {% if messages %}
                        <div class="messages-container mb-4">
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show animate__animated animate__fadeInDown" role="alert" data-aos="fade-down">
                                    <i class="fas fa-check-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Page Header -->
                    <div class="page-header mb-4" data-aos="fade-up">
                        {% block page_header %}{% endblock %}
                    </div>

                    <!-- Page Content -->
                    <div class="page-content" data-aos="fade-up" data-aos-delay="200">
                        {% block content %}{% endblock %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <div class="fab-container">
        <button class="fab-main btn btn-primary rounded-circle shadow-lg" data-bs-toggle="modal" data-bs-target="#quickActionsModal">
            <i class="fas fa-plus fa-lg"></i>
        </button>
    </div>

    <!-- Quick Actions Modal -->
    <div class="modal fade" id="quickActionsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content glass">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a href="{% url 'products_create' %}" class="btn btn-outline-primary w-100 p-4">
                                <i class="fas fa-plus fa-2x mb-2 d-block"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'categories_create' %}" class="btn btn-outline-success w-100 p-4">
                                <i class="fas fa-tags fa-2x mb-2 d-block"></i>
                                إضافة فئة جديدة
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'notifications_create' %}" class="btn btn-outline-warning w-100 p-4">
                                <i class="fas fa-bell fa-2x mb-2 d-block"></i>
                                إضافة إشعار
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="/admin/" class="btn btn-outline-info w-100 p-4">
                                <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                لوحة الإدارة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="btn btn-primary rounded-circle shadow-lg position-fixed" id="backToTop" style="bottom: 20px; left: 20px; width: 50px; height: 50px; display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Scripts -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JavaScript -->
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // شريط التحميل البسيط
        document.addEventListener('DOMContentLoaded', function() {
            const pageLoader = document.getElementById('page-loader');
            if (pageLoader) {
                setTimeout(() => {
                    pageLoader.classList.add('loaded');
                    setTimeout(() => {
                        pageLoader.style.display = 'none';
                    }, 300);
                }, 1200);
            }
        });

        // Sidebar Toggle for Mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        });

        // Active Navigation Link
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar .nav-link');
        navLinks.forEach(link => {
            // إزالة الفئة النشطة من جميع الروابط
            link.classList.remove('active');

            // إضافة الفئة النشطة للرابط الحالي
            const linkPath = link.getAttribute('href');
            if (linkPath === currentPath ||
                (linkPath !== '/' && currentPath.startsWith(linkPath))) {
                link.classList.add('active');
            }
        });

        // Back to Top Button
        const backToTopBtn = document.getElementById('backToTop');
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
            } else {
                backToTopBtn.style.display = 'none';
            }
        });

        backToTopBtn?.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Global Search
        document.getElementById('globalSearch')?.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value;
                if (searchTerm.trim()) {
                    // Implement global search functionality
                    Swal.fire({
                        title: 'البحث',
                        text: `البحث عن: ${searchTerm}`,
                        icon: 'info',
                        confirmButtonText: 'موافق'
                    });
                }
            }
        });

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Floating Particles Animation
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(255,255,255,0.5);
                border-radius: 50%;
                pointer-events: none;
                animation: float 6s linear infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 6}s;
            `;
            document.querySelector('.particles-container')?.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 500);

        // Enhanced Card Hover Effects
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Smooth Page Transitions
        document.querySelectorAll('a[href^="/"]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (!this.target || this.target === '_self') {
                    e.preventDefault();
                    const href = this.getAttribute('href');

                    // Add loading effect
                    document.body.style.opacity = '0.7';

                    setTimeout(() => {
                        window.location.href = href;
                    }, 200);
                }
            });
        });

        // Dynamic Stats Counter Animation
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                element.textContent = Math.floor(current);
                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                }
            }, 20);
        }

        // Initialize counters when in viewport
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target.querySelector('.stats-number');
                    if (counter && !counter.classList.contains('animated')) {
                        counter.classList.add('animated');
                        const target = parseInt(counter.textContent);
                        counter.textContent = '0';
                        animateCounter(counter, target);
                    }
                }
            });
        }, observerOptions);

        document.querySelectorAll('.stats-card').forEach(card => {
            observer.observe(card);
        });

        // Enhanced Form Validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const requiredFields = this.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('is-invalid');
                        field.addEventListener('input', function() {
                            this.classList.remove('is-invalid');
                        });
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'خطأ في البيانات',
                        text: 'يرجى ملء جميع الحقول المطلوبة',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            });
        });
    </script>

    <!-- Bulk Add JavaScript -->
    <script src="{% static 'dashboard/js/bulk-add.js' %}"></script>

    <!-- Navigation Active State JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const currentPath = window.location.pathname;

        // تفعيل الحالة النشطة للروابط في الشريط الجانبي
        const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');

        sidebarLinks.forEach(link => {
            // إزالة الحالة النشطة من جميع الروابط
            link.classList.remove('active');

            // التحقق من الرابط الحالي
            const linkPath = new URL(link.href).pathname;

            if (currentPath === linkPath ||
                (currentPath.startsWith(linkPath) && linkPath !== '/')) {
                link.classList.add('active');
            }

            // حالات خاصة للصفحة الرئيسية
            if (currentPath === '/' && linkPath === '/') {
                link.classList.add('active');
            }
        });

        // تفعيل الحالة النشطة للروابط في شريط التنقل العلوي
        const topNavLinks = document.querySelectorAll('.top-nav-link');

        topNavLinks.forEach(link => {
            // إزالة الحالة النشطة من جميع الروابط
            link.classList.remove('active');

            // التحقق من الرابط الحالي
            const linkPath = new URL(link.href).pathname;

            if (currentPath === linkPath ||
                (currentPath.startsWith(linkPath) && linkPath !== '/')) {
                link.classList.add('active');
            }

            // حالات خاصة للصفحة الرئيسية
            if (currentPath === '/' && linkPath === '/') {
                link.classList.add('active');
            }
        });

        // تحسين تأثيرات التمرير للشريط الجانبي
        sidebarLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(-12px)';
                    this.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.15)';
                }
            });

            link.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(-8px)';
                    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                }
            });
        });

        // تحسين تأثيرات التمرير لشريط التنقل العلوي
        topNavLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(-3px)';
                    this.style.boxShadow = '0 6px 20px rgba(37, 99, 235, 0.4)';
                }
            });

            link.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                }
            });
        });
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
