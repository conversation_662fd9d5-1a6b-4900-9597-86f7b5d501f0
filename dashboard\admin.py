from django.contrib import admin
from django.utils.html import format_html
from .models import *

# Register your models here.

@admin.register(Areas)
class AreasAdmin(admin.ModelAdmin):
    list_display = ['id', 'area_name']
    search_fields = ['area_name']
    list_filter = ['area_name']

@admin.register(StoreType)
class StoreTypeAdmin(admin.ModelAdmin):
    list_display = ['id', 'store_type']
    search_fields = ['store_type']
    list_filter = ['store_type']

@admin.register(Companies)
class CompaniesAdmin(admin.ModelAdmin):
    list_display = ['id', 'name']
    search_fields = ['name']
    list_filter = ['name']

@admin.register(Stores)
class StoresAdmin(admin.ModelAdmin):
    list_display = ['id', 'store_name', 'area', 'store_type', 'enable', 'store_person_name', 'store_person_phone_number']
    search_fields = ['store_name', 'store_person_name', 'store_person_phone_number']
    list_filter = ['area', 'store_type', 'enable', 'notification', 'is_static_in_main_screen', 'is_found_24_for_food_stor']
    readonly_fields = ['last_login', 'date_joined']
    fieldsets = (
        ('معلومات المستخدم', {
            'fields': ('username', 'email', 'first_name', 'last_name', 'is_active', 'is_staff', 'is_superuser')
        }),
        ('معلومات المتجر', {
            'fields': ('store_name', 'area', 'store_type', 'lat', 'long', 'store_picture')
        }),
        ('معلومات المسؤول', {
            'fields': ('store_person_name', 'store_person_phone_number')
        }),
        ('الإعدادات', {
            'fields': ('enable', 'notification', 'is_static_in_main_screen', 'is_found_24_for_food_stor', 'price_stor')
        }),
        ('تفاصيل إضافية', {
            'fields': ('description', 'note')
        }),
    )

@admin.register(Categ)
class CategAdmin(admin.ModelAdmin):
    list_display = ['id', 'categ_name', 'display_image']
    search_fields = ['categ_name']
    list_filter = ['categ_name']

    def display_image(self, obj):
        if obj.categ_picture:
            return format_html('<img src="{}" width="50" height="50" />', obj.categ_picture.url)
        return "لا توجد صورة"
    display_image.short_description = 'صورة الفئة'

@admin.register(Famlies)
class FamliesAdmin(admin.ModelAdmin):
    list_display = ['id', 'family_name', 'categ']
    search_fields = ['family_name']
    list_filter = ['categ']

@admin.register(Products)
class ProductsAdmin(admin.ModelAdmin):
    list_display = ['id', 'product_name', 'store', 'categ', 'price', 'enable', 'views', 'display_image']
    search_fields = ['product_name', 'seo']
    list_filter = ['store', 'categ', 'family', 'company', 'enable', 'notification', 'is_static_in_main_screen', 'state_fouder']
    readonly_fields = ['slug', 'views', 'datetimes', 'update_date']
    prepopulated_fields = {"slug": ("product_name",)}

    def display_image(self, obj):
        if obj.product_picture:
            return format_html('<img src="{}" width="50" height="50" />', obj.product_picture.url)
        return "لا توجد صورة"
    display_image.short_description = 'صورة المنتج'

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('product_name', 'slug', 'store', 'categ', 'family', 'company')
        }),
        ('التفاصيل', {
            'fields': ('product_description', 'seo', 'price')
        }),
        ('الصور', {
            'fields': ('product_picture', 'product_picture_backend')
        }),
        ('الإعدادات', {
            'fields': ('enable', 'notification', 'is_static_in_main_screen', 'state_fouder')
        }),
        ('إحصائيات', {
            'fields': ('views', 'datetimes', 'update_date'),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('note',)
        }),
    )

@admin.register(ProductsImg)
class ProductsImgAdmin(admin.ModelAdmin):
    list_display = ['id', 'product', 'display_image', 'datetimes']
    search_fields = ['product__product_name']
    list_filter = ['product', 'datetimes']

    def display_image(self, obj):
        if obj.img:
            return format_html('<img src="{}" width="50" height="50" />', obj.img.url)
        return "لا توجد صورة"
    display_image.short_description = 'الصورة'

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'phone_number', 'place']
    search_fields = ['name', 'phone_number', 'place']
    list_filter = ['place']

@admin.register(ProductsTageem)
class ProductsTageeemAdmin(admin.ModelAdmin):
    list_display = ['id', 'custom', 'product', 'ok', 'datetimes']
    search_fields = ['custom__name', 'product__product_name']
    list_filter = ['ok', 'datetimes', 'product']

@admin.register(CustomerTageem)
class CustomerTageeemAdmin(admin.ModelAdmin):
    list_display = ['id', 'custom', 'store', 'ok', 'datetimes']
    search_fields = ['custom__name', 'store__store_name']
    list_filter = ['ok', 'datetimes', 'store']

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'custom', 'products', 'count', 'price', 'all_price', 'order_hawalah_number', 'order_state', 'request_date']
    search_fields = ['custom__name', 'products__product_name', 'order_hawalah_number']
    list_filter = ['order_state', 'request_date', 'get_date']
    readonly_fields = ['request_date', 'get_date']

@admin.register(Bills)
class BillsAdmin(admin.ModelAdmin):
    list_display = ['id', 'bill_numbers']
    search_fields = ['bill_numbers']

@admin.register(Baskets)
class BasketsAdmin(admin.ModelAdmin):
    list_display = ['id', 'bill_numbers', 'order', 'place']
    search_fields = ['bill_numbers__bill_numbers', 'order__products__product_name', 'place']
    list_filter = ['place']

@admin.register(Notifications)
class NotificationsAdmin(admin.ModelAdmin):
    list_display = ['id', 'store', 'notifications_text', 'from_date', 'to_date', 'date']
    search_fields = ['store__store_name', 'notifications_text']
    list_filter = ['store', 'from_date', 'to_date', 'date']

    fieldsets = (
        ('معلومات الإشعار', {
            'fields': ('store', 'notifications_text', 'description')
        }),
        ('التواريخ', {
            'fields': ('from_date', 'to_date', 'date')
        }),
        ('المنتجات', {
            'fields': ('products',)
        }),
    )

@admin.register(BasketsTestHeaders)
class BasketsTestHeadersAdmin(admin.ModelAdmin):
    list_display = ['id', 'bill_numbers', 'peson_name', 'phone_number', 'city', 'order_state', 'enables', 'all_price_display', 'all_items_display']
    search_fields = ['bill_numbers', 'peson_name', 'phone_number', 'order_hawalah_number']
    list_filter = ['order_state', 'city', 'enables', 'request_date']
    readonly_fields = ['request_date', 'get_date', 'all_price_display', 'all_items_display', 'product_names_display']

    def all_price_display(self, obj):
        return obj.all_price
    all_price_display.short_description = 'السعر الإجمالي'

    def all_items_display(self, obj):
        return obj.all_items
    all_items_display.short_description = 'عدد العناصر'

    def product_names_display(self, obj):
        return obj.product_names
    product_names_display.short_description = 'أسماء المنتجات'

    fieldsets = (
        ('معلومات الفاتورة', {
            'fields': ('bill_numbers', 'csrf_cookies', 'order_hawalah_number', 'order_state')
        }),
        ('معلومات العميل', {
            'fields': ('peson_name', 'phone_number', 'city', 'place')
        }),
        ('التواريخ', {
            'fields': ('request_date', 'get_date')
        }),
        ('الحالة', {
            'fields': ('enables',)
        }),
        ('ملخص السلة', {
            'fields': ('all_price_display', 'all_items_display', 'product_names_display'),
            'classes': ('collapse',)
        }),
    )

class BasketsTestInline(admin.TabularInline):
    model = BasketsTest
    extra = 0
    readonly_fields = ['all_price']

@admin.register(BasketsTest)
class BasketsTestAdmin(admin.ModelAdmin):
    list_display = ['id', 'basket_headers', 'products', 'count', 'all_price']
    search_fields = ['basket_headers__bill_numbers', 'products__product_name']
    list_filter = ['basket_headers', 'products']

# إضافة Inline للسلة في رأس السلة
BasketsTestHeadersAdmin.inlines = [BasketsTestInline]

@admin.register(DeliveryPricing)
class DeliveryPricingAdmin(admin.ModelAdmin):
    list_display = ['id', 'distance_range_text', 'distance_range_km', 'price_per_delivery', 'is_active', 'created_at']
    search_fields = ['description']
    list_filter = ['is_active', 'created_at']
    readonly_fields = ['created_at', 'updated_at', 'distance_range_text', 'distance_range_km']

    fieldsets = (
        ('نطاق المسافة', {
            'fields': ('distance_from', 'distance_to', 'distance_range_text', 'distance_range_km')
        }),
        ('التسعير', {
            'fields': ('price_per_delivery', 'description')
        }),
        ('الإعدادات', {
            'fields': ('is_active',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ['id', 'rating_type', 'rating_value', 'rated_name', 'reviewer_name', 'is_verified', 'is_public', 'created_at']
    list_filter = ['rating_type', 'rating_value', 'is_verified', 'is_public', 'created_at']
    search_fields = ['comment', 'rated_store__store_name', 'rated_customer__name', 'rated_delivery_person']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']

    fieldsets = (
        ('معلومات التقييم', {
            'fields': ('rating_type', 'rating_value', 'comment', 'is_public', 'is_verified')
        }),
        ('المُقيِّم', {
            'fields': ('reviewer_customer', 'reviewer_store')
        }),
        ('المُقيَّم', {
            'fields': ('rated_store', 'rated_customer', 'rated_delivery_person')
        }),
        ('معلومات إضافية', {
            'fields': ('order', 'admin_response')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(RatingStatistics)
class RatingStatisticsAdmin(admin.ModelAdmin):
    list_display = ['id', 'get_rated_name', 'total_ratings', 'average_rating', 'rating_percentage', 'last_updated']
    list_filter = ['last_updated']
    search_fields = ['store__store_name', 'customer__name', 'delivery_person_name']
    readonly_fields = ['total_ratings', 'average_rating', 'five_stars', 'four_stars', 'three_stars', 'two_stars', 'one_star', 'last_updated']
    ordering = ['-average_rating', '-total_ratings']

    def get_rated_name(self, obj):
        if obj.store:
            return f"متجر: {obj.store.store_name}"
        elif obj.customer:
            return f"عميل: {obj.customer.name}"
        elif obj.delivery_person_name:
            return f"عامل توصيل: {obj.delivery_person_name}"
        return "غير محدد"
    get_rated_name.short_description = 'المُقيَّم'

    actions = ['update_statistics']

    def update_statistics(self, request, queryset):
        """تحديث الإحصائيات للعناصر المحددة"""
        updated_count = 0
        for stats in queryset:
            stats.update_statistics()
            updated_count += 1

        self.message_user(request, f'تم تحديث إحصائيات {updated_count} عنصر بنجاح.')
    update_statistics.short_description = 'تحديث الإحصائيات'


# تخصيص عنوان لوحة الإدارة
admin.site.site_header = "لوحة تحكم نظام توصيل الطلبات"
admin.site.site_title = "نظام توصيل الطلبات"
admin.site.index_title = "مرحباً بك في لوحة التحكم"
