{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}إحصائيات التقييمات{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        text-align: center;
        transition: transform 0.3s;
    }
    .stats-card:hover {
        transform: translateY(-5px);
    }
    .stats-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    .rating-bar {
        background: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    .rating-fill {
        background: linear-gradient(45deg, #f6c23e, #f4b619);
        height: 100%;
        transition: width 0.5s ease;
    }
    .top-item {
        background: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #4e73df;
        transition: all 0.3s;
    }
    .top-item:hover {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transform: translateX(5px);
    }
    .chart-container {
        position: relative;
        height: 300px;
        margin: 2rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar text-primary"></i>
            إحصائيات التقييمات
        </h1>
        <div>
            <a href="{% url 'ratings_list' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-list"></i>
                قائمة التقييمات
            </a>
            <a href="{% url 'ratings_create' %}" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i>
                إضافة تقييم
            </a>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="h4 mb-0">{{ total_ratings }}</div>
                <div class="small">إجمالي التقييمات</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="h4 mb-0">{{ store_ratings }}</div>
                <div class="small">تقييمات المتاجر</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="h4 mb-0">{{ customer_ratings }}</div>
                <div class="small">تقييمات العملاء</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-motorcycle"></i>
                </div>
                <div class="h4 mb-0">{{ delivery_ratings }}</div>
                <div class="small">تقييمات التوصيل</div>
            </div>
        </div>
    </div>

    <!-- متوسط التقييمات -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-store"></i>
                        متوسط تقييم المتاجر
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="h2 text-warning">{{ avg_store_rating }}</div>
                    <div class="rating-stars text-warning">
                        {% for i in "12345" %}
                            {% if avg_store_rating >= forloop.counter %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-users"></i>
                        متوسط تقييم العملاء
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="h2 text-warning">{{ avg_customer_rating }}</div>
                    <div class="rating-stars text-warning">
                        {% for i in "12345" %}
                            {% if avg_customer_rating >= forloop.counter %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-motorcycle"></i>
                        متوسط تقييم التوصيل
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="h2 text-warning">{{ avg_delivery_rating }}</div>
                    <div class="rating-stars text-warning">
                        {% for i in "12345" %}
                            {% if avg_delivery_rating >= forloop.counter %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- توزيع التقييمات -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i>
                        توزيع التقييمات
                    </h6>
                </div>
                <div class="card-body">
                    {% for rating, count in rating_distribution.items %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>
                                {% for i in "12345" %}
                                    {% if rating >= forloop.counter %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </span>
                            <span class="font-weight-bold">{{ count }}</span>
                        </div>
                        <div class="rating-bar">
                            <div class="rating-fill" style="width: {% if total_ratings > 0 %}{{ count|floatformat:0|add:0|mul:100|div:total_ratings }}{% else %}0{% endif %}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-clock"></i>
                        التقييمات الأخيرة
                    </h6>
                </div>
                <div class="card-body">
                    {% for rating in recent_ratings %}
                    <div class="top-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="font-weight-bold">{{ rating.rated_name }}</div>
                                <div class="small text-muted">{{ rating.reviewer_name }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-warning">
                                    {% for i in "12345" %}
                                        {% if rating.rating_value >= forloop.counter %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="small text-muted">{{ rating.created_at|date:"d/m H:i" }}</div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted">
                        <i class="fas fa-star fa-2x mb-2"></i>
                        <p>لا توجد تقييمات حديثة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- أفضل المتاجر والعملاء -->
    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy"></i>
                        أفضل المتاجر
                    </h6>
                </div>
                <div class="card-body">
                    {% for stats in top_stores %}
                    <div class="top-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="font-weight-bold">{{ stats.store.store_name }}</div>
                                <div class="small text-muted">{{ stats.total_ratings }} تقييم</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-warning">{{ stats.average_rating }}</div>
                                <div class="small text-warning">
                                    {% for i in "12345" %}
                                        {% if stats.average_rating >= forloop.counter %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted">
                        <i class="fas fa-store fa-2x mb-2"></i>
                        <p>لا توجد تقييمات للمتاجر</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-medal"></i>
                        أفضل العملاء
                    </h6>
                </div>
                <div class="card-body">
                    {% for stats in top_customers %}
                    <div class="top-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="font-weight-bold">{{ stats.customer.name }}</div>
                                <div class="small text-muted">{{ stats.total_ratings }} تقييم</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-warning">{{ stats.average_rating }}</div>
                                <div class="small text-warning">
                                    {% for i in "12345" %}
                                        {% if stats.average_rating >= forloop.counter %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>لا توجد تقييمات للعملاء</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-award"></i>
                        أفضل عمال التوصيل
                    </h6>
                </div>
                <div class="card-body">
                    {% for stats in top_delivery_persons %}
                    <div class="top-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="font-weight-bold">{{ stats.delivery_person_name }}</div>
                                <div class="small text-muted">{{ stats.total_ratings }} تقييم</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-warning">{{ stats.average_rating }}</div>
                                <div class="small text-warning">
                                    {% for i in "12345" %}
                                        {% if stats.average_rating >= forloop.counter %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted">
                        <i class="fas fa-motorcycle fa-2x mb-2"></i>
                        <p>لا توجد تقييمات لعمال التوصيل</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.stats-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // تحريك أشرطة التقييم
    $('.rating-fill').each(function() {
        const width = $(this).css('width');
        $(this).css('width', '0');
        $(this).animate({width: width}, 1000);
    });
});
</script>
{% endblock %}
