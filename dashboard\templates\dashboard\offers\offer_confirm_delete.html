{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - العروض الخاصة{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'special_offers_list' %}">العروض الخاصة</a></li>
<li class="breadcrumb-item"><a href="{% url 'special_offer_detail' offer.pk %}">{{ offer.title }}</a></li>
<li class="breadcrumb-item active">حذف</li>
{% endblock %}

{% block extra_css %}
<style>
.delete-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 600px;
    margin: 2rem auto;
}

.delete-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.warning-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.offer-summary {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-left: 4px solid #dc3545;
}

.summary-item {
    display: flex;
    justify-content: between;
    margin-bottom: 0.5rem;
}

.summary-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.summary-value {
    color: #6c757d;
}

.warning-list {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.btn-delete {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    color: white;
}

.btn-cancel {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    color: white;
}

.offer-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.type-discount_percentage { background: #cce5ff; color: #004085; }
.type-discount_fixed { background: #d4edda; color: #155724; }
.type-buy_get { background: #fff3cd; color: #856404; }
.type-free_delivery { background: #e2e3e5; color: #383d41; }
.type-bundle { background: #f8d7da; color: #721c24; }
.type-seasonal { background: #d1ecf1; color: #0c5460; }

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-draft { background: #e2e3e5; color: #383d41; }
.status-paused { background: #fff3cd; color: #856404; }
.status-expired { background: #f8d7da; color: #721c24; }
.status-cancelled { background: #f5c6cb; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-12">
        <div class="delete-card">
            <!-- رأس التحذير -->
            <div class="delete-header">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="mb-2">تأكيد حذف العرض</h3>
                <p class="mb-0 opacity-75">هذا الإجراء لا يمكن التراجع عنه</p>
            </div>

            <!-- محتوى البطاقة -->
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <h5 class="text-danger">هل أنت متأكد من حذف هذا العرض؟</h5>
                    <p class="text-muted">سيتم حذف العرض وجميع البيانات المرتبطة به نهائياً</p>
                </div>

                <!-- ملخص العرض -->
                <div class="offer-summary">
                    <h6 class="text-danger mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        ملخص العرض المراد حذفه
                    </h6>
                    
                    <div class="summary-item">
                        <div class="summary-label">العنوان:</div>
                        <div class="summary-value">{{ offer.title }}</div>
                    </div>
                    
                    <div class="summary-item">
                        <div class="summary-label">النوع:</div>
                        <div class="summary-value">
                            <span class="offer-type-badge type-{{ offer.offer_type }}">
                                {{ offer.get_offer_type_display }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="summary-item">
                        <div class="summary-label">الحالة:</div>
                        <div class="summary-value">
                            <span class="status-badge status-{{ offer.status }}">
                                {{ offer.get_status_display }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="summary-item">
                        <div class="summary-label">الفترة:</div>
                        <div class="summary-value">
                            {{ offer.start_date|date:"Y/m/d" }} - {{ offer.end_date|date:"Y/m/d" }}
                        </div>
                    </div>
                    
                    <div class="summary-item">
                        <div class="summary-label">الاستخدامات:</div>
                        <div class="summary-value">
                            {{ offer.current_usage_count }}
                            {% if offer.total_usage_limit > 0 %}
                                / {{ offer.total_usage_limit }}
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if offer.offer_type == 'discount_percentage' %}
                    <div class="summary-item">
                        <div class="summary-label">نسبة الخصم:</div>
                        <div class="summary-value">{{ offer.discount_percentage }}%</div>
                    </div>
                    {% elif offer.offer_type == 'discount_fixed' %}
                    <div class="summary-item">
                        <div class="summary-label">مبلغ الخصم:</div>
                        <div class="summary-value">{{ offer.discount_amount|floatformat:0 }} د.ع</div>
                    </div>
                    {% elif offer.offer_type == 'buy_get' %}
                    <div class="summary-item">
                        <div class="summary-label">العرض:</div>
                        <div class="summary-value">اشتري {{ offer.buy_quantity }} واحصل على {{ offer.get_quantity }}</div>
                    </div>
                    {% endif %}
                    
                    <div class="summary-item">
                        <div class="summary-label">تاريخ الإنشاء:</div>
                        <div class="summary-value">{{ offer.created_date|date:"Y/m/d H:i" }}</div>
                    </div>
                    
                    <div class="summary-item">
                        <div class="summary-label">أنشأ بواسطة:</div>
                        <div class="summary-value">{{ offer.created_by.username }}</div>
                    </div>
                </div>

                <!-- تحذيرات -->
                <div class="warning-list">
                    <h6 class="text-warning mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تحذيرات مهمة
                    </h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            سيتم حذف العرض نهائياً ولا يمكن استرداده
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            سيتم حذف جميع سجلات استخدام هذا العرض
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            لن يتمكن العملاء من استخدام هذا العرض بعد الآن
                        </li>
                        {% if offer.current_usage_count > 0 %}
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning me-2"></i>
                            هذا العرض تم استخدامه {{ offer.current_usage_count }} مرة
                        </li>
                        {% endif %}
                        {% if offer.status == 'active' %}
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning me-2"></i>
                            هذا العرض نشط حالياً وقد يؤثر على العملاء
                        </li>
                        {% endif %}
                    </ul>
                </div>

                <!-- أزرار التحكم -->
                <div class="d-flex justify-content-center gap-3">
                    <a href="{% url 'special_offer_detail' offer.pk %}" class="btn btn-cancel">
                        <i class="fas fa-arrow-right me-2"></i>
                        إلغاء
                    </a>
                    
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-delete" onclick="return confirmDelete()">
                            <i class="fas fa-trash me-2"></i>
                            تأكيد الحذف
                        </button>
                    </form>
                </div>

                <!-- معلومات إضافية -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكنك تعطيل العرض بدلاً من حذفه إذا كنت تريد الاحتفاظ بالبيانات
                    </small>
                    <br>
                    <a href="{% url 'special_offer_edit' offer.pk %}" class="btn btn-link btn-sm">
                        تعديل العرض بدلاً من الحذف
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    return confirm('هل أنت متأكد تماماً من حذف هذا العرض؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بالعرض.');
}
</script>
{% endblock %}
