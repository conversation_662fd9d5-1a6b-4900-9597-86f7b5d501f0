# Generated manually for ratings system

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0007_deliverypricing_delete_deliverycost_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Rating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating_type', models.CharField(choices=[('store', 'تقييم متجر'), ('customer', 'تقييم عميل'), ('delivery', 'تقييم عامل توصيل')], max_length=20, verbose_name='نوع التقييم')),
                ('rating_value', models.IntegerField(choices=[(1, '⭐ (سيء جداً)'), (2, '⭐⭐ (سيء)'), (3, '⭐⭐⭐ (متوسط)'), (4, '⭐⭐⭐⭐ (جيد)'), (5, '⭐⭐⭐⭐⭐ (ممتاز)')], verbose_name='التقييم')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='التعليق')),
                ('rated_delivery_person', models.CharField(blank=True, max_length=255, null=True, verbose_name='عامل التوصيل المُقيَّم')),
                ('is_public', models.BooleanField(default=True, verbose_name='تقييم عام')),
                ('is_verified', models.BooleanField(default=False, verbose_name='تقييم موثق')),
                ('admin_response', models.TextField(blank=True, null=True, verbose_name='رد الإدارة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='dashboard.order', verbose_name='الطلب المرتبط')),
                ('rated_customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_ratings', to='dashboard.customer', verbose_name='العميل المُقيَّم')),
                ('rated_store', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_ratings', to='dashboard.stores', verbose_name='المتجر المُقيَّم')),
                ('reviewer_customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='given_ratings', to='dashboard.customer', verbose_name='العميل المُقيِّم')),
                ('reviewer_store', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='given_ratings', to='dashboard.stores', verbose_name='المتجر المُقيِّم')),
            ],
            options={
                'verbose_name': 'تقييم',
                'verbose_name_plural': 'التقييمات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RatingStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_person_name', models.CharField(blank=True, max_length=255, null=True, unique=True, verbose_name='اسم عامل التوصيل')),
                ('total_ratings', models.IntegerField(default=0, verbose_name='إجمالي التقييمات')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='متوسط التقييم')),
                ('five_stars', models.IntegerField(default=0, verbose_name='5 نجوم')),
                ('four_stars', models.IntegerField(default=0, verbose_name='4 نجوم')),
                ('three_stars', models.IntegerField(default=0, verbose_name='3 نجوم')),
                ('two_stars', models.IntegerField(default=0, verbose_name='نجمتان')),
                ('one_star', models.IntegerField(default=0, verbose_name='نجمة واحدة')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('customer', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rating_stats', to='dashboard.customer', verbose_name='العميل')),
                ('store', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rating_stats', to='dashboard.stores', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'إحصائية تقييم',
                'verbose_name_plural': 'إحصائيات التقييمات',
            },
        ),
    ]
