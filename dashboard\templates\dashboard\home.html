{% extends 'dashboard/base.html' %}

{% block title %}الرئيسية - لوحة تحكم نظام توصيل الطلبات المتطور{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">لوحة المعلومات</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h1 class="display-4 mb-2 animate__animated animate__fadeInUp">
            <i class="fas fa-tachometer-alt me-3"></i>
            مرحباً بك في لوحة التحكم المتطورة
        </h1>
        <p class="lead text-white-50 animate__animated animate__fadeInUp animate__delay-1s">
            نظرة شاملة ومتطورة على إحصائيات نظام توصيل الطلبات
        </p>
    </div>
    <div class="animate__animated animate__fadeInRight animate__delay-2s">
        <div class="glass p-3 rounded text-center">
            <i class="fas fa-calendar-alt fa-2x text-white mb-2"></i>
            <h6 class="text-white mb-0">{{ "now"|date:"Y/m/d" }}</h6>
            <small class="text-white-50">{{ "now"|date:"l" }}</small>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}

<!-- بطاقات الإحصائيات الرئيسية الملونة -->
<div class="row mb-5">
    <div class="col-12 mb-4">
        <h3 class="text-white mb-3" data-aos="fade-up">
            <i class="fas fa-chart-bar me-2"></i>
            الإحصائيات العامة للنظام
        </h3>
        <p class="text-white-50 mb-4" data-aos="fade-up" data-aos-delay="100">
            نظرة شاملة على أهم المؤشرات في نظام توصيل الطلبات
        </p>
    </div>

    <!-- إجمالي المتاجر -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="main-stats-card stores-card" data-aos="fade-up" data-aos-delay="100">
            <div class="main-stats-background">
                <i class="fas fa-store"></i>
            </div>
            <div class="main-stats-content">
                <div class="main-stats-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="main-stats-info">
                    <div class="main-stats-number" data-target="{{ total_stores }}">{{ total_stores }}</div>
                    <div class="main-stats-label">إجمالي المتاجر</div>
                    <div class="main-stats-description">المتاجر المسجلة في النظام</div>
                </div>
            </div>
            <div class="main-stats-footer">
                <a href="{% url 'stores_list' %}" class="main-stats-link">
                    <span>عرض التفاصيل</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- إجمالي المنتجات -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="main-stats-card products-card" data-aos="fade-up" data-aos-delay="200">
            <div class="main-stats-background">
                <i class="fas fa-box"></i>
            </div>
            <div class="main-stats-content">
                <div class="main-stats-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="main-stats-info">
                    <div class="main-stats-number" data-target="{{ total_products }}">{{ total_products }}</div>
                    <div class="main-stats-label">إجمالي المنتجات</div>
                    <div class="main-stats-description">المنتجات المتاحة للطلب</div>
                </div>
            </div>
            <div class="main-stats-footer">
                <a href="{% url 'products_list' %}" class="main-stats-link">
                    <span>عرض التفاصيل</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- إجمالي العملاء -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="main-stats-card customers-card" data-aos="fade-up" data-aos-delay="300">
            <div class="main-stats-background">
                <i class="fas fa-users"></i>
            </div>
            <div class="main-stats-content">
                <div class="main-stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="main-stats-info">
                    <div class="main-stats-number" data-target="{{ total_customers }}">{{ total_customers }}</div>
                    <div class="main-stats-label">إجمالي العملاء</div>
                    <div class="main-stats-description">العملاء المسجلين في النظام</div>
                </div>
            </div>
            <div class="main-stats-footer">
                <a href="{% url 'customers_list' %}" class="main-stats-link">
                    <span>عرض التفاصيل</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- إجمالي الطلبات -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="main-stats-card orders-card" data-aos="fade-up" data-aos-delay="400">
            <div class="main-stats-background">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="main-stats-content">
                <div class="main-stats-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="main-stats-info">
                    <div class="main-stats-number" data-target="{{ total_orders }}">{{ total_orders }}</div>
                    <div class="main-stats-label">إجمالي الطلبات</div>
                    <div class="main-stats-description">جميع الطلبات في النظام</div>
                </div>
            </div>
            <div class="main-stats-footer">
                <a href="{% url 'orders_list' %}" class="main-stats-link">
                    <span>عرض التفاصيل</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات حالات الطلبات -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-1">إحصائيات حالات الطلبات</h5>
                <p class="text-muted mb-0 small">تفصيل شامل لجميع حالات الطلبات في النظام</p>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for status_code, status_data in orders_stats.items %}
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-3">
                        <div class="order-status-card status-{{ status_code }}" data-aos="fade-up" data-aos-delay="{{ forloop.counter|add:4 }}00">
                            <div class="order-status-content">
                                <div class="order-status-number" data-target="{{ status_data.count }}">{{ status_data.count }}</div>
                                <div class="order-status-label">{{ status_data.name }}</div>
                            </div>
                            <div class="order-status-icon">
                                {% if status_code == 'pending' %}
                                    <i class="fas fa-clock"></i>
                                {% elif status_code == 'waiting_shipping' %}
                                    <i class="fas fa-hourglass-half"></i>
                                {% elif status_code == 'shipped' %}
                                    <i class="fas fa-shipping-fast"></i>
                                {% elif status_code == 'on_way' %}
                                    <i class="fas fa-truck"></i>
                                {% elif status_code == 'delivered' %}
                                    <i class="fas fa-check-circle"></i>
                                {% elif status_code == 'no_answer' %}
                                    <i class="fas fa-phone-slash"></i>
                                {% elif status_code == 'postponed' %}
                                    <i class="fas fa-pause-circle"></i>
                                {% elif status_code == 'wrong_address' %}
                                    <i class="fas fa-map-marker-alt"></i>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card" data-aos="fade-up">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">مؤشرات الأداء الرئيسية</h5>
                        <p class="text-muted mb-0 small">تحليل شامل لأداء النظام</p>
                    </div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-primary active" data-period="week">أسبوع</button>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-period="month">شهر</button>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-period="year">سنة</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <canvas id="performanceChart" height="300"></canvas>
                    </div>
                    <div class="col-lg-4">
                        <div class="row">
                            <div class="col-6 col-lg-12 mb-3">
                                <div class="glass p-3 rounded text-center">
                                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                                    <h4 class="text-dark mb-1">125,430</h4>
                                    <small class="text-muted">إجمالي المبيعات (د.ع)</small>
                                </div>
                            </div>
                            <div class="col-6 col-lg-12 mb-3">
                                <div class="glass p-3 rounded text-center">
                                    <i class="fas fa-percentage fa-2x text-warning mb-2"></i>
                                    <h4 class="text-dark mb-1">94.2%</h4>
                                    <small class="text-muted">معدل رضا العملاء</small>
                                </div>
                            </div>
                            <div class="col-6 col-lg-12 mb-3">
                                <div class="glass p-3 rounded text-center">
                                    <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                    <h4 class="text-dark mb-1">23 دقيقة</h4>
                                    <small class="text-muted">متوسط وقت التوصيل</small>
                                </div>
                            </div>
                            <div class="col-6 col-lg-12 mb-3">
                                <div class="glass p-3 rounded text-center">
                                    <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                    <h4 class="text-dark mb-1">4.8/5</h4>
                                    <small class="text-muted">متوسط التقييمات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Analytics Section -->
<div class="row mb-5">
    <!-- Recent Orders -->
    <div class="col-lg-8 mb-4">
        <div class="card" data-aos="fade-right">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أحدث الطلبات المباشرة
                    </h5>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success me-2 animate__animated animate__pulse animate__infinite">
                            <i class="fas fa-circle me-1"></i>مباشر
                        </span>
                        <button class="btn btn-sm btn-outline-light" onclick="refreshOrders()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                {% for order in recent_orders %}
                                <tr class="animate__animated animate__fadeInUp" style="animation-delay: {{ forloop.counter0|add:1 }}00ms;">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                                {{ order.custom.name|first }}
                                            </div>
                                            <div>
                                                <strong>{{ order.custom.name }}</strong><br>
                                                <small class="text-muted">{{ order.custom.phone_number }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {{ order.products.product_name|truncatechars:25 }}<br>
                                            <small class="text-muted">كمية: {{ order.count }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ order.all_price }} د.ع</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">قيد المعالجة</span>
                                    </td>
                                    <td>
                                        <div>
                                            {{ order.request_date|date:"Y/m/d" }}<br>
                                            <small class="text-muted">{{ order.request_date|time:"H:i" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="قبول">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'orders_list' %}" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع الطلبات
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <div class="animate__animated animate__bounceIn">
                            <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات حتى الآن</h5>
                            <p class="text-muted">ستظهر الطلبات الجديدة هنا تلقائياً</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Stats & Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100" data-aos="fade-left">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <!-- Today's Stats -->
                <div class="glass p-3 rounded mb-3">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-calendar-day me-2"></i>
                        إحصائيات اليوم
                    </h6>
                    <div class="row text-center">
                        <div class="col-6 mb-2">
                            <h4 class="text-success mb-0">{{ total_orders|default:0 }}</h4>
                            <small class="text-muted">طلبات جديدة</small>
                        </div>
                        <div class="col-6 mb-2">
                            <h4 class="text-info mb-0">{{ total_customers|default:0 }}</h4>
                            <small class="text-muted">عملاء جدد</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning mb-0">15,250</h4>
                            <small class="text-muted">مبيعات (د.ع)</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger mb-0">98%</h4>
                            <small class="text-muted">معدل النجاح</small>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="glass p-3 rounded mb-3">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-rocket me-2"></i>
                        إجراءات سريعة
                    </h6>
                    <div class="d-grid gap-2">
                        <a href="{% url 'products_create' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-2"></i>منتج جديد
                        </a>
                        <a href="{% url 'categories_create' %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-tags me-2"></i>فئة جديدة
                        </a>
                        <a href="{% url 'notifications_create' %}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-bell me-2"></i>إشعار جديد
                        </a>
                    </div>
                </div>

                <!-- System Status -->
                <div class="glass p-3 rounded">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-server me-2"></i>
                        حالة النظام
                    </h6>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small>استخدام الخادم</small>
                            <small class="text-success">23%</small>
                        </div>
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar bg-success" style="width: 23%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small>استخدام الذاكرة</small>
                            <small class="text-warning">67%</small>
                        </div>
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar bg-warning" style="width: 67%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small>مساحة التخزين</small>
                            <small class="text-info">45%</small>
                        </div>
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar bg-info" style="width: 45%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    
    <!-- Recent Products -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    أحدث المنتجات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_products %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>المتجر</th>
                                    <th>السعر</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in recent_products %}
                                <tr>
                                    <td>{{ product.product_name|truncatechars:25 }}</td>
                                    <td>{{ product.store.store_name|truncatechars:20 }}</td>
                                    <td>{{ product.price }} د.ع</td>
                                    <td>{{ product.datetimes|date:"Y/m/d" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'products_list' %}" class="btn btn-primary btn-sm">
                            عرض جميع المنتجات
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد منتجات حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Top Stores and Categories -->
<div class="row">
    <!-- Top Stores -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أفضل المتاجر
                </h5>
            </div>
            <div class="card-body">
                {% if top_stores %}
                    {% for store in top_stores %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ store.store_name }}</h6>
                            <small class="text-muted">{{ store.product_count }} منتج</small>
                        </div>
                        <div class="badge bg-primary">{{ forloop.counter }}</div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-store fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد متاجر حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Top Categories -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    أفضل الفئات
                </h5>
            </div>
            <div class="card-body">
                {% if top_categories %}
                    {% for category in top_categories %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ category.categ_name }}</h6>
                            <small class="text-muted">{{ category.product_count }} منتج</small>
                        </div>
                        <div class="badge bg-success">{{ forloop.counter }}</div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد فئات حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'products_create' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus mb-2"></i><br>
                            إضافة منتج جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'categories_create' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-tags mb-2"></i><br>
                            إضافة فئة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'notifications_create' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-bell mb-2"></i><br>
                            إضافة إشعار
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/" class="btn btn-outline-info w-100">
                            <i class="fas fa-cog mb-2"></i><br>
                            لوحة الإدارة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <div id="liveToast" class="toast hide" role="alert">
        <div class="toast-header glass">
            <i class="fas fa-bell text-primary me-2"></i>
            <strong class="me-auto">إشعار جديد</strong>
            <small class="text-muted">الآن</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body glass">
            <span id="toastMessage">طلب جديد تم استلامه!</span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Stats Cards */
    .stats-card {
        position: relative;
        overflow: hidden;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .stats-content {
        z-index: 2;
        position: relative;
    }

    .stats-trend {
        margin-top: 10px;
        font-size: 0.9rem;
    }

    .stats-chart {
        position: absolute;
        bottom: 10px;
        right: 10px;
        opacity: 0.3;
        z-index: 1;
    }

    /* Performance Chart Container */
    #performanceChart {
        max-height: 300px;
    }

    /* Enhanced Progress Bars */
    .progress {
        background: rgba(255,255,255,0.2);
        border-radius: 10px;
        overflow: hidden;
    }

    .progress-bar {
        border-radius: 10px;
        transition: width 1s ease-in-out;
    }

    /* Toast Notifications */
    .toast {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 15px;
    }

    .toast-header, .toast-body {
        background: transparent;
        border: none;
        color: white;
    }

    /* Live Data Indicator */
    .live-indicator {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* Enhanced Table Rows */
    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: var(--glass-bg);
        transform: scale(1.01);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize Charts
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
        initializeRealTimeUpdates();
        initializeCounters();
    });

    // Initialize Mini Charts for Stats Cards
    function initializeCharts() {
        // Stores Chart
        const storesCtx = document.getElementById('storesChart');
        if (storesCtx) {
            new Chart(storesCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        data: [12, 19, 15, 25, 22, 30],
                        borderColor: 'rgba(255,255,255,0.8)',
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
        }

        // Performance Chart
        const performanceCtx = document.getElementById('performanceChart');
        if (performanceCtx) {
            new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
                    datasets: [
                        {
                            label: 'الطلبات',
                            data: [65, 78, 90, 81, 95, 105, 120],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'المبيعات',
                            data: [45, 58, 70, 61, 75, 85, 95],
                            borderColor: '#f093fb',
                            backgroundColor: 'rgba(240, 147, 251, 0.1)',
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: { color: '#333' }
                        }
                    },
                    scales: {
                        x: {
                            grid: { color: 'rgba(0,0,0,0.1)' },
                            ticks: { color: '#666' }
                        },
                        y: {
                            grid: { color: 'rgba(0,0,0,0.1)' },
                            ticks: { color: '#666' }
                        }
                    }
                }
            });
        }
    }

    // Real-time Updates
    function initializeRealTimeUpdates() {
        // Simulate real-time order updates
        setInterval(() => {
            if (Math.random() > 0.7) { // 30% chance every 5 seconds
                showNewOrderNotification();
            }
        }, 5000);

        // Update system stats
        setInterval(updateSystemStats, 10000);
    }

    // Show new order notification
    function showNewOrderNotification() {
        const toastElement = document.getElementById('liveToast');
        const messageElement = document.getElementById('toastMessage');

        const messages = [
            'طلب جديد من أحمد محمد - 25,000 د.ع',
            'طلب جديد من فاطمة علي - 18,500 د.ع',
            'طلب جديد من محمد حسن - 32,000 د.ع'
        ];

        messageElement.textContent = messages[Math.floor(Math.random() * messages.length)];

        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    }

    // Update system statistics
    function updateSystemStats() {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const currentWidth = parseInt(bar.style.width) || 50;
            const newWidth = Math.max(10, Math.min(90, currentWidth + (Math.random() - 0.5) * 10));
            bar.style.width = newWidth + '%';

            // Update text
            const textElement = bar.parentElement.previousElementSibling.querySelector('small:last-child');
            if (textElement) {
                textElement.textContent = Math.round(newWidth) + '%';

                // Update color based on value
                bar.className = 'progress-bar';
                if (newWidth < 30) bar.classList.add('bg-success');
                else if (newWidth < 70) bar.classList.add('bg-warning');
                else bar.classList.add('bg-danger');
            }
        });
    }

    // Initialize animated counters
    function initializeCounters() {
        const counters = document.querySelectorAll('.stats-number[data-target]');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            animateCounter(counter, target);
        });
    }

    // Animate counter function
    function animateCounter(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            element.textContent = Math.floor(current);
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 20);
    }

    // Refresh orders function
    function refreshOrders() {
        const button = event.target.closest('button');
        const icon = button.querySelector('i');

        // Add loading state
        icon.classList.add('fa-spin');
        button.disabled = true;

        // Simulate API call
        setTimeout(() => {
            icon.classList.remove('fa-spin');
            button.disabled = false;

            // Show success message
            Swal.fire({
                title: 'تم التحديث',
                text: 'تم تحديث قائمة الطلبات بنجاح',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }, 1500);
    }

    // تحريك الأرقام في البطاقات الرئيسية الجديدة
    document.addEventListener('DOMContentLoaded', function() {
        const animateMainNumbers = () => {
            // تحريك أرقام البطاقات الرئيسية
            const mainNumbers = document.querySelectorAll('.main-stats-number[data-target]');

            mainNumbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-target'));
                const increment = target / 80;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current).toLocaleString('ar-EG');
                }, 25);
            });

            // تحريك أرقام حالات الطلبات
            const orderNumbers = document.querySelectorAll('.order-status-number[data-target]');

            orderNumbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-target'));
                const increment = target / 60;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current);
                }, 30);
            });
        };

        // تشغيل التحريك عند تحميل الصفحة
        setTimeout(animateMainNumbers, 800);

        // إضافة تأثيرات تفاعلية للبطاقات الرئيسية
        const mainStatsCards = document.querySelectorAll('.main-stats-card');

        mainStatsCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تأثير النقر على البطاقات
        mainStatsCards.forEach(card => {
            card.addEventListener('click', function() {
                const link = this.querySelector('.main-stats-link');
                if (link) {
                    window.location.href = link.href;
                }
            });

            // إضافة مؤشر اليد
            card.style.cursor = 'pointer';
        });
    });
</script>
{% endblock %}
