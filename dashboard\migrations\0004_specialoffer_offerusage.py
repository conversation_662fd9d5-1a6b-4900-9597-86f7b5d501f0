# Generated by Django 4.1.4 on 2025-07-09 03:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dashboard', '0003_accountingperiod_storeaccount_payment_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SpecialOffer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان العرض')),
                ('description', models.TextField(verbose_name='وصف العرض')),
                ('offer_type', models.CharField(choices=[('discount_percentage', 'خصم نسبة مئوية'), ('discount_fixed', 'خصم مبلغ ثابت'), ('buy_get', 'اشتري واحصل على'), ('free_delivery', 'توصيل مجاني'), ('bundle', 'عرض حزمة'), ('seasonal', 'عرض موسمي')], max_length=30, verbose_name='نوع العرض')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم (%)')),
                ('discount_amount', models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='مبلغ الخصم (د.ع)')),
                ('minimum_order_value', models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأدنى لقيمة الطلب (د.ع)')),
                ('maximum_discount', models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأقصى للخصم (د.ع)')),
                ('buy_quantity', models.IntegerField(default=1, verbose_name='اشتري كمية')),
                ('get_quantity', models.IntegerField(default=1, verbose_name='احصل على كمية')),
                ('start_date', models.DateTimeField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateTimeField(verbose_name='تاريخ النهاية')),
                ('target_audience', models.CharField(choices=[('all', 'جميع العملاء'), ('new_customers', 'العملاء الجدد'), ('returning_customers', 'العملاء المتكررين'), ('vip_customers', 'العملاء المميزين'), ('specific_area', 'منطقة محددة')], max_length=30, verbose_name='الجمهور المستهدف')),
                ('usage_limit_per_customer', models.IntegerField(default=1, verbose_name='حد الاستخدام لكل عميل')),
                ('total_usage_limit', models.IntegerField(default=0, verbose_name='حد الاستخدام الإجمالي (0 = بلا حدود)')),
                ('current_usage_count', models.IntegerField(default=0, verbose_name='عدد الاستخدامات الحالي')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشط'), ('paused', 'متوقف'), ('expired', 'منتهي'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('is_featured', models.BooleanField(default=False, verbose_name='عرض مميز')),
                ('show_on_homepage', models.BooleanField(default=False, verbose_name='عرض في الصفحة الرئيسية')),
                ('requires_coupon_code', models.BooleanField(default=False, verbose_name='يتطلب كود خصم')),
                ('coupon_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='كود الخصم')),
                ('image', models.ImageField(blank=True, null=True, upload_to='offers/', verbose_name='صورة العرض')),
                ('banner_image', models.ImageField(blank=True, null=True, upload_to='offers/banners/', verbose_name='صورة البانر')),
                ('terms_and_conditions', models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات داخلية')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_offers', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('target_areas', models.ManyToManyField(blank=True, related_name='special_offers', to='dashboard.areas', verbose_name='المناطق المستهدفة')),
                ('target_products', models.ManyToManyField(blank=True, related_name='special_offers', to='dashboard.products', verbose_name='المنتجات المستهدفة')),
                ('target_stores', models.ManyToManyField(blank=True, related_name='special_offers', to='dashboard.stores', verbose_name='المتاجر المستهدفة')),
            ],
            options={
                'verbose_name': 'عرض خاص',
                'verbose_name_plural': 'العروض الخاصة',
                'ordering': ['-created_date'],
            },
        ),
        migrations.CreateModel(
            name='OfferUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discount_amount', models.DecimalField(decimal_places=0, max_digits=10, verbose_name='مبلغ الخصم المطبق')),
                ('usage_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاستخدام')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.customer', verbose_name='العميل')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.specialoffer', verbose_name='العرض')),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dashboard.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'استخدام عرض',
                'verbose_name_plural': 'استخدامات العروض',
                'unique_together': {('offer', 'customer', 'order')},
            },
        ),
    ]
