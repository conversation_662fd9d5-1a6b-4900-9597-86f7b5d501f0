# 💰 النظام المحاسبي المتطور

## 🎯 نظرة عامة
تم إنشاء نظام محاسبي شامل ومتطور لإدارة الحسابات والعمولات بينك وبين المتاجر، يحسب المستحقات بناءً على عدد الطلبات وقيمتها، مع إمكانيات متقدمة لإدارة الدفعات والتقارير.

## ✨ المكونات الرئيسية

### 🏗️ **النماذج (Models):**

#### **1. إعدادات العمولة (CommissionSettings):**
```python
class CommissionSettings(models.Model):
    store = models.OneToOneField(Stores, ...)  # المتجر
    commission_type = models.CharField(...)    # نوع العمولة
    fixed_amount = models.DecimalField(...)    # المبلغ الثابت
    percentage_rate = models.DecimalField(...) # النسبة المئوية
    minimum_commission = models.DecimalField(...) # الحد الأدنى
    maximum_commission = models.DecimalField(...) # الحد الأقصى
    is_active = models.BooleanField(...)       # مفعل/معطل
```

**أنواع العمولة:**
- **ثابت (fixed)**: مبلغ ثابت لكل طلب
- **نسبة (percentage)**: نسبة مئوية من قيمة الطلب
- **متدرج (tiered)**: يقل المبلغ مع زيادة عدد الطلبات

#### **2. الفترات المحاسبية (AccountingPeriod):**
```python
class AccountingPeriod(models.Model):
    name = models.CharField(...)           # اسم الفترة
    period_type = models.CharField(...)    # نوع الفترة
    start_date = models.DateField(...)     # تاريخ البداية
    end_date = models.DateField(...)       # تاريخ النهاية
    status = models.CharField(...)         # الحالة
    total_orders = models.IntegerField(...) # إجمالي الطلبات
    total_revenue = models.DecimalField(...) # إجمالي الإيرادات
    total_commission = models.DecimalField(...) # إجمالي العمولات
```

**أنواع الفترات:**
- **أسبوعي (weekly)**: فترة أسبوعية
- **شهري (monthly)**: فترة شهرية
- **ربع سنوي (quarterly)**: فترة ربع سنوية
- **مخصص (custom)**: فترة مخصصة

#### **3. حسابات المتاجر (StoreAccount):**
```python
class StoreAccount(models.Model):
    period = models.ForeignKey(AccountingPeriod, ...) # الفترة المحاسبية
    store = models.ForeignKey(Stores, ...)            # المتجر
    orders_count = models.IntegerField(...)           # عدد الطلبات
    total_orders_value = models.DecimalField(...)     # قيمة الطلبات
    commission_amount = models.DecimalField(...)      # مبلغ العمولة
    paid_amount = models.DecimalField(...)            # المبلغ المدفوع
    remaining_amount = models.DecimalField(...)       # المبلغ المتبقي
    is_settled = models.BooleanField(...)             # مسدد بالكامل
```

#### **4. الدفعات (Payment):**
```python
class Payment(models.Model):
    store_account = models.ForeignKey(StoreAccount, ...) # حساب المتجر
    amount = models.DecimalField(...)                    # المبلغ
    payment_method = models.CharField(...)               # طريقة الدفع
    payment_date = models.DateTimeField(...)             # تاريخ الدفع
    reference_number = models.CharField(...)             # رقم المرجع
    created_by = models.ForeignKey(User, ...)            # أنشأ بواسطة
```

**طرق الدفع:**
- **نقدي (cash)**
- **تحويل بنكي (bank_transfer)**
- **شيك (check)**
- **دفع إلكتروني (online)**

### 🎨 **الواجهات والصفحات:**

#### **📊 لوحة التحكم المحاسبية:**
- **إحصائيات عامة**: إجمالي المتاجر، الفترات النشطة، العمولات، المبالغ المعلقة
- **الفترات الحديثة**: آخر 5 فترات محاسبية
- **الحسابات المعلقة**: المتاجر التي لديها مستحقات
- **الدفعات الحديثة**: آخر 10 دفعات
- **إجراءات سريعة**: روابط للوظائف الرئيسية

#### **⚙️ إعدادات العمولات:**
- **قائمة الإعدادات**: جميع إعدادات العمولة للمتاجر
- **إنشاء إعدادات جديدة**: نموذج لإضافة إعدادات عمولة
- **تعديل الإعدادات**: تحديث إعدادات موجودة
- **معاينة الإعدادات**: عرض تفاصيل الإعدادات

#### **📅 الفترات المحاسبية:**
- **قائمة الفترات**: جميع الفترات المحاسبية
- **إنشاء فترة جديدة**: نموذج لإضافة فترة
- **تفاصيل الفترة**: عرض حسابات المتاجر في الفترة
- **حساب الحسابات**: حساب تلقائي للمستحقات

#### **💳 إدارة الدفعات:**
- **قائمة الدفعات**: جميع الدفعات مع فلترة
- **إضافة دفعة**: نموذج لإضافة دفعة جديدة
- **تفاصيل حساب المتجر**: عرض الدفعات والمستحقات

#### **📈 التقارير:**
- **تقرير ملخص**: إحصائيات عامة
- **تقرير مفصل**: تفاصيل كاملة للحسابات
- **تقرير الدفعات**: سجل جميع الدفعات
- **تقرير العمولات**: تحليل العمولات

### 🔧 **الوظائف المتقدمة:**

#### **🧮 حساب العمولة التلقائي:**
```python
def calculate_commission(self, order_value, orders_count=1):
    if self.commission_type == 'fixed':
        commission = self.fixed_amount * orders_count
    elif self.commission_type == 'percentage':
        commission = (order_value * self.percentage_rate) / 100
    else:  # tiered
        commission = self._calculate_tiered_commission(order_value, orders_count)
    
    # تطبيق الحد الأدنى والأقصى
    if self.minimum_commission > 0:
        commission = max(commission, self.minimum_commission)
    if self.maximum_commission > 0:
        commission = min(commission, self.maximum_commission)
        
    return commission
```

#### **📊 النظام المتدرج:**
- **1-10 طلبات**: المبلغ الكامل
- **11-50 طلب**: خصم 10% (90% من المبلغ)
- **51-100 طلب**: خصم 20% (80% من المبلغ)
- **أكثر من 100**: خصم 30% (70% من المبلغ)

#### **🔄 حساب الفترة المحاسبية:**
```python
def calculate_period_accounts(request, pk):
    # حذف الحسابات الموجودة
    StoreAccount.objects.filter(period=period).delete()
    
    # حساب الطلبات لكل متجر في الفترة
    orders_in_period = Order.objects.filter(
        request_date__range=[period.start_date, period.end_date],
        products__store__isnull=False
    )
    
    # إنشاء حسابات المتاجر وحساب العمولات
    for store_id, data in store_data.items():
        store_account = StoreAccount.objects.create(...)
        store_account.calculate_commission()
```

#### **💰 تحديث الدفعات التلقائي:**
```python
def save(self, *args, **kwargs):
    super().save(*args, **kwargs)
    # تحديث حساب المتجر
    self.store_account.paid_amount = self.store_account.payments.aggregate(
        total=Sum('amount')
    )['total'] or 0
    self.store_account.remaining_amount = self.store_account.commission_amount - self.store_account.paid_amount
    self.store_account.is_settled = self.store_account.remaining_amount <= 0
    self.store_account.save()
```

### 🎯 **المسارات (URLs):**
```python
# النظام المحاسبي
path('accounting/', views.accounting_dashboard, name='accounting_dashboard'),

# إعدادات العمولات
path('accounting/commission-settings/', views.commission_settings_list, name='commission_settings_list'),
path('accounting/commission-settings/create/', views.commission_settings_create, name='commission_settings_create'),
path('accounting/commission-settings/<int:pk>/edit/', views.commission_settings_edit, name='commission_settings_edit'),

# الفترات المحاسبية
path('accounting/periods/', views.accounting_periods_list, name='accounting_periods_list'),
path('accounting/periods/create/', views.accounting_period_create, name='accounting_period_create'),
path('accounting/periods/<int:pk>/', views.accounting_period_detail, name='accounting_period_detail'),
path('accounting/periods/<int:pk>/calculate/', views.calculate_period_accounts, name='calculate_period_accounts'),

# حسابات المتاجر والدفعات
path('accounting/store-accounts/<int:pk>/', views.store_account_detail, name='store_account_detail'),
path('accounting/store-accounts/<int:store_account_id>/add-payment/', views.add_payment, name='add_payment'),
path('accounting/payments/', views.payments_list, name='payments_list'),

# التقارير
path('accounting/reports/', views.accounting_reports, name='accounting_reports'),
```

### 🎨 **التصميم والواجهة:**

#### **🌈 ألوان النظام:**
- **الأساسي**: تدرج أزرق-بنفسجي للبطاقات الرئيسية
- **النجاح**: أخضر للدفعات والحسابات المسددة
- **التحذير**: برتقالي للحسابات المعلقة
- **المعلومات**: أزرق فاتح للمعلومات العامة

#### **📱 التجاوب:**
- **الشاشات الكبيرة**: تخطيط شبكي متقدم
- **الشاشات المتوسطة**: تكيف الأعمدة
- **الشاشات الصغيرة**: ترتيب عمودي

#### **🎭 التأثيرات:**
- **تأثيرات التمرير**: رفع البطاقات وتغيير الظلال
- **انتقالات سلسة**: لجميع العناصر التفاعلية
- **ألوان ديناميكية**: حسب حالة الحساب

### 🚀 **الفوائد المحققة:**

#### **💼 للإدارة:**
- **حساب دقيق**: للمستحقات بناءً على الطلبات
- **تتبع شامل**: لجميع الدفعات والمستحقات
- **تقارير متقدمة**: لاتخاذ قرارات مدروسة
- **أتمتة كاملة**: للعمليات الحسابية

#### **🏪 للمتاجر:**
- **شفافية كاملة**: في حساب العمولات
- **نظام عادل**: مع خيارات متعددة للعمولة
- **تحفيز الأداء**: مع النظام المتدرج
- **سهولة التتبع**: للمستحقات والدفعات

#### **📊 للمحاسبة:**
- **دقة عالية**: في الحسابات والتقارير
- **توفير الوقت**: مع الحساب التلقائي
- **تنظيم ممتاز**: للفترات المحاسبية
- **أرشفة شاملة**: لجميع المعاملات

### 🎯 **سيناريوهات الاستخدام:**

#### **📅 إنشاء فترة محاسبية جديدة:**
1. اذهب إلى النظام المحاسبي
2. انقر على "فترة جديدة"
3. حدد نوع الفترة والتواريخ
4. احفظ الفترة
5. انقر على "حساب الحسابات"

#### **⚙️ إعداد عمولة متجر:**
1. اذهب إلى إعدادات العمولات
2. انقر على "إضافة إعدادات جديدة"
3. اختر المتجر ونوع العمولة
4. حدد القيم والحدود
5. احفظ الإعدادات

#### **💰 إضافة دفعة:**
1. اذهب إلى تفاصيل حساب المتجر
2. انقر على "إضافة دفعة"
3. أدخل المبلغ وطريقة الدفع
4. أضف رقم المرجع والملاحظات
5. احفظ الدفعة

### 🎉 **الخلاصة:**

تم إنشاء نظام محاسبي شامل ومتطور يتميز بـ:

1. **حساب دقيق ومرن** للعمولات بثلاثة أنواع مختلفة
2. **إدارة شاملة** للفترات المحاسبية والحسابات
3. **تتبع كامل** للدفعات والمستحقات
4. **تقارير متقدمة** للتحليل واتخاذ القرارات
5. **واجهة جميلة** وسهلة الاستخدام
6. **أتمتة كاملة** للعمليات الحسابية
7. **مرونة عالية** في التخصيص والإعدادات

النظام الآن **جاهز للاستخدام المكثف** ويوفر حلاً متكاملاً لإدارة الحسابات مع المتاجر! 🚀✨
