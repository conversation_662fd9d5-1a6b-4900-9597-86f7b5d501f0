from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify

# Create your models here.

class Areas(models.Model):
    area_name = models.CharField(max_length=255, verbose_name="اسم المنطقة")

    class Meta:
        verbose_name = "منطقة"
        verbose_name_plural = "المناطق"

    def __str__(self):
        return self.area_name

class StoreType(models.Model):
    store_type = models.CharField(max_length=255, verbose_name="نوع المتجر")

    class Meta:
        verbose_name = "نوع متجر"
        verbose_name_plural = "أنواع المتاجر"

    def __str__(self):
        return self.store_type

class Companies(models.Model):
    name = models.CharField(max_length=255, verbose_name="اسم الشركة")

    class Meta:
        verbose_name = "شركة"
        verbose_name_plural = "الشركات"

    def __str__(self):
        return self.name

class Stores(User):
    # معرف فريد للمتجر للتعامل مع Firebase
    id_store = models.CharField(max_length=50, unique=True, verbose_name="معرف المتجر", help_text="معرف فريد للتعامل مع Firebase")

    area = models.ForeignKey(Areas, on_delete=models.PROTECT, verbose_name="المنطقة")
    store_type = models.ForeignKey(StoreType, on_delete=models.PROTECT, verbose_name="نوع المتجر")
    store_name = models.CharField(max_length=255, verbose_name="اسم المتجر")
    lat = models.CharField(max_length=255, verbose_name="خط العرض")
    long = models.CharField(max_length=255, verbose_name="خط الطول")
    store_person_name = models.CharField(max_length=255, verbose_name="اسم مسؤول المتجر")
    store_person_phone_number = models.CharField(max_length=255, verbose_name="رقم هاتف مسؤول المتجر")
    store_picture = models.FileField(upload_to='store_pictures/', max_length=255, verbose_name="صورة المتجر")
    enable = models.BooleanField(default=True, verbose_name="مفعل")
    notification = models.BooleanField(default=True, verbose_name="الإشعارات")
    description = models.TextField(null=True, blank=True, verbose_name="الوصف")
    note = models.CharField(max_length=1000, null=True, blank=True, verbose_name="ملاحظات")
    is_static_in_main_screen = models.BooleanField(default=False, verbose_name="ثابت في الشاشة الرئيسية")
    is_found_24_for_food_stor = models.BooleanField(default=False, verbose_name="متوفر 24 ساعة للطعام")
    price_stor = models.IntegerField(default=0, verbose_name="إيجار المحل (ريال يمني)")

    # حقول إضافية لتتبع بيانات الحساب المُولدة
    generated_username = models.CharField(max_length=150, blank=True, null=True, verbose_name="اسم المستخدم المُولد")
    generated_password = models.CharField(max_length=128, blank=True, null=True, verbose_name="كلمة المرور المُولدة")
    account_created_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ إنشاء الحساب")

    class Meta:
        verbose_name = "متجر"
        verbose_name_plural = "المتاجر"

    def __str__(self):
        return self.store_name

    def save(self, *args, **kwargs):
        """حفظ المتجر مع توليد البيانات التلقائية"""
        # توليد البيانات عند الإنشاء فقط
        if not self.pk:  # متجر جديد
            self.generate_store_credentials()
        super().save(*args, **kwargs)

    def generate_store_credentials(self):
        """توليد اسم المستخدم وكلمة المرور و id_store تلقائياً"""
        import random
        import string
        from django.utils import timezone

        # توليد id_store فريد
        if not self.id_store:
            timestamp = str(int(timezone.now().timestamp()))
            random_suffix = ''.join(random.choices(string.digits, k=4))
            self.id_store = f"STORE_{timestamp}_{random_suffix}"

        # توليد اسم مستخدم فريد
        if not self.username:
            base_username = self.store_name.replace(' ', '_').lower()
            # إزالة الأحرف غير المسموحة
            base_username = ''.join(c for c in base_username if c.isalnum() or c == '_')

            # التأكد من أن اسم المستخدم فريد
            counter = 1
            username = f"store_{base_username}"
            while User.objects.filter(username=username).exists():
                username = f"store_{base_username}_{counter}"
                counter += 1

            self.username = username
            self.generated_username = username

        # توليد كلمة مرور قوية
        if not self.generated_password:
            # توليد كلمة مرور تحتوي على أحرف وأرقام
            password_chars = string.ascii_letters + string.digits
            password = ''.join(random.choices(password_chars, k=12))

            # إضافة رقم في البداية والنهاية لضمان وجود أرقام
            password = random.choice(string.digits) + password + random.choice(string.digits)

            self.generated_password = password
            self.set_password(password)

        # تعيين البريد الإلكتروني إذا لم يكن موجوداً
        if not self.email:
            self.email = f"{self.username}@store.local"

    def get_store_credentials(self):
        """الحصول على بيانات الحساب المُولدة"""
        return {
            'id_store': self.id_store,
            'username': self.generated_username or self.username,
            'password': self.generated_password,
            'email': self.email,
            'created_date': self.account_created_date
        }

    def regenerate_password(self):
        """إعادة توليد كلمة المرور"""
        import random
        import string

        password_chars = string.ascii_letters + string.digits
        new_password = ''.join(random.choices(password_chars, k=12))
        new_password = random.choice(string.digits) + new_password + random.choice(string.digits)

        self.generated_password = new_password
        self.set_password(new_password)
        self.save()

        return new_password

class Categ(models.Model):
    categ_name = models.CharField(max_length=255, verbose_name="اسم الفئة")
    categ_picture = models.FileField(upload_to='categ_pictures/', max_length=255, null=True, blank=True, verbose_name="صورة الفئة")
    note = models.CharField(max_length=1000, null=True, blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "فئة"
        verbose_name_plural = "الفئات"

    def __str__(self):
        return self.categ_name

    @property
    def get_products(self):
        return Products.objects.filter(categ_id=self.id)

class Famlies(models.Model):
    categ = models.ForeignKey(Categ, on_delete=models.PROTECT, verbose_name="الفئة")
    family_name = models.CharField(max_length=255, verbose_name="اسم العائلة")
    note = models.CharField(max_length=1000, null=True, blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عائلة منتج"
        verbose_name_plural = "عائلات المنتجات"

    def __str__(self):
        return self.family_name

class Products(models.Model):
    store = models.ForeignKey(Stores, on_delete=models.PROTECT, null=True, blank=True, verbose_name="المتجر")
    categ = models.ForeignKey(Categ, on_delete=models.PROTECT, null=True, blank=True, verbose_name="الفئة")
    family = models.ForeignKey(Famlies, on_delete=models.PROTECT, null=True, blank=True, verbose_name="العائلة")
    company = models.ForeignKey(Companies, on_delete=models.PROTECT, null=True, blank=True, verbose_name="الشركة")
    slug = models.SlugField(max_length=200, unique=True, blank=True, verbose_name="الرابط")
    product_name = models.CharField(max_length=255, verbose_name="اسم المنتج")
    product_description = models.TextField(null=True, blank=True, verbose_name="وصف المنتج")
    seo = models.CharField(max_length=1000, null=True, blank=True, verbose_name="كلمات مفتاحية")
    price = models.IntegerField(default=0, verbose_name="السعر (ريال يمني)")
    product_picture = models.FileField(upload_to='product_pictures/', max_length=255, verbose_name="صورة المنتج")
    product_picture_backend = models.FileField(upload_to='product_pictures/', max_length=255, verbose_name="صورة المنتج الخلفية")
    enable = models.BooleanField(default=True, verbose_name="مفعل")
    notification = models.BooleanField(default=True, verbose_name="الإشعارات")
    note = models.CharField(max_length=1000, null=True, blank=True, verbose_name="ملاحظات")
    views = models.IntegerField(default=0, verbose_name="المشاهدات")
    state_fouder = models.BooleanField(default=True, verbose_name="حالة المؤسس")
    is_static_in_main_screen = models.BooleanField(default=False, verbose_name="ثابت في الشاشة الرئيسية")
    datetimes = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    update_date = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "منتج"
        verbose_name_plural = "المنتجات"

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.product_name)
            unique_slug = base_slug
            num = 1
            while Products.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{num}"
                num += 1
            self.slug = unique_slug
        super().save(*args, **kwargs)

    def __str__(self):
        return self.product_name

class ProductsImg(models.Model):
    product = models.ForeignKey(Products, on_delete=models.CASCADE, verbose_name="المنتج")
    img = models.FileField(upload_to='product_pictures/', max_length=255, verbose_name="الصورة")
    datetimes = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    update_date = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "صورة منتج"
        verbose_name_plural = "صور المنتجات"

class Customer(models.Model):
    name = models.CharField(max_length=255, verbose_name="الاسم")
    phone_number = models.CharField(max_length=255, verbose_name="رقم الهاتف")
    place = models.CharField(max_length=255, verbose_name="المكان")
    notes = models.CharField(max_length=500, null=True, blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عميل"
        verbose_name_plural = "العملاء"

    def __str__(self):
        return self.name

class ProductsTageem(models.Model):
    custom = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    product = models.ForeignKey(Products, on_delete=models.CASCADE, verbose_name="المنتج")
    ok = models.BooleanField(default=False, verbose_name="موافق")
    datetimes = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التقييم")
    notes = models.CharField(max_length=500, null=True, blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "تقييم منتج"
        verbose_name_plural = "تقييمات المنتجات"

    def __str__(self):
        return self.product.product_name

class CustomerTageem(models.Model):
    custom = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    store = models.ForeignKey(Stores, on_delete=models.CASCADE, verbose_name="المتجر")
    ok = models.BooleanField(default=False, verbose_name="موافق")
    datetimes = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التقييم")
    notes = models.CharField(max_length=500, null=True, blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "تقييم عميل"
        verbose_name_plural = "تقييمات العملاء"

    def __str__(self):
        return self.custom.name

class Order(models.Model):
    ORDER_STATUS_CHOICES = [
        ('pending', 'يتم التجهيز'),
        ('waiting_shipping', 'في انتظار الشحن'),
        ('shipped', 'تم الشحن'),
        ('on_way', 'في الطريق إليك'),
        ('delivered', 'تم الاستلام'),
        ('no_answer', 'لا يرد'),
        ('postponed', 'تم التأجيل'),
        ('wrong_address', 'عنوان خاطئ'),
    ]

    custom = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    products = models.ForeignKey(Products, on_delete=models.CASCADE, verbose_name="المنتج")
    price = models.DecimalField(decimal_places=0, max_digits=10, verbose_name="السعر (ريال يمني)")
    count = models.IntegerField(default=1, verbose_name="الكمية")
    all_price = models.DecimalField(decimal_places=0, max_digits=10, verbose_name="السعر الإجمالي (ريال يمني)")
    order_hawalah_number = models.CharField(max_length=50, verbose_name="رقم الحوالة")
    order_state = models.CharField(
        max_length=50,
        choices=ORDER_STATUS_CHOICES,
        default='pending',
        verbose_name="حالة الطلب"
    )
    request_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الطلب")
    get_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الاستلام")

    class Meta:
        verbose_name = "طلب"
        verbose_name_plural = "الطلبات"

    def __str__(self):
        return f"{self.products.product_name} - {self.get_order_state_display()}"

    def get_status_color(self):
        """إرجاع لون مناسب لحالة الطلب"""
        colors = {
            'pending': '#d97706',  # برتقالي للتجهيز
            'waiting_shipping': '#0891b2',  # تركوازي للانتظار
            'shipped': '#1e3a8a',  # أزرق للشحن
            'on_way': '#7c3aed',  # بنفسجي للطريق
            'delivered': '#059669',  # أخضر للاستلام
            'no_answer': '#dc2626',  # أحمر لعدم الرد
            'postponed': '#64748b',  # رمادي للتأجيل
            'wrong_address': '#dc2626',  # أحمر للعنوان الخاطئ
        }
        return colors.get(self.order_state, '#64748b')

class Bills(models.Model):
    bill_numbers = models.CharField(max_length=255, verbose_name="رقم الفاتورة")

    class Meta:
        verbose_name = "فاتورة"
        verbose_name_plural = "الفواتير"

    def __str__(self):
        return self.bill_numbers

class Baskets(models.Model):
    bill_numbers = models.ForeignKey(Bills, on_delete=models.CASCADE, verbose_name="رقم الفاتورة")
    order = models.ForeignKey(Order, on_delete=models.CASCADE, verbose_name="الطلب")
    place = models.CharField(max_length=255, verbose_name="المكان")

    class Meta:
        verbose_name = "سلة"
        verbose_name_plural = "السلال"

class Notifications(models.Model):
    store = models.ForeignKey(Stores, on_delete=models.PROTECT, verbose_name="المتجر")
    notifications_text = models.TextField(verbose_name="نص الإشعار")
    from_date = models.DateField(verbose_name="من تاريخ")
    to_date = models.DateField(verbose_name="إلى تاريخ")
    description = models.TextField(null=True, blank=True, verbose_name="الوصف")
    products = models.CharField(max_length=255, null=True, blank=True, verbose_name="المنتجات")
    date = models.DateField(auto_now_add=True, verbose_name="التاريخ")

    class Meta:
        verbose_name = "إشعار"
        verbose_name_plural = "الإشعارات"

    def __str__(self):
        return self.notifications_text

class BasketsTestHeaders(models.Model):
    bill_numbers = models.CharField(max_length=255, verbose_name="رقم الفاتورة")
    csrf_cookies = models.CharField(max_length=255, verbose_name="كوكيز CSRF")
    order_hawalah_number = models.CharField(max_length=50, null=True, blank=True, verbose_name="رقم الحوالة")
    order_state = models.CharField(max_length=50, null=True, blank=True, verbose_name="حالة الطلب")
    request_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الطلب")
    get_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الاستلام")
    peson_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="اسم الشخص")
    phone_number = models.CharField(max_length=50, null=True, blank=True, verbose_name="رقم الهاتف")
    city = models.CharField(max_length=50, null=True, blank=True, verbose_name="المدينة")
    place = models.CharField(max_length=255, verbose_name="المكان")
    enables = models.IntegerField(default=0, verbose_name="مفعل")  # 0 = غير مؤكد، 1 = مؤكد

    class Meta:
        verbose_name = "رأس سلة اختبار"
        verbose_name_plural = "رؤوس سلال الاختبار"

    def __str__(self):
        return self.bill_numbers

    @property
    def items(self):
        return BasketsTest.objects.filter(basket_headers_id=self.id)

    @property
    def product_names(self):
        pro_name = ''
        for i in BasketsTest.objects.filter(basket_headers_id=self.id):
            pro_name += i.products.product_name + '  '
        return pro_name

    @property
    def all_price(self):
        all_p = 0
        for data in BasketsTest.objects.filter(basket_headers_id=self.id):
            all_p += data.all_price
        return all_p

    @property
    def all_items(self):
        return BasketsTest.objects.filter(basket_headers_id=self.id).count()

class BasketsTest(models.Model):
    basket_headers = models.ForeignKey(BasketsTestHeaders, on_delete=models.PROTECT, verbose_name="رأس السلة")
    products = models.ForeignKey(Products, on_delete=models.CASCADE, verbose_name="المنتج")
    count = models.IntegerField(default=1, verbose_name="الكمية")
    all_price = models.DecimalField(decimal_places=0, max_digits=10, verbose_name="السعر الإجمالي (ريال يمني)")

    class Meta:
        verbose_name = "سلة اختبار"
        verbose_name_plural = "سلال الاختبار"

    def __str__(self):
        return f"{self.products.product_name} - {self.count}"

# نماذج النظام المحاسبي
class CommissionSettings(models.Model):
    """إعدادات العمولة للمتاجر"""
    store = models.OneToOneField(Stores, on_delete=models.CASCADE, verbose_name="المتجر", related_name="commission_settings")
    commission_type = models.CharField(
        max_length=20,
        choices=[
            ('fixed', 'مبلغ ثابت لكل طلب'),
            ('percentage', 'نسبة مئوية من قيمة الطلب'),
            ('tiered', 'نظام متدرج حسب عدد الطلبات'),
        ],
        default='fixed',
        verbose_name="نوع العمولة"
    )
    fixed_amount = models.DecimalField(
        decimal_places=0,
        max_digits=10,
        default=0,
        verbose_name="المبلغ الثابت (ريال يمني)"
    )
    percentage_rate = models.DecimalField(
        decimal_places=2,
        max_digits=5,
        default=0,
        verbose_name="النسبة المئوية (%)"
    )
    minimum_commission = models.DecimalField(
        decimal_places=0,
        max_digits=10,
        default=0,
        verbose_name="الحد الأدنى للعمولة (ريال يمني)"
    )
    maximum_commission = models.DecimalField(
        decimal_places=0,
        max_digits=10,
        default=0,
        verbose_name="الحد الأقصى للعمولة (ريال يمني)"
    )
    is_active = models.BooleanField(default=True, verbose_name="مفعل")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_date = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إعدادات العمولة"
        verbose_name_plural = "إعدادات العمولات"

    def __str__(self):
        return f"عمولة {self.store.store_name}"

    def calculate_commission(self, order_value, orders_count=1):
        """حساب العمولة بناءً على نوع العمولة"""
        if not self.is_active:
            return 0

        if self.commission_type == 'fixed':
            commission = self.fixed_amount * orders_count
        elif self.commission_type == 'percentage':
            commission = (order_value * self.percentage_rate) / 100
        else:  # tiered
            commission = self._calculate_tiered_commission(order_value, orders_count)

        # تطبيق الحد الأدنى والأقصى
        if self.minimum_commission > 0:
            commission = max(commission, self.minimum_commission)
        if self.maximum_commission > 0:
            commission = min(commission, self.maximum_commission)

        return commission

    def _calculate_tiered_commission(self, order_value, orders_count):
        """حساب العمولة المتدرجة"""
        if orders_count <= 10:
            return self.fixed_amount * orders_count
        elif orders_count <= 50:
            return (self.fixed_amount * 0.9) * orders_count
        elif orders_count <= 100:
            return (self.fixed_amount * 0.8) * orders_count
        else:
            return (self.fixed_amount * 0.7) * orders_count

class AccountingPeriod(models.Model):
    """فترات المحاسبة"""
    PERIOD_TYPES = [
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('custom', 'فترة مخصصة'),
    ]

    STATUS_CHOICES = [
        ('open', 'مفتوحة'),
        ('closed', 'مغلقة'),
        ('finalized', 'نهائية'),
    ]

    name = models.CharField(max_length=255, verbose_name="اسم الفترة")
    period_type = models.CharField(max_length=20, choices=PERIOD_TYPES, verbose_name="نوع الفترة")
    start_date = models.DateField(verbose_name="تاريخ البداية")
    end_date = models.DateField(verbose_name="تاريخ النهاية")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open', verbose_name="الحالة")
    total_orders = models.IntegerField(default=0, verbose_name="إجمالي الطلبات")
    total_revenue = models.DecimalField(decimal_places=0, max_digits=15, default=0, verbose_name="إجمالي الإيرادات")
    total_commission = models.DecimalField(decimal_places=0, max_digits=15, default=0, verbose_name="إجمالي العمولات")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    closed_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الإغلاق")

    class Meta:
        verbose_name = "فترة محاسبية"
        verbose_name_plural = "الفترات المحاسبية"
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

class StoreAccount(models.Model):
    """حساب المتجر لفترة محاسبية معينة"""
    period = models.ForeignKey(AccountingPeriod, on_delete=models.CASCADE, verbose_name="الفترة المحاسبية")
    store = models.ForeignKey(Stores, on_delete=models.CASCADE, verbose_name="المتجر")
    orders_count = models.IntegerField(default=0, verbose_name="عدد الطلبات")
    total_orders_value = models.DecimalField(decimal_places=0, max_digits=15, default=0, verbose_name="قيمة الطلبات الإجمالية")
    commission_amount = models.DecimalField(decimal_places=0, max_digits=15, default=0, verbose_name="مبلغ العمولة")
    paid_amount = models.DecimalField(decimal_places=0, max_digits=15, default=0, verbose_name="المبلغ المدفوع")
    remaining_amount = models.DecimalField(decimal_places=0, max_digits=15, default=0, verbose_name="المبلغ المتبقي")
    last_payment_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ آخر دفعة")
    is_settled = models.BooleanField(default=False, verbose_name="مسدد بالكامل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_date = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حساب متجر"
        verbose_name_plural = "حسابات المتاجر"
        unique_together = ['period', 'store']

    def __str__(self):
        return f"حساب {self.store.store_name} - {self.period.name}"

    def calculate_commission(self):
        """حساب العمولة بناءً على إعدادات المتجر"""
        try:
            commission_settings = self.store.commission_settings
            self.commission_amount = commission_settings.calculate_commission(
                self.total_orders_value,
                self.orders_count
            )
            self.remaining_amount = self.commission_amount - self.paid_amount
            self.save()
        except CommissionSettings.DoesNotExist:
            self.commission_amount = 0
            self.remaining_amount = 0
            self.save()

class Payment(models.Model):
    """دفعات المتاجر"""
    PAYMENT_METHODS = [
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('online', 'دفع إلكتروني'),
    ]

    store_account = models.ForeignKey(StoreAccount, on_delete=models.CASCADE, verbose_name="حساب المتجر", related_name="payments")
    amount = models.DecimalField(decimal_places=0, max_digits=15, verbose_name="المبلغ")
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS, verbose_name="طريقة الدفع")
    payment_date = models.DateTimeField(verbose_name="تاريخ الدفع")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, verbose_name="أنشأ بواسطة")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "دفعة"
        verbose_name_plural = "الدفعات"
        ordering = ['-payment_date']

    def __str__(self):
        return f"دفعة {self.amount} ريال يمني - {self.store_account.store.store_name}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # تحديث حساب المتجر
        from django.db.models import Sum
        self.store_account.paid_amount = self.store_account.payments.aggregate(
            total=Sum('amount')
        )['total'] or 0
        self.store_account.remaining_amount = self.store_account.commission_amount - self.store_account.paid_amount
        self.store_account.is_settled = self.store_account.remaining_amount <= 0
        self.store_account.last_payment_date = self.payment_date
        self.store_account.save()

# نموذج العروض الخاصة
class SpecialOffer(models.Model):
    """العروض الخاصة للمتاجر"""
    OFFER_TYPES = [
        ('discount_percentage', 'خصم نسبة مئوية'),
        ('discount_fixed', 'خصم مبلغ ثابت'),
        ('buy_get', 'اشتري واحصل على'),
        ('free_delivery', 'توصيل مجاني'),
        ('bundle', 'عرض حزمة'),
        ('seasonal', 'عرض موسمي'),
    ]

    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('active', 'نشط'),
        ('paused', 'متوقف'),
        ('expired', 'منتهي'),
        ('cancelled', 'ملغي'),
    ]

    TARGET_AUDIENCE = [
        ('all', 'جميع العملاء'),
        ('new_customers', 'العملاء الجدد'),
        ('returning_customers', 'العملاء المتكررين'),
        ('vip_customers', 'العملاء المميزين'),
        ('specific_area', 'منطقة محددة'),
    ]

    title = models.CharField(max_length=255, verbose_name="عنوان العرض")
    description = models.TextField(verbose_name="وصف العرض")
    offer_type = models.CharField(max_length=30, choices=OFFER_TYPES, verbose_name="نوع العرض")

    # تفاصيل العرض
    discount_percentage = models.DecimalField(
        decimal_places=2,
        max_digits=5,
        default=0,
        verbose_name="نسبة الخصم (%)"
    )
    discount_amount = models.DecimalField(
        decimal_places=0,
        max_digits=10,
        default=0,
        verbose_name="مبلغ الخصم (ريال يمني)"
    )
    minimum_order_value = models.DecimalField(
        decimal_places=0,
        max_digits=10,
        default=0,
        verbose_name="الحد الأدنى لقيمة الطلب (ريال يمني)"
    )
    maximum_discount = models.DecimalField(
        decimal_places=0,
        max_digits=10,
        default=0,
        verbose_name="الحد الأقصى للخصم (ريال يمني)"
    )

    # عرض اشتري واحصل على
    buy_quantity = models.IntegerField(default=1, verbose_name="اشتري كمية")
    get_quantity = models.IntegerField(default=1, verbose_name="احصل على كمية")

    # الفترة الزمنية
    start_date = models.DateTimeField(verbose_name="تاريخ البداية")
    end_date = models.DateTimeField(verbose_name="تاريخ النهاية")

    # الاستهداف
    target_audience = models.CharField(max_length=30, choices=TARGET_AUDIENCE, verbose_name="الجمهور المستهدف")
    target_stores = models.ManyToManyField(Stores, blank=True, verbose_name="المتاجر المستهدفة", related_name="special_offers")
    target_areas = models.ManyToManyField(Areas, blank=True, verbose_name="المناطق المستهدفة", related_name="special_offers")
    target_products = models.ManyToManyField(Products, blank=True, verbose_name="المنتجات المستهدفة", related_name="special_offers")

    # الحدود والقيود
    usage_limit_per_customer = models.IntegerField(default=1, verbose_name="حد الاستخدام لكل عميل")
    total_usage_limit = models.IntegerField(default=0, verbose_name="حد الاستخدام الإجمالي (0 = بلا حدود)")
    current_usage_count = models.IntegerField(default=0, verbose_name="عدد الاستخدامات الحالي")

    # الحالة والإعدادات
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    is_featured = models.BooleanField(default=False, verbose_name="عرض مميز")
    show_on_homepage = models.BooleanField(default=False, verbose_name="عرض في الصفحة الرئيسية")
    requires_coupon_code = models.BooleanField(default=False, verbose_name="يتطلب كود خصم")
    coupon_code = models.CharField(max_length=50, blank=True, null=True, verbose_name="كود الخصم")

    # الصور والوسائط
    image = models.ImageField(upload_to='offers/', blank=True, null=True, verbose_name="صورة العرض")
    banner_image = models.ImageField(upload_to='offers/banners/', blank=True, null=True, verbose_name="صورة البانر")

    # معلومات إضافية
    terms_and_conditions = models.TextField(blank=True, null=True, verbose_name="الشروط والأحكام")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات داخلية")

    # تتبع البيانات
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, verbose_name="أنشأ بواسطة", related_name="created_offers")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_date = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عرض خاص"
        verbose_name_plural = "العروض الخاصة"
        ordering = ['-created_date']

    def __str__(self):
        return self.title

    def is_active(self):
        """التحقق من أن العرض نشط ومتاح"""
        from django.utils import timezone
        now = timezone.now()
        return (
            self.status == 'active' and
            self.start_date <= now <= self.end_date and
            (self.total_usage_limit == 0 or self.current_usage_count < self.total_usage_limit)
        )

    def get_discount_value(self, order_value=0):
        """حساب قيمة الخصم"""
        if self.offer_type == 'discount_percentage':
            discount = (order_value * self.discount_percentage) / 100
            if self.maximum_discount > 0:
                discount = min(discount, self.maximum_discount)
            return discount
        elif self.offer_type == 'discount_fixed':
            return min(self.discount_amount, order_value)
        elif self.offer_type == 'free_delivery':
            return 0  # يتم التعامل معه في منطق التوصيل
        return 0

    def can_be_used_by_customer(self, customer_id):
        """التحقق من إمكانية استخدام العرض من قبل العميل"""
        if not self.is_active():
            return False

        # التحقق من حد الاستخدام لكل عميل
        customer_usage = OfferUsage.objects.filter(
            offer=self,
            customer_id=customer_id
        ).count()

        return customer_usage < self.usage_limit_per_customer

    def get_remaining_uses(self):
        """الحصول على عدد الاستخدامات المتبقية"""
        if self.total_usage_limit == 0:
            return "بلا حدود"
        return max(0, self.total_usage_limit - self.current_usage_count)

class OfferUsage(models.Model):
    """تتبع استخدام العروض"""
    offer = models.ForeignKey(SpecialOffer, on_delete=models.CASCADE, verbose_name="العرض")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    order = models.ForeignKey(Order, on_delete=models.CASCADE, null=True, blank=True, verbose_name="الطلب")
    discount_amount = models.DecimalField(decimal_places=0, max_digits=10, verbose_name="مبلغ الخصم المطبق")
    usage_date = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الاستخدام")

    class Meta:
        verbose_name = "استخدام عرض"
        verbose_name_plural = "استخدامات العروض"
        unique_together = ['offer', 'customer', 'order']

    def __str__(self):
        return f"{self.offer.title} - {self.customer.customer_name}"


class DeliveryPricing(models.Model):
    """نموذج تسعير التوصيل حسب المسافة بالمتر"""

    # المسافة بالمتر
    distance_from = models.IntegerField(
        verbose_name='المسافة من (متر)',
        help_text='بداية النطاق بالمتر'
    )

    distance_to = models.IntegerField(
        verbose_name='المسافة إلى (متر)',
        help_text='نهاية النطاق بالمتر'
    )

    # السعر
    price_per_delivery = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='سعر التوصيل',
        help_text='سعر التوصيل بالريال اليمني لهذا النطاق'
    )

    # معلومات إضافية
    description = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name='وصف النطاق',
        help_text='وصف اختياري للنطاق (مثل: قريب، متوسط، بعيد)'
    )

    # حالة النطاق
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط',
        help_text='هل هذا النطاق نشط للتوصيل؟'
    )

    # معلومات النظام
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='أنشئ بواسطة'
    )

    class Meta:
        verbose_name = 'تسعير التوصيل'
        verbose_name_plural = 'تسعير التوصيل'
        ordering = ['distance_from']

        # التأكد من عدم تداخل النطاقات
        constraints = [
            models.CheckConstraint(
                check=models.Q(distance_to__gt=models.F('distance_from')),
                name='distance_to_greater_than_distance_from'
            ),
        ]

    def __str__(self):
        return f"{self.distance_from}-{self.distance_to}م - {self.price_per_delivery} ريال"

    @property
    def distance_range_text(self):
        """نص النطاق منسق"""
        if self.distance_from == 0:
            return f"حتى {self.distance_to} متر"
        elif self.distance_to >= 999999:  # رقم كبير للإشارة إلى "أكثر من"
            return f"أكثر من {self.distance_from} متر"
        else:
            return f"{self.distance_from} - {self.distance_to} متر"

    @property
    def distance_range_km(self):
        """النطاق بالكيلومتر للعرض"""
        from_km = self.distance_from / 1000
        to_km = self.distance_to / 1000

        if self.distance_from == 0:
            return f"حتى {to_km:.1f} كم"
        elif self.distance_to >= 999999:
            return f"أكثر من {from_km:.1f} كم"
        else:
            return f"{from_km:.1f} - {to_km:.1f} كم"

    def is_distance_in_range(self, distance_meters):
        """التحقق من وجود مسافة معينة ضمن هذا النطاق"""
        return self.distance_from <= distance_meters <= self.distance_to

    @classmethod
    def get_price_for_distance(cls, distance_meters):
        """الحصول على السعر لمسافة معينة"""
        try:
            pricing = cls.objects.filter(
                distance_from__lte=distance_meters,
                distance_to__gte=distance_meters,
                is_active=True
            ).first()

            if pricing:
                return pricing.price_per_delivery
            else:
                return None
        except Exception:
            return None

    @classmethod
    def calculate_distance_meters(cls, lat1, lng1, lat2, lng2):
        """حساب المسافة بالمتر باستخدام صيغة Haversine"""
        import math

        # تحويل الدرجات إلى راديان
        lat1_rad = math.radians(float(lat1))
        lng1_rad = math.radians(float(lng1))
        lat2_rad = math.radians(float(lat2))
        lng2_rad = math.radians(float(lng2))

        # حساب الفروق
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad

        # صيغة Haversine
        a = (math.sin(dlat / 2) ** 2 +
             math.cos(lat1_rad) * math.cos(lat2_rad) *
             math.sin(dlng / 2) ** 2)

        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

        # نصف قطر الأرض بالمتر
        R = 6371000

        # المسافة بالمتر
        distance = R * c

        return round(distance)

    @classmethod
    def get_delivery_cost(cls, store_lat, store_lng, customer_lat, customer_lng):
        """حساب تكلفة التوصيل بين المتجر والعميل"""
        try:
            # حساب المسافة بالمتر
            distance_meters = cls.calculate_distance_meters(
                store_lat, store_lng, customer_lat, customer_lng
            )

            # الحصول على السعر
            price = cls.get_price_for_distance(distance_meters)

            return {
                'distance_meters': distance_meters,
                'distance_km': round(distance_meters / 1000, 2),
                'price': price,
                'success': price is not None
            }
        except Exception as e:
            return {
                'distance_meters': 0,
                'distance_km': 0,
                'price': None,
                'success': False,
                'error': str(e)
            }

    def clean(self):
        """التحقق من صحة البيانات"""
        from django.core.exceptions import ValidationError

        if self.distance_from >= self.distance_to:
            raise ValidationError('المسافة "إلى" يجب أن تكون أكبر من المسافة "من"')

        # التحقق من عدم تداخل النطاقات مع النطاقات الأخرى
        overlapping = DeliveryPricing.objects.filter(
            models.Q(
                models.Q(distance_from__lt=self.distance_to) &
                models.Q(distance_to__gt=self.distance_from)
            ),
            is_active=True
        )

        if self.pk:
            overlapping = overlapping.exclude(pk=self.pk)

        if overlapping.exists():
            raise ValidationError('هذا النطاق يتداخل مع نطاق آخر موجود')

    def save(self, *args, **kwargs):
        """حفظ محسن مع التحقق"""
        self.full_clean()
        super().save(*args, **kwargs)



