# 🚀 نظام توصيل الطلبات المتطور - لوحة التحكم الذكية

نظام شامل ومتطور لإدارة توصيل الطلبات مع لوحة تحكم فائقة التطور باستخدام أحدث التقنيات والتصميمات.

## ✨ المميزات المتطورة

### 🎨 تصميم فائق التطور
- **Glassmorphism Design**: تصميم زجاجي شفاف مع تأثيرات blur متطورة
- **تدرجات لونية متحركة**: خلفيات متحركة مع تأثيرات بصرية مذهلة
- **رسوم متحركة متقدمة**: استخدام AOS و Animate.css للحركات السلسة
- **تصميم متجاوب بالكامل**: يعمل بشكل مثالي على جميع الأجهزة
- **دعم RTL كامل**: واجهة مُحسنة للغة العربية

### 🏠 لوحة التحكم الذكية
- **إحصائيات تفاعلية**: رسوم بيانية متحركة مع Chart.js
- **تحديثات فورية**: إشعارات مباشرة وتحديث البيانات تلقائياً
- **مؤشرات أداء متقدمة**: KPIs مع رسوم بيانية صغيرة
- **حالة النظام المباشرة**: مراقبة الخادم والذاكرة والتخزين
- **إجراءات سريعة**: أزرار عائمة للوصول السريع

### 📊 إدارة البيانات المتطورة
- **🗺️ المناطق**: إدارة ذكية للمناطق الجغرافية مع خرائط تفاعلية
- **🏪 أنواع المتاجر**: تصنيف متقدم مع إحصائيات مفصلة
- **🏢 الشركات**: إدارة شاملة مع ملفات تعريفية كاملة
- **🛍️ المتاجر**: لوحة تحكم متكاملة مع معاينة مباشرة
- **🏷️ الفئات**: تصنيف ذكي مع صور وإحصائيات
- **📦 المنتجات المتطورة**:
  - عرض شبكي وقائمة قابل للتبديل
  - بحث متقدم مع فلاتر ذكية
  - تعديل سريع ونسخ المنتجات
  - معاينة مباشرة مع تأثيرات hover
- **👥 العملاء**: ملفات شخصية تفاعلية مع تاريخ الطلبات
- **🛒 الطلبات المباشرة**: متابعة فورية مع إشعارات صوتية
- **🔔 الإشعارات الذكية**: نظام متطور مع جدولة زمنية
- **🧺 السلال التفاعلية**: إدارة متقدمة مع تحليلات

### لوحة الإدارة المتقدمة
- **تكوين شامل**: جميع النماذج مُعدة في لوحة الإدارة
- **عرض محسن**: عرض البيانات مع الصور والتفاصيل
- **بحث وتصفية**: إمكانيات بحث وتصفية متقدمة
- **إحصائيات**: عرض إحصائيات مفصلة لكل قسم

## 🛠️ التقنيات المتطورة المستخدمة

### Backend
- **Django 4.1+**: إطار العمل الرئيسي مع أحدث المميزات
- **SQLite**: قاعدة بيانات سريعة (قابلة للترقية لـ PostgreSQL/MySQL)
- **Pillow**: معالجة الصور المتقدمة

### Frontend المتطور
- **Bootstrap 5 RTL**: تصميم متجاوب مع دعم كامل للعربية
- **CSS3 المتقدم**:
  - CSS Variables للتخصيص السهل
  - Glassmorphism Effects
  - Custom Animations
  - Responsive Grid System
- **JavaScript ES6+**:
  - Chart.js للرسوم البيانية التفاعلية
  - SweetAlert2 للتنبيهات الجميلة
  - AOS للرسوم المتحركة
  - Custom Event Handlers

### UI/UX المتطور
- **Font Awesome 6**: أحدث الأيقونات
- **Google Fonts**: خطوط عربية احترافية (Cairo, Tajawal)
- **Animate.css**: رسوم متحركة سلسة
- **Custom Scrollbars**: شريط تمرير مخصص

## التثبيت والتشغيل

### المتطلبات
```bash
pip install -r requirements.txt
```

### إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### إنشاء مستخدم إداري
```bash
python manage.py createsuperuser
```

### تشغيل الخادم
```bash
python manage.py runserver
```

## الوصول للنظام

- **لوحة التحكم الرئيسية**: http://127.0.0.1:8000/
- **لوحة الإدارة**: http://127.0.0.1:8000/admin/

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
delivery_control/
├── dashboard/              # تطبيق لوحة التحكم
│   ├── models.py          # نماذج البيانات
│   ├── admin.py           # تكوين لوحة الإدارة
│   ├── views.py           # العروض والمنطق
│   ├── forms.py           # نماذج الإدخال
│   ├── urls.py            # المسارات
│   └── templates/         # القوالب
├── delivery_control/       # إعدادات المشروع
├── static/                # الملفات الثابتة
├── media/                 # ملفات الوسائط
└── requirements.txt       # المتطلبات
```

## النماذج الرئيسية

### Areas (المناطق)
- إدارة المناطق الجغرافية للتوصيل

### StoreType (أنواع المتاجر)
- تصنيف المتاجر (مطاعم، صيدليات، إلخ)

### Stores (المتاجر)
- معلومات المتاجر والمالكين
- الموقع الجغرافي والتفاصيل

### Products (المنتجات)
- إدارة شاملة للمنتجات
- الصور والأسعار والتصنيفات

### Orders (الطلبات)
- متابعة الطلبات وحالاتها
- ربط العملاء بالمنتجات

### BasketsTestHeaders (السلال)
- إدارة سلال التسوق
- تتبع حالة الطلبات

## المميزات المتقدمة

- **البحث والتصفية**: في جميع الأقسام
- **رفع الصور**: دعم رفع وعرض الصور
- **الإحصائيات**: لوحة معلومات شاملة
- **التصميم المتجاوب**: يعمل على جميع الأجهزة
- **الأمان**: حماية جميع الصفحات بنظام المصادقة

## التطوير المستقبلي

- [ ] API للتطبيقات المحمولة
- [ ] نظام دفع إلكتروني
- [ ] تتبع الطلبات في الوقت الفعلي
- [ ] تقارير مفصلة
- [ ] نظام تقييم المتاجر والمنتجات

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
