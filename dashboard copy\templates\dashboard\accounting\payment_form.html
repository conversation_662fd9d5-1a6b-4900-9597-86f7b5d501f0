{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounting_period_detail' store_account.period.pk %}">{{ store_account.period.name }}</a></li>
<li class="breadcrumb-item"><a href="{% url 'store_account_detail' store_account.pk %}">{{ store_account.store.store_name }}</a></li>
<li class="breadcrumb-item active">إضافة دفعة</li>
{% endblock %}

{% block extra_css %}
<style>
.payment-form-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.account-summary {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.summary-item {
    text-align: center;
    padding: 1rem;
}

.summary-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.payment-method-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-method-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.payment-method-card:hover {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.payment-method-card.selected {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.payment-method-card.selected::before {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #28a745;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.method-icon {
    font-size: 2rem;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.amount-input-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #28a745;
}

.amount-display {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    margin-top: 1rem;
    border: 2px solid #28a745;
}

.amount-number {
    font-size: 2rem;
    font-weight: 700;
    color: #28a745;
}

.remaining-alert {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.btn-submit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.help-section {
    background: linear-gradient(135deg, #e2e3e5 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="payment-form-card">
            <!-- رأس النموذج -->
            <div class="form-header">
                <div class="mb-3">
                    <i class="fas fa-money-bill-wave fa-3x"></i>
                </div>
                <h3 class="mb-0">{{ title }}</h3>
                <p class="mb-0 mt-2 opacity-75">إضافة دفعة جديدة لحساب المتجر</p>
            </div>

            <!-- ملخص الحساب -->
            <div class="card-body p-4">
                <div class="account-summary">
                    <h6 class="text-center mb-3">
                        <i class="fas fa-store me-2"></i>
                        ملخص حساب {{ store_account.store.store_name }}
                    </h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="summary-item">
                                <div class="summary-number text-warning">{{ store_account.commission_amount|floatformat:0 }}</div>
                                <div class="summary-label">إجمالي العمولة (د.ع)</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <div class="summary-number text-success">{{ store_account.paid_amount|floatformat:0 }}</div>
                                <div class="summary-label">المدفوع (د.ع)</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <div class="summary-number text-danger">{{ store_account.remaining_amount|floatformat:0 }}</div>
                                <div class="summary-label">المتبقي (د.ع)</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <div class="summary-number text-primary">{{ store_account.orders_count }}</div>
                                <div class="summary-label">عدد الطلبات</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- النموذج -->
                <form method="post" id="paymentForm">
                    {% csrf_token %}
                    
                    <!-- طريقة الدفع -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-credit-card me-2"></i>
                            طريقة الدفع
                        </label>
                        <div class="payment-method-selector">
                            <div class="payment-method-card" data-method="cash">
                                <div class="method-icon">
                                    <i class="fas fa-money-bill"></i>
                                </div>
                                <h6>نقدي</h6>
                            </div>
                            <div class="payment-method-card" data-method="bank_transfer">
                                <div class="method-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <h6>تحويل بنكي</h6>
                            </div>
                            <div class="payment-method-card" data-method="check">
                                <div class="method-icon">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <h6>شيك</h6>
                            </div>
                            <div class="payment-method-card" data-method="online">
                                <div class="method-icon">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <h6>دفع إلكتروني</h6>
                            </div>
                        </div>
                        {{ form.payment_method }}
                        {% if form.payment_method.errors %}
                            <div class="text-danger small">{{ form.payment_method.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- المبلغ -->
                    <div class="amount-input-section">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-calculator me-2"></i>
                            مبلغ الدفعة
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">المبلغ (د.ع)</label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger small">{{ form.amount.errors }}</div>
                                {% endif %}
                                <div class="form-text">الحد الأقصى: {{ store_account.remaining_amount|floatformat:0 }} د.ع</div>
                            </div>
                            <div class="col-md-6">
                                <div class="amount-display">
                                    <div class="amount-number" id="amountDisplay">0</div>
                                    <small class="text-muted">دينار عراقي</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="remaining-alert">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>المبلغ المتبقي بعد هذه الدفعة:</strong>
                                </div>
                                <div>
                                    <span class="badge bg-warning" id="remainingAfter">{{ store_account.remaining_amount|floatformat:0 }} د.ع</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تاريخ الدفع -->
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.payment_date.id_for_label }}" class="form-label">
                                <i class="fas fa-calendar me-2"></i>
                                تاريخ الدفع
                            </label>
                            {{ form.payment_date }}
                            {% if form.payment_date.errors %}
                                <div class="text-danger small">{{ form.payment_date.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.reference_number.id_for_label }}" class="form-label">
                                <i class="fas fa-hashtag me-2"></i>
                                رقم المرجع (اختياري)
                            </label>
                            {{ form.reference_number }}
                            {% if form.reference_number.errors %}
                                <div class="text-danger small">{{ form.reference_number.errors }}</div>
                            {% endif %}
                            <div class="form-text">رقم الإيصال أو المرجع البنكي</div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            <i class="fas fa-sticky-note me-2"></i>
                            ملاحظات (اختياري)
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors }}</div>
                        {% endif %}
                        <div class="form-text">أضف أي ملاحظات إضافية عن هذه الدفعة</div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'store_account_detail' store_account.pk %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للحساب
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="previewPayment()">
                                <i class="fas fa-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-save me-2"></i>
                                حفظ الدفعة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قسم المساعدة -->
        <div class="help-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>
                نصائح مهمة
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من صحة المبلغ قبل الحفظ
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            احتفظ برقم المرجع للمتابعة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            يمكن إضافة دفعات متعددة
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-info text-primary me-2"></i>
                            الدفعة النقدية: لا تحتاج رقم مرجع
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info text-primary me-2"></i>
                            التحويل البنكي: أضف رقم العملية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info text-primary me-2"></i>
                            الشيك: أضف رقم الشيك
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('{{ form.payment_method.id_for_label }}');
    const methodCards = document.querySelectorAll('.payment-method-card');
    const amountInput = document.getElementById('{{ form.amount.id_for_label }}');
    const amountDisplay = document.getElementById('amountDisplay');
    const remainingAfter = document.getElementById('remainingAfter');
    const remainingAmount = {{ store_account.remaining_amount }};
    
    // تحديد طريقة الدفع المحددة
    function updateSelectedMethod() {
        const selectedMethod = paymentMethodSelect.value;
        
        methodCards.forEach(card => {
            card.classList.remove('selected');
            if (card.dataset.method === selectedMethod) {
                card.classList.add('selected');
            }
        });
    }
    
    // ربط النقر على البطاقات
    methodCards.forEach(card => {
        card.addEventListener('click', function() {
            paymentMethodSelect.value = this.dataset.method;
            updateSelectedMethod();
        });
    });
    
    // ربط تغيير القائمة المنسدلة
    paymentMethodSelect.addEventListener('change', updateSelectedMethod);
    
    // تحديث عرض المبلغ
    function updateAmountDisplay() {
        const amount = parseFloat(amountInput.value) || 0;
        amountDisplay.textContent = amount.toLocaleString('ar-EG');
        
        const remaining = remainingAmount - amount;
        remainingAfter.textContent = remaining.toLocaleString('ar-EG') + ' د.ع';
        
        if (remaining < 0) {
            remainingAfter.className = 'badge bg-danger';
        } else if (remaining === 0) {
            remainingAfter.className = 'badge bg-success';
        } else {
            remainingAfter.className = 'badge bg-warning';
        }
    }
    
    // ربط تحديث المبلغ
    amountInput.addEventListener('input', updateAmountDisplay);
    
    // معاينة الدفعة
    window.previewPayment = function() {
        const amount = amountInput.value || 0;
        const method = paymentMethodSelect.options[paymentMethodSelect.selectedIndex].text;
        const date = document.getElementById('{{ form.payment_date.id_for_label }}').value;
        const reference = document.getElementById('{{ form.reference_number.id_for_label }}').value;
        
        let preview = 'معاينة الدفعة:\n\n';
        preview += `المبلغ: ${amount} د.ع\n`;
        preview += `طريقة الدفع: ${method}\n`;
        preview += `التاريخ: ${date}\n`;
        if (reference) {
            preview += `رقم المرجع: ${reference}\n`;
        }
        preview += `المتبقي بعد الدفعة: ${remainingAmount - amount} د.ع`;
        
        alert(preview);
    };
    
    // تحقق من صحة النموذج
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value) || 0;
        
        if (amount <= 0) {
            e.preventDefault();
            alert('يجب إدخال مبلغ صحيح');
            return false;
        }
        
        if (amount > remainingAmount) {
            e.preventDefault();
            alert('المبلغ لا يمكن أن يتجاوز المبلغ المتبقي');
            return false;
        }
        
        if (!confirm(`هل أنت متأكد من إضافة دفعة بمبلغ ${amount} د.ع؟`)) {
            e.preventDefault();
            return false;
        }
    });
    
    // تهيئة أولية
    updateSelectedMethod();
    updateAmountDisplay();
    
    // تعيين التاريخ الحالي
    const now = new Date();
    const dateString = now.toISOString().slice(0, 16);
    document.getElementById('{{ form.payment_date.id_for_label }}').value = dateString;
});
</script>
{% endblock %}
