{% extends 'dashboard/base.html' %}

{% block title %}المناطق - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة المناطق</h1>
        <p class="text-muted">عرض وإدارة جميع المناطق في النظام</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="{% url 'areas_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة منطقة جديدة
            </a>
            <button type="button"
                    class="btn btn-success bulk-add-btn"
                    data-entity="areas"
                    data-url="{% url 'bulk_add_areas' %}"
                    data-fields='[{"name": "area_name", "label": "اسم المنطقة", "type": "text", "required": true, "placeholder": "أدخل اسم المنطقة"}]'>
                <i class="fas fa-layer-group me-2"></i>
                إضافة متعددة
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-map-marker-alt me-2"></i>
            قائمة المناطق
        </h5>
    </div>
    <div class="card-body">
        {% if areas %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المنطقة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for area in areas %}
                        <tr>
                            <td>{{ area.id }}</td>
                            <td>{{ area.area_name }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'areas_edit' area.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'areas_delete' area.pk %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-map-marker-alt fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مناطق</h5>
                <p class="text-muted">لم يتم إضافة أي مناطق حتى الآن</p>
                <a href="{% url 'areas_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول منطقة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
