# Generated by Django 4.1.4 on 2025-07-08 10:14

from django.conf import settings
import django.contrib.auth.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Areas',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('area_name', models.CharField(max_length=255, verbose_name='اسم المنطقة')),
            ],
            options={
                'verbose_name': 'منطقة',
                'verbose_name_plural': 'المناطق',
            },
        ),
        migrations.CreateModel(
            name='BasketsTestHeaders',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_numbers', models.CharField(max_length=255, verbose_name='رقم الفاتورة')),
                ('csrf_cookies', models.CharField(max_length=255, verbose_name='كوكيز CSRF')),
                ('order_hawalah_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحوالة')),
                ('order_state', models.CharField(blank=True, max_length=50, null=True, verbose_name='حالة الطلب')),
                ('request_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('get_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاستلام')),
                ('peson_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم الشخص')),
                ('phone_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الهاتف')),
                ('city', models.CharField(blank=True, max_length=50, null=True, verbose_name='المدينة')),
                ('place', models.CharField(max_length=255, verbose_name='المكان')),
                ('enables', models.IntegerField(default=0, verbose_name='مفعل')),
            ],
            options={
                'verbose_name': 'رأس سلة اختبار',
                'verbose_name_plural': 'رؤوس سلال الاختبار',
            },
        ),
        migrations.CreateModel(
            name='Bills',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_numbers', models.CharField(max_length=255, verbose_name='رقم الفاتورة')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
            },
        ),
        migrations.CreateModel(
            name='Categ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('categ_name', models.CharField(max_length=255, verbose_name='اسم الفئة')),
                ('categ_picture', models.FileField(blank=True, max_length=255, null=True, upload_to='categ_pictures/', verbose_name='صورة الفئة')),
                ('note', models.CharField(blank=True, max_length=1000, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
            },
        ),
        migrations.CreateModel(
            name='Companies',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم الشركة')),
            ],
            options={
                'verbose_name': 'شركة',
                'verbose_name_plural': 'الشركات',
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='الاسم')),
                ('phone_number', models.CharField(max_length=255, verbose_name='رقم الهاتف')),
                ('place', models.CharField(max_length=255, verbose_name='المكان')),
                ('notes', models.CharField(blank=True, max_length=500, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
            },
        ),
        migrations.CreateModel(
            name='Famlies',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('family_name', models.CharField(max_length=255, verbose_name='اسم العائلة')),
                ('note', models.CharField(blank=True, max_length=1000, null=True, verbose_name='ملاحظات')),
                ('categ', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='dashboard.categ', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'عائلة منتج',
                'verbose_name_plural': 'عائلات المنتجات',
            },
        ),
        migrations.CreateModel(
            name='Products',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True, verbose_name='الرابط')),
                ('product_name', models.CharField(max_length=255, verbose_name='اسم المنتج')),
                ('product_description', models.TextField(blank=True, null=True, verbose_name='وصف المنتج')),
                ('seo', models.CharField(blank=True, max_length=1000, null=True, verbose_name='كلمات مفتاحية')),
                ('price', models.IntegerField(default=0, verbose_name='السعر')),
                ('product_picture', models.FileField(max_length=255, upload_to='product_pictures/', verbose_name='صورة المنتج')),
                ('product_picture_backend', models.FileField(max_length=255, upload_to='product_pictures/', verbose_name='صورة المنتج الخلفية')),
                ('enable', models.BooleanField(default=True, verbose_name='مفعل')),
                ('notification', models.BooleanField(default=True, verbose_name='الإشعارات')),
                ('note', models.CharField(blank=True, max_length=1000, null=True, verbose_name='ملاحظات')),
                ('views', models.IntegerField(default=0, verbose_name='المشاهدات')),
                ('state_fouder', models.BooleanField(default=True, verbose_name='حالة المؤسس')),
                ('is_static_in_main_screen', models.BooleanField(default=False, verbose_name='ثابت في الشاشة الرئيسية')),
                ('datetimes', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('categ', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='dashboard.categ', verbose_name='الفئة')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='dashboard.companies', verbose_name='الشركة')),
                ('family', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='dashboard.famlies', verbose_name='العائلة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
            },
        ),
        migrations.CreateModel(
            name='StoreType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('store_type', models.CharField(max_length=255, verbose_name='نوع المتجر')),
            ],
            options={
                'verbose_name': 'نوع متجر',
                'verbose_name_plural': 'أنواع المتاجر',
            },
        ),
        migrations.CreateModel(
            name='Stores',
            fields=[
                ('user_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('store_name', models.CharField(max_length=255, verbose_name='اسم المتجر')),
                ('lat', models.CharField(max_length=255, verbose_name='خط العرض')),
                ('long', models.CharField(max_length=255, verbose_name='خط الطول')),
                ('store_person_name', models.CharField(max_length=255, verbose_name='اسم مسؤول المتجر')),
                ('store_person_phone_number', models.CharField(max_length=255, verbose_name='رقم هاتف مسؤول المتجر')),
                ('store_picture', models.FileField(max_length=255, upload_to='store_pictures/', verbose_name='صورة المتجر')),
                ('enable', models.BooleanField(default=True, verbose_name='مفعل')),
                ('notification', models.BooleanField(default=True, verbose_name='الإشعارات')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('note', models.CharField(blank=True, max_length=1000, null=True, verbose_name='ملاحظات')),
                ('is_static_in_main_screen', models.BooleanField(default=False, verbose_name='ثابت في الشاشة الرئيسية')),
                ('is_found_24_for_food_stor', models.BooleanField(default=False, verbose_name='متوفر 24 ساعة للطعام')),
                ('price_stor', models.IntegerField(default=0, verbose_name='إيجار المحل')),
                ('area', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='dashboard.areas', verbose_name='المنطقة')),
                ('store_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='dashboard.storetype', verbose_name='نوع المتجر')),
            ],
            options={
                'verbose_name': 'متجر',
                'verbose_name_plural': 'المتاجر',
            },
            bases=('auth.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='ProductsTageem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ok', models.BooleanField(default=False, verbose_name='موافق')),
                ('datetimes', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('notes', models.CharField(blank=True, max_length=500, null=True, verbose_name='ملاحظات')),
                ('custom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.customer', verbose_name='العميل')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.products', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'تقييم منتج',
                'verbose_name_plural': 'تقييمات المنتجات',
            },
        ),
        migrations.CreateModel(
            name='ProductsImg',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('img', models.FileField(max_length=255, upload_to='product_pictures/', verbose_name='الصورة')),
                ('datetimes', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.products', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة منتج',
                'verbose_name_plural': 'صور المنتجات',
            },
        ),
        migrations.AddField(
            model_name='products',
            name='store',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='dashboard.stores', verbose_name='المتجر'),
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=0, max_digits=10, verbose_name='السعر')),
                ('count', models.IntegerField(default=1, verbose_name='الكمية')),
                ('all_price', models.DecimalField(decimal_places=0, max_digits=10, verbose_name='السعر الإجمالي')),
                ('order_hawalah_number', models.CharField(max_length=50, verbose_name='رقم الحوالة')),
                ('order_state', models.CharField(blank=True, max_length=50, null=True, verbose_name='حالة الطلب')),
                ('request_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('get_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاستلام')),
                ('custom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.customer', verbose_name='العميل')),
                ('products', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.products', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
            },
        ),
        migrations.CreateModel(
            name='Notifications',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notifications_text', models.TextField(verbose_name='نص الإشعار')),
                ('from_date', models.DateField(verbose_name='من تاريخ')),
                ('to_date', models.DateField(verbose_name='إلى تاريخ')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('products', models.CharField(blank=True, max_length=255, null=True, verbose_name='المنتجات')),
                ('date', models.DateField(auto_now_add=True, verbose_name='التاريخ')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='dashboard.stores', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
            },
        ),
        migrations.CreateModel(
            name='CustomerTageem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ok', models.BooleanField(default=False, verbose_name='موافق')),
                ('datetimes', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('notes', models.CharField(blank=True, max_length=500, null=True, verbose_name='ملاحظات')),
                ('custom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.customer', verbose_name='العميل')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.stores', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'تقييم عميل',
                'verbose_name_plural': 'تقييمات العملاء',
            },
        ),
        migrations.CreateModel(
            name='BasketsTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('count', models.IntegerField(default=1, verbose_name='الكمية')),
                ('all_price', models.DecimalField(decimal_places=0, max_digits=10, verbose_name='السعر الإجمالي')),
                ('basket_headers', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='dashboard.basketstestheaders', verbose_name='رأس السلة')),
                ('products', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.products', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'سلة اختبار',
                'verbose_name_plural': 'سلال الاختبار',
            },
        ),
        migrations.CreateModel(
            name='Baskets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('place', models.CharField(max_length=255, verbose_name='المكان')),
                ('bill_numbers', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.bills', verbose_name='رقم الفاتورة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'سلة',
                'verbose_name_plural': 'السلال',
            },
        ),
    ]
