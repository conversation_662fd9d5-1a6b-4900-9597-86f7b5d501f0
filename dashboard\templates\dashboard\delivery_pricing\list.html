{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تسعير التوصيل حسب المسافة{% endblock %}

{% block extra_css %}
<style>
.pricing-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
}

.pricing-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.distance-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: bold;
    font-size: 1.1rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.price-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: bold;
    font-size: 1.2rem;
    display: inline-block;
}

.distance-meters {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
}

.status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calculator text-primary"></i>
            تسعير التوصيل حسب المسافة
        </h1>
        <div>
            <a href="{% url 'delivery_pricing_calculator' %}" class="btn btn-info btn-sm">
                <i class="fas fa-calculator"></i>
                حاسبة التكلفة
            </a>
            <button type="button" class="btn btn-success btn-sm" onclick="syncDeliveryPricingToFirebase()">
                <i class="fas fa-sync-alt"></i>
                مزامنة مع Firebase
            </button>
            <a href="{% url 'delivery_pricing_create' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i>
                إضافة نطاق جديد
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                فلاتر البحث
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-4 mb-3">
                    <label for="is_active" class="form-label">الحالة</label>
                    <select name="is_active" id="is_active" class="form-control">
                        <option value="">جميع الحالات</option>
                        <option value="true" {% if current_status == "true" %}selected{% endif %}>نشط</option>
                        <option value="false" {% if current_status == "false" %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="ابحث في الوصف..." value="{{ search_query }}">
                </div>
                
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة التسعير -->
    <div class="row">
        {% for pricing in pricing_list %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="pricing-card">
                <div class="card-body">
                    <!-- نطاق المسافة -->
                    <div class="text-center mb-3">
                        <div class="distance-badge">
                            <i class="fas fa-route"></i>
                            {{ pricing.distance_range_km }}
                        </div>
                        <div class="distance-meters">
                            {{ pricing.distance_range_text }}
                        </div>
                    </div>
                    
                    <!-- السعر -->
                    <div class="text-center mb-3">
                        <div class="price-badge">
                            <i class="fas fa-money-bill-wave"></i>
                            {{ pricing.price_per_delivery }} ريال يمني
                        </div>
                    </div>
                    
                    <!-- الوصف -->
                    {% if pricing.description %}
                    <div class="text-center mb-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            {{ pricing.description }}
                        </small>
                    </div>
                    {% endif %}
                    
                    <!-- الحالة -->
                    <div class="text-center mb-3">
                        {% if pricing.is_active %}
                        <span class="badge status-active">
                            <i class="fas fa-check-circle"></i>
                            نشط
                        </span>
                        {% else %}
                        <span class="badge status-inactive">
                            <i class="fas fa-pause-circle"></i>
                            غير نشط
                        </span>
                        {% endif %}
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="small text-muted text-center mb-3">
                        <div><i class="fas fa-calendar"></i> {{ pricing.created_at|date:"Y/m/d" }}</div>
                        {% if pricing.created_by %}
                        <div><i class="fas fa-user"></i> {{ pricing.created_by.username }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="{% url 'delivery_pricing_edit' pricing.id %}"
                               class="btn btn-warning" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'delivery_pricing_delete' pricing.id %}"
                               class="btn btn-danger" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>

                        <!-- مؤشر Firebase -->
                        <small class="text-success" title="متزامن مع Firebase Firestore">
                            <i class="fas fa-cloud"></i>
                            Firebase
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calculator fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">لا يوجد تسعير محدد</h5>
                    <p class="text-gray-500">لم يتم إنشاء أي نطاقات تسعير بعد</p>
                    <a href="{% url 'delivery_pricing_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة نطاق جديد
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        كيف يعمل النظام؟
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                                <h6>1. تحديد المواقع</h6>
                                <p class="small text-muted">يتم تحديد موقع المتجر وموقع العميل</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-route fa-2x text-success mb-2"></i>
                                <h6>2. حساب المسافة</h6>
                                <p class="small text-muted">يتم حساب المسافة بالمتر باستخدام الإحداثيات</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-money-bill-wave fa-2x text-warning mb-2"></i>
                                <h6>3. تحديد السعر</h6>
                                <p class="small text-muted">يتم تحديد السعر حسب النطاق المناسب</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// مزامنة تسعير التوصيل مع Firebase
function syncDeliveryPricingToFirebase() {
    if (confirm('هل أنت متأكد من مزامنة جميع تسعيرات التوصيل مع Firebase Firestore؟')) {
        // إظهار مؤشر التحميل
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المزامنة...';
        btn.disabled = true;

        // إرسال طلب المزامنة
        fetch('{% url "sync_delivery_pricing_to_firebase" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                // إعادة تحميل الصفحة لعرض رسائل النجاح
                window.location.reload();
            } else {
                throw new Error('فشل في المزامنة');
            }
        })
        .catch(error => {
            console.error('خطأ في المزامنة:', error);
            alert('حدث خطأ أثناء المزامنة. يرجى المحاولة مرة أخرى.');

            // إعادة تعيين الزر
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

// دالة للحصول على CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>

{% endblock %}
