from django.core.management.base import BaseCommand
from django.db import connection
from datetime import datetime


class Command(BaseCommand):
    help = 'إنشاء جداول التقييمات مباشرة في قاعدة البيانات'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔄 بدء إنشاء جداول التقييمات...'))
        
        with connection.cursor() as cursor:
            try:
                # إنشاء جدول التقييمات
                self.stdout.write('📋 إنشاء جدول dashboard_rating...')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dashboard_rating (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        rating_type VARCHAR(20) NOT NULL,
                        rating_value INTEGER NOT NULL,
                        comment TEXT,
                        rated_delivery_person VARCHAR(255),
                        is_public BOOLEAN NOT NULL DEFAULT 1,
                        is_verified BOOLEAN NOT NULL DEFAULT 0,
                        admin_response TEXT,
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        order_id INTEGER,
                        rated_customer_id INTEGER,
                        rated_store_id INTEGER,
                        reviewer_customer_id INTEGER,
                        reviewer_store_id INTEGER,
                        FOREIGN KEY (order_id) REFERENCES dashboard_order (id),
                        FOREIGN KEY (rated_customer_id) REFERENCES dashboard_customer (id),
                        FOREIGN KEY (rated_store_id) REFERENCES dashboard_stores (id),
                        FOREIGN KEY (reviewer_customer_id) REFERENCES dashboard_customer (id),
                        FOREIGN KEY (reviewer_store_id) REFERENCES dashboard_stores (id)
                    )
                ''')
                
                # إنشاء جدول إحصائيات التقييمات
                self.stdout.write('📊 إنشاء جدول dashboard_ratingstatistics...')
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dashboard_ratingstatistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        delivery_person_name VARCHAR(255) UNIQUE,
                        total_ratings INTEGER NOT NULL DEFAULT 0,
                        average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00,
                        five_stars INTEGER NOT NULL DEFAULT 0,
                        four_stars INTEGER NOT NULL DEFAULT 0,
                        three_stars INTEGER NOT NULL DEFAULT 0,
                        two_stars INTEGER NOT NULL DEFAULT 0,
                        one_star INTEGER NOT NULL DEFAULT 0,
                        last_updated DATETIME NOT NULL,
                        customer_id INTEGER UNIQUE,
                        store_id INTEGER UNIQUE,
                        FOREIGN KEY (customer_id) REFERENCES dashboard_customer (id),
                        FOREIGN KEY (store_id) REFERENCES dashboard_stores (id)
                    )
                ''')
                
                # إضافة سجل الهجرة
                self.stdout.write('🔄 تحديث جدول django_migrations...')
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
                cursor.execute('''
                    INSERT OR IGNORE INTO django_migrations (app, name, applied) 
                    VALUES ('dashboard', '0008_rating_ratingstatistics', ?)
                ''', [current_time])
                
                # التحقق من الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dashboard_rating%'")
                tables = cursor.fetchall()
                
                self.stdout.write(self.style.SUCCESS('✅ تم إنشاء الجداول بنجاح:'))
                for table in tables:
                    self.stdout.write(f'  - {table[0]}')
                
                self.stdout.write(self.style.SUCCESS('🎉 تم إنشاء نظام التقييمات بنجاح!'))
                self.stdout.write('🌐 يمكنك الآن الوصول إلى: http://127.0.0.1:8000/ratings/')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'❌ خطأ في إنشاء الجداول: {e}'))
                raise
