<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - شركة زاد للتوصيل</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <style>
        :root {
            --zad-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --zad-secondary: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            --zad-accent: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            --zad-success: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            --zad-warning: linear-gradient(135deg, #f39c12 0%, #d68910 100%);
            --zad-info: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(52, 73, 94, 0.2);
            --shadow-light: 0 8px 32px rgba(44, 62, 80, 0.15);
            --shadow-heavy: 0 15px 35px rgba(44, 62, 80, 0.25);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            background: #ffffff !important;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية بيضاء نظيفة */
        body::before {
            display: none !important;
        }

        /* Floating Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Main Container */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* Login Card */
        .login-card {
            background: white;
            border: 1px solid rgba(229, 231, 235, 0.8);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
            padding: 0;
            overflow: hidden;
            max-width: 420px;
            width: 100%;
            position: relative;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--zad-primary);
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Header */
        .login-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .company-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255,255,255,0.5);
            position: relative;
            z-index: 2;
        }

        .company-name {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .company-tagline {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 2;
        }

        /* Form Body */
        .login-body {
            padding: 40px 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.8rem;
        }

        /* Form Groups */
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px 15px 50px;
            font-size: 1.1rem;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
            background: white;
            transform: scale(1.02);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.2rem;
            z-index: 10;
        }

        /* Login Button */
        .btn-login {
            width: 100%;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: 500;
            border: none;
            border-radius: 4px;
            background: #1e3a8a;
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.025em;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: var(--transition);
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
        }

        /* Remember Me */
        .form-check {
            margin: 20px 0;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        /* Footer Links */
        .login-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .login-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .login-footer a:hover {
            color: #5a6fd8;
            transform: translateY(-1px);
        }

        /* Error Messages */
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 193, 7, 0.1));
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        /* Loading State */
        .btn-login.loading {
            pointer-events: none;
        }

        .btn-login.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-card {
                margin: 10px;
                max-width: none;
            }
            
            .company-name {
                font-size: 2rem;
            }
            
            .company-logo {
                font-size: 3rem;
            }
            
            .login-header {
                padding: 30px 20px;
            }
            
            .login-body {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 2.5s;"></div>
    </div>

    <div class="login-container">
        <div class="login-card animate__animated animate__fadeInUp">
            <!-- Header -->
            <div class="login-header">
                <div class="company-logo animate__animated animate__bounceIn animate__delay-1s">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="company-name animate__animated animate__fadeInDown animate__delay-2s">
                    شركة زاد
                </h1>
                <p class="company-tagline animate__animated animate__fadeInUp animate__delay-3s">
                    نحو مستقبل أفضل في عالم التوصيل
                </p>
            </div>

            <!-- Body -->
            <div class="login-body">
                <h2 class="form-title animate__animated animate__fadeInDown animate__delay-4s">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </h2>

                {% if form.errors %}
                    <div class="alert alert-danger animate__animated animate__shakeX">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يرجى التحقق من البيانات المدخلة
                    </div>
                {% endif %}

                <form method="post" id="loginForm">
                    {% csrf_token %}
                    
                    <div class="form-group animate__animated animate__fadeInRight animate__delay-5s">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            اسم المستخدم
                        </label>
                        <div class="position-relative">
                            <input type="text" 
                                   class="form-control" 
                                   id="{{ form.username.id_for_label }}"
                                   name="{{ form.username.name }}"
                                   placeholder="أدخل اسم المستخدم"
                                   required>
                            <i class="fas fa-user input-icon"></i>
                        </div>
                    </div>

                    <div class="form-group animate__animated animate__fadeInLeft animate__delay-6s">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            كلمة المرور
                        </label>
                        <div class="position-relative">
                            <input type="password" 
                                   class="form-control" 
                                   id="{{ form.password.id_for_label }}"
                                   name="{{ form.password.name }}"
                                   placeholder="أدخل كلمة المرور"
                                   required>
                            <i class="fas fa-lock input-icon"></i>
                            <button type="button" class="btn btn-link position-absolute" style="left: 50px; top: 50%; transform: translateY(-50%); z-index: 10;" onclick="togglePassword()">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-check animate__animated animate__fadeInUp animate__delay-7s">
                        <input class="form-check-input" type="checkbox" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            تذكرني
                        </label>
                    </div>

                    <button type="submit" class="btn btn-login animate__animated animate__fadeInUp animate__delay-8s">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        دخول
                    </button>
                </form>

                <div class="login-footer animate__animated animate__fadeInUp animate__delay-9s">
                    <a href="#" onclick="showForgotPassword()">
                        <i class="fas fa-question-circle me-1"></i>
                        نسيت كلمة المرور؟
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Sweet Alert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Show forgot password
        function showForgotPassword() {
            Swal.fire({
                title: 'استعادة كلمة المرور',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="resetEmail" placeholder="أدخل بريدك الإلكتروني">
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'إرسال',
                cancelButtonText: 'إلغاء',
                background: 'var(--glass-bg)',
                backdrop: 'rgba(0,0,0,0.8)',
                customClass: {
                    popup: 'glass'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'تم الإرسال!',
                        text: 'تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني',
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    });
                }
            });
        }

        // Form submission with loading
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.btn-login');
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span style="opacity: 0;">دخول</span>';
            
            // Remove loading state after 3 seconds if no redirect
            setTimeout(() => {
                submitBtn.classList.remove('loading');
                submitBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>دخول';
            }, 3000);
        });

        // Create more particles dynamically
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            document.querySelector('.particles').appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 1000);

        // Welcome message
        setTimeout(() => {
            Swal.fire({
                title: 'مرحباً بك في شركة زاد!',
                text: 'يرجى تسجيل الدخول للوصول إلى لوحة التحكم',
                icon: 'info',
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end',
                background: 'var(--glass-bg)',
                color: 'white'
            });
        }, 2000);
    </script>
</body>
</html>
