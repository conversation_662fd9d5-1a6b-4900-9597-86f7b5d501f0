{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'orders_list' %}">الطلبات</a></li>
<li class="breadcrumb-item"><a href="{% url 'orders_detail' order.pk %}">طلب #{{ order.id }}</a></li>
<li class="breadcrumb-item active">حذف</li>
{% endblock %}

{% block extra_css %}
<style>
.delete-warning {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border: 2px solid #fc8181;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.delete-icon {
    font-size: 4rem;
    color: #e53e3e;
    margin-bottom: 1rem;
}

.order-summary {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
}

.danger-text {
    color: #e53e3e;
    font-weight: 600;
}

.warning-list {
    text-align: right;
    margin: 1rem 0;
}

.warning-list li {
    margin-bottom: 0.5rem;
    color: #744210;
}

.btn-danger-confirm {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-danger-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}

.btn-safe {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-safe:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- تحذير الحذف -->
        <div class="delete-warning">
            <div class="delete-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="danger-text mb-3">تحذير: حذف الطلب</h3>
            <p class="mb-0">أنت على وشك حذف الطلب #{{ order.id }} نهائياً. هذا الإجراء لا يمكن التراجع عنه!</p>
        </div>

        <!-- ملخص الطلب -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    تفاصيل الطلب المراد حذفه
                </h5>
            </div>
            <div class="card-body">
                <div class="order-summary">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>رقم الطلب:</strong> #{{ order.id }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>تاريخ الطلب:</strong> {{ order.request_date|date:"Y/m/d H:i" }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>العميل:</strong> {{ order.custom.name|default:"غير محدد" }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>المنتج:</strong> {{ order.products.product_name|default:"غير محدد" }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>الكمية:</strong> {{ order.count }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>المبلغ الإجمالي:</strong> {{ order.all_price }} د.ع
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>رقم الحوالة:</strong> {{ order.order_hawalah_number }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>الحالة:</strong> 
                            <span class="badge bg-primary">{{ order.get_order_state_display }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحذيرات مهمة -->
        <div class="card mb-4">
            <div class="card-header bg-warning">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    تحذيرات مهمة قبل الحذف
                </h6>
            </div>
            <div class="card-body">
                <ul class="warning-list">
                    <li>
                        <i class="fas fa-times-circle text-danger me-2"></i>
                        سيتم حذف جميع بيانات الطلب نهائياً ولا يمكن استرجاعها
                    </li>
                    <li>
                        <i class="fas fa-database text-danger me-2"></i>
                        سيتم فقدان سجل المعاملة المالية المرتبطة بالطلب
                    </li>
                    <li>
                        <i class="fas fa-chart-bar text-danger me-2"></i>
                        ستتأثر الإحصائيات والتقارير المالية
                    </li>
                    <li>
                        <i class="fas fa-user text-danger me-2"></i>
                        قد يؤثر على سجل العميل وتاريخ طلباته
                    </li>
                    <li>
                        <i class="fas fa-store text-danger me-2"></i>
                        قد يؤثر على إحصائيات المتجر والمنتج
                    </li>
                </ul>
                
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-shield-alt me-2"></i>
                    <strong>بديل آمن:</strong> بدلاً من الحذف، يمكنك تغيير حالة الطلب إلى "ملغي" للاحتفاظ بالسجل
                </div>
            </div>
        </div>

        <!-- نموذج التأكيد -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    تأكيد الحذف
                </h6>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label danger-text" for="confirmDelete">
                                أؤكد أنني أريد حذف هذا الطلب نهائياً وأتحمل المسؤولية الكاملة
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-danger-confirm" id="deleteBtn" disabled>
                                <i class="fas fa-trash me-2"></i>
                                حذف الطلب نهائياً
                            </button>
                        </div>
                        
                        <div>
                            <a href="{% url 'orders_detail' order.pk %}" class="btn btn-safe me-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للطلب
                            </a>
                            <a href="{% url 'orders_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-2"></i>
                                قائمة الطلبات
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- بدائل آمنة -->
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    بدائل آمنة للحذف
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <form method="post" action="{% url 'orders_update_status' order.pk %}">
                                {% csrf_token %}
                                <input type="hidden" name="status" value="cancelled">
                                <button type="submit" class="btn btn-outline-warning">
                                    <i class="fas fa-ban me-2"></i>
                                    إلغاء الطلب (بدلاً من الحذف)
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{% url 'orders_edit' order.pk %}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>
                                تعديل بيانات الطلب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteBtn = document.getElementById('deleteBtn');
    
    confirmCheckbox.addEventListener('change', function() {
        deleteBtn.disabled = !this.checked;
        if (this.checked) {
            deleteBtn.classList.remove('btn-secondary');
            deleteBtn.classList.add('btn-danger-confirm');
        } else {
            deleteBtn.classList.remove('btn-danger-confirm');
            deleteBtn.classList.add('btn-secondary');
        }
    });
    
    // تأكيد إضافي عند الإرسال
    deleteBtn.addEventListener('click', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا الطلب نهائياً؟ لا يمكن التراجع عن هذا الإجراء!')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
