{% extends 'dashboard/base.html' %}

{% block title %}{{ period.name }} - تفاصيل الفترة المحاسبية{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item"><a href="{% url 'accounting_periods_list' %}">الفترات المحاسبية</a></li>
<li class="breadcrumb-item active">{{ period.name }}</li>
{% endblock %}

{% block extra_css %}
<style>
.period-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.period-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-open { background: rgba(40, 167, 69, 0.9); }
.status-closed { background: rgba(255, 193, 7, 0.9); }
.status-finalized { background: rgba(108, 117, 125, 0.9); }

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.store-account-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.store-account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.store-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.settlement-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.settled { background: #d4edda; color: #155724; }
.unsettled { background: #fff3cd; color: #856404; }

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-action {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الفترة -->
<div class="period-header">
    <span class="period-status status-{{ period.status }}">
        {{ period.get_status_display }}
    </span>
    
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">{{ period.name }}</h2>
            <p class="mb-0 opacity-75">
                <i class="fas fa-calendar me-2"></i>
                {{ period.start_date|date:"Y/m/d" }} - {{ period.end_date|date:"Y/m/d" }}
            </p>
            <small class="opacity-75">
                <i class="fas fa-tag me-1"></i>
                {{ period.get_period_type_display }}
            </small>
        </div>
        <div class="col-md-4 text-end">
            <div class="action-buttons">
                {% if period.status == 'open' %}
                <a href="{% url 'calculate_period_accounts' period.pk %}" 
                   class="quick-action"
                   onclick="return confirm('هل تريد إعادة حساب مستحقات هذه الفترة؟')">
                    <i class="fas fa-calculator me-2"></i>
                    حساب المستحقات
                </a>
                {% endif %}
                <a href="{% url 'accounting_reports' %}?period={{ period.pk }}" class="quick-action">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number text-primary">{{ total_stores }}</div>
        <div class="stat-label">متجر مشارك</div>
    </div>
    <div class="stat-card">
        <div class="stat-number text-success">{{ total_orders }}</div>
        <div class="stat-label">إجمالي الطلبات</div>
    </div>
    <div class="stat-card">
        <div class="stat-number text-info">{{ total_revenue|floatformat:0 }}</div>
        <div class="stat-label">الإيرادات (د.ع)</div>
    </div>
    <div class="stat-card">
        <div class="stat-number text-warning">{{ total_commission|floatformat:0 }}</div>
        <div class="stat-label">العمولات (د.ع)</div>
    </div>
    <div class="stat-card">
        <div class="stat-number text-success">{{ total_paid|floatformat:0 }}</div>
        <div class="stat-label">المدفوع (د.ع)</div>
    </div>
    <div class="stat-card">
        <div class="stat-number text-danger">{{ total_remaining|floatformat:0 }}</div>
        <div class="stat-label">المتبقي (د.ع)</div>
    </div>
</div>

<!-- حسابات المتاجر -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-store me-2"></i>
                        حسابات المتاجر في هذه الفترة
                    </h5>
                    <span class="badge bg-primary">{{ store_accounts|length }} متجر</span>
                </div>
            </div>
            <div class="card-body">
                {% if store_accounts %}
                    {% for account in store_accounts %}
                    <div class="store-account-card">
                        <div class="store-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1">
                                        <i class="fas fa-store text-primary me-2"></i>
                                        {{ account.store.store_name }}
                                    </h6>
                                    <small class="text-muted">{{ account.store.store_type.type_name }}</small>
                                </div>
                                <div class="col-md-6 text-end">
                                    <span class="settlement-badge {% if account.is_settled %}settled{% else %}unsettled{% endif %}">
                                        {% if account.is_settled %}مسدد بالكامل{% else %}غير مسدد{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center mb-3">
                                    <h5 class="text-primary mb-0">{{ account.orders_count }}</h5>
                                    <small class="text-muted">طلب</small>
                                </div>
                                <div class="col-md-3 text-center mb-3">
                                    <h5 class="text-success mb-0">{{ account.total_orders_value|floatformat:0 }}</h5>
                                    <small class="text-muted">قيمة الطلبات (د.ع)</small>
                                </div>
                                <div class="col-md-3 text-center mb-3">
                                    <h5 class="text-warning mb-0">{{ account.commission_amount|floatformat:0 }}</h5>
                                    <small class="text-muted">العمولة (د.ع)</small>
                                </div>
                                <div class="col-md-3 text-center mb-3">
                                    <h5 class="text-danger mb-0">{{ account.remaining_amount|floatformat:0 }}</h5>
                                    <small class="text-muted">المتبقي (د.ع)</small>
                                </div>
                            </div>
                            
                            <!-- شريط التقدم -->
                            <div class="progress-bar-custom mb-3">
                                <div class="progress-fill" style="width: {% if account.commission_amount > 0 %}{% widthratio account.paid_amount account.commission_amount 100 %}%{% else %}0%{% endif %}"></div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        نسبة السداد: 
                                        <strong>
                                            {% if account.commission_amount > 0 %}
                                                {% widthratio account.paid_amount account.commission_amount 100 %}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </strong>
                                    </small>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'store_account_detail' account.pk %}" 
                                       class="btn btn-outline-primary" title="تفاصيل الحساب">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if not account.is_settled %}
                                    <a href="{% url 'add_payment' account.pk %}" 
                                       class="btn btn-outline-success" title="إضافة دفعة">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-calculator fa-4x mb-3"></i>
                        <h5>لا توجد حسابات محسوبة</h5>
                        <p class="mb-4">لم يتم حساب مستحقات المتاجر لهذه الفترة بعد</p>
                        {% if period.status == 'open' %}
                        <a href="{% url 'calculate_period_accounts' period.pk %}" 
                           class="btn btn-primary"
                           onclick="return confirm('هل تريد حساب مستحقات هذه الفترة؟')">
                            <i class="fas fa-calculator me-2"></i>
                            حساب المستحقات الآن
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
{% if period.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات الفترة
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ period.notes }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_reports' %}?period={{ period.pk }}" 
                           class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <br>
                            تقارير الفترة
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'payments_list' %}?period={{ period.pk }}" 
                           class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-credit-card fa-2x mb-2"></i>
                            <br>
                            دفعات الفترة
                        </a>
                    </div>
                    {% if period.status == 'open' %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'calculate_period_accounts' period.pk %}" 
                           class="btn btn-outline-warning w-100 py-3"
                           onclick="return confirm('هل تريد إعادة حساب مستحقات هذه الفترة؟')">
                            <i class="fas fa-calculator fa-2x mb-2"></i>
                            <br>
                            إعادة الحساب
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'accounting_periods_list' %}" 
                           class="btn btn-outline-secondary w-100 py-3">
                            <i class="fas fa-list fa-2x mb-2"></i>
                            <br>
                            جميع الفترات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
