from django import template
from decimal import Decimal, InvalidOperation

register = template.Library()

@register.filter
def mul(value, arg):
    """ضرب قيمتين"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    """قسمة قيمتين"""
    try:
        if float(arg) == 0:
            return 0
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def sub(value, arg):
    """طرح قيمتين"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def add_custom(value, arg):
    """جمع قيمتين"""
    try:
        return float(value) + float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """حساب النسبة المئوية"""
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (Value<PERSON>rro<PERSON>, TypeError, ZeroDivisionError):
        return 0

@register.filter
def currency(value):
    """تنسيق العملة"""
    try:
        return "{:,.0f}".format(float(value))
    except (ValueError, TypeError):
        return "0"

@register.filter
def abs_value(value):
    """القيمة المطلقة"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0
