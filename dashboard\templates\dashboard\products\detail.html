{% extends 'dashboard/base.html' %}

{% block title %}{{ product.product_name }} - تفاصيل المنتج{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item">
    <a href="{% url 'products_list' %}">المنتجات</a>
</li>
<li class="breadcrumb-item active">{{ product.product_name }}</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h1 class="display-5 mb-2 animate__animated animate__fadeInUp">
            <i class="fas fa-box me-3"></i>
            تفاصيل المنتج
        </h1>
        <p class="lead text-white-50 animate__animated animate__fadeInUp animate__delay-1s">
            عرض تفاصيل شاملة للمنتج: {{ product.product_name }}
        </p>
    </div>
    <div class="animate__animated animate__fadeInRight animate__delay-2s">
        <a href="{% url 'products_edit' product.pk %}" class="btn btn-warning btn-lg me-2">
            <i class="fas fa-edit me-2"></i>
            تعديل المنتج
        </a>
        <a href="{% url 'products_list' %}" class="btn btn-secondary btn-lg">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Product Images -->
    <div class="col-lg-6 mb-4">
        <div class="card" data-aos="fade-right">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-images me-2"></i>
                    صور المنتج
                </h5>
            </div>
            <div class="card-body">
                {% if product.product_picture %}
                    <div class="text-center mb-3">
                        <img src="{{ product.product_picture.url }}" 
                             alt="{{ product.product_name }}" 
                             class="img-fluid rounded shadow"
                             style="max-height: 400px; object-fit: cover;">
                    </div>
                {% endif %}
                
                {% if product.product_picture_backend %}
                    <div class="text-center mb-3">
                        <h6>الصورة الخلفية:</h6>
                        <img src="{{ product.product_picture_backend.url }}" 
                             alt="{{ product.product_name }} - خلفية" 
                             class="img-fluid rounded shadow"
                             style="max-height: 300px; object-fit: cover;">
                    </div>
                {% endif %}
                
                {% if images %}
                    <h6>صور إضافية:</h6>
                    <div class="row">
                        {% for image in images %}
                        <div class="col-4 mb-2">
                            <img src="{{ image.img.url }}" 
                                 alt="صورة إضافية" 
                                 class="img-fluid rounded shadow-sm"
                                 style="height: 100px; object-fit: cover;">
                        </div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Product Details -->
    <div class="col-lg-6 mb-4">
        <div class="card" data-aos="fade-left">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المنتج
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>اسم المنتج:</strong></div>
                    <div class="col-sm-8">{{ product.product_name }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>السعر:</strong></div>
                    <div class="col-sm-8">
                        <span class="h4 text-success">{{ product.price }} د.ع</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>المتجر:</strong></div>
                    <div class="col-sm-8">{{ product.store.store_name|default:"غير محدد" }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>الفئة:</strong></div>
                    <div class="col-sm-8">{{ product.categ.categ_name|default:"غير محدد" }}</div>
                </div>
                
                {% if product.family %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>العائلة:</strong></div>
                    <div class="col-sm-8">{{ product.family.family_name }}</div>
                </div>
                {% endif %}
                
                {% if product.company %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>الشركة:</strong></div>
                    <div class="col-sm-8">{{ product.company.name }}</div>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>المشاهدات:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-info">{{ product.views }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>الحالة:</strong></div>
                    <div class="col-sm-8">
                        {% if product.enable %}
                            <span class="badge bg-success">مفعل</span>
                        {% else %}
                            <span class="badge bg-danger">معطل</span>
                        {% endif %}
                    </div>
                </div>
                
                {% if product.is_static_in_main_screen %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>مثبت في الرئيسية:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-warning">نعم</span>
                    </div>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>تاريخ الإضافة:</strong></div>
                    <div class="col-sm-8">{{ product.datetimes|date:"Y/m/d H:i" }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Description -->
{% if product.product_description %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card" data-aos="fade-up">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-align-left me-2"></i>
                    وصف المنتج
                </h5>
            </div>
            <div class="card-body">
                <p class="lead">{{ product.product_description }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- SEO and Notes -->
<div class="row">
    {% if product.seo %}
    <div class="col-lg-6 mb-4">
        <div class="card" data-aos="fade-right">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    كلمات مفتاحية (SEO)
                </h5>
            </div>
            <div class="card-body">
                <p>{{ product.seo }}</p>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if product.note %}
    <div class="col-lg-6 mb-4">
        <div class="card" data-aos="fade-left">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <p>{{ product.note }}</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Action Buttons -->
<div class="row">
    <div class="col-12">
        <div class="card" data-aos="fade-up">
            <div class="card-body text-center">
                <h5 class="mb-3">إجراءات المنتج</h5>
                <div class="btn-group" role="group">
                    <a href="{% url 'products_edit' product.pk %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المنتج
                    </a>
                    <button class="btn btn-success" onclick="duplicateProduct({{ product.pk }})">
                        <i class="fas fa-copy me-2"></i>
                        نسخ المنتج
                    </button>
                    <button class="btn btn-warning" onclick="toggleStatus({{ product.pk }})">
                        <i class="fas fa-toggle-on me-2"></i>
                        {% if product.enable %}إلغاء التفعيل{% else %}تفعيل{% endif %}
                    </button>
                    <button class="btn btn-danger" onclick="deleteProduct({{ product.pk }})">
                        <i class="fas fa-trash me-2"></i>
                        حذف المنتج
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function duplicateProduct(productId) {
        Swal.fire({
            title: 'نسخ المنتج',
            text: 'هل تريد إنشاء نسخة من هذا المنتج؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، انسخ',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire('تم النسخ!', 'تم إنشاء نسخة من المنتج بنجاح', 'success');
            }
        });
    }
    
    function toggleStatus(productId) {
        Swal.fire({
            title: 'تغيير حالة المنتج',
            text: 'هل تريد تغيير حالة تفعيل هذا المنتج؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، غير',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire('تم التغيير!', 'تم تغيير حالة المنتج بنجاح', 'success');
            }
        });
    }
    
    function deleteProduct(productId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'سيتم حذف هذا المنتج نهائياً',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = "{% url 'products_delete' product.pk %}";
            }
        });
    }
</script>
{% endblock %}
