from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    # Authentication
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', views.custom_logout, name='logout'),

    # الصفحة الرئيسية
    path('', views.dashboard_home, name='dashboard_home'),
    
    # المناطق
    path('areas/', views.areas_list, name='areas_list'),
    path('areas/create/', views.areas_create, name='areas_create'),
    path('areas/<int:pk>/edit/', views.areas_edit, name='areas_edit'),
    path('areas/<int:pk>/delete/', views.areas_delete, name='areas_delete'),
    
    # أنواع المتاجر
    path('store-types/', views.store_types_list, name='store_types_list'),
    path('store-types/create/', views.store_types_create, name='store_types_create'),
    path('store-types/<int:pk>/edit/', views.store_types_edit, name='store_types_edit'),
    path('store-types/<int:pk>/delete/', views.store_types_delete, name='store_types_delete'),
    
    # الشركات
    path('companies/', views.companies_list, name='companies_list'),
    path('companies/create/', views.companies_create, name='companies_create'),
    path('companies/<int:pk>/edit/', views.companies_edit, name='companies_edit'),
    path('companies/<int:pk>/delete/', views.companies_delete, name='companies_delete'),
    
    # المتاجر
    path('stores/', views.stores_list, name='stores_list'),
    path('stores/map/', views.stores_map, name='stores_map'),
    path('stores/create/', views.stores_create, name='stores_create'),
    path('stores/<int:pk>/', views.stores_detail, name='stores_detail'),
    path('stores/<int:pk>/edit/', views.stores_edit, name='stores_edit'),
    path('stores/<int:pk>/delete/', views.stores_delete, name='stores_delete'),
    path('stores/<int:pk>/regenerate-password/', views.stores_regenerate_password, name='stores_regenerate_password'),
    path('stores/sync-firebase/', views.sync_stores_to_firebase, name='sync_stores_to_firebase'),
    
    # الفئات
    path('categories/', views.categories_list, name='categories_list'),
    path('categories/create/', views.categories_create, name='categories_create'),
    path('categories/<int:pk>/edit/', views.categories_edit, name='categories_edit'),
    path('categories/<int:pk>/delete/', views.categories_delete, name='categories_delete'),
    
    # المنتجات
    path('products/', views.products_list, name='products_list'),
    path('products/create/', views.products_create, name='products_create'),
    path('products/<int:pk>/edit/', views.products_edit, name='products_edit'),
    path('products/<int:pk>/', views.products_detail, name='products_detail'),
    path('products/<int:pk>/delete/', views.products_delete, name='products_delete'),
    path('products/sync-firebase/', views.sync_products_to_firebase, name='sync_products_to_firebase'),
    
    # العملاء
    path('customers/', views.customers_list, name='customers_list'),
    path('customers/<int:pk>/', views.customers_detail, name='customers_detail'),
    
    # الطلبات القديمة (من قاعدة البيانات المحلية)
    path('local-orders/', views.orders_list, name='orders_list'),
    path('local-orders/<int:pk>/', views.orders_detail, name='orders_detail'),
    path('local-orders/<int:pk>/edit/', views.orders_edit, name='orders_edit'),
    path('local-orders/<int:pk>/update-status/', views.orders_update_status, name='orders_update_status'),
    path('local-orders/<int:pk>/add-note/', views.orders_add_note, name='orders_add_note'),
    path('local-orders/<int:pk>/print/', views.orders_print, name='orders_print'),
    path('local-orders/<int:pk>/history/', views.orders_history_old, name='orders_history_old'),
    path('local-orders/<int:pk>/delete/', views.orders_delete, name='orders_delete'),

    # النظام المحاسبي
    path('accounting/', views.accounting_dashboard, name='accounting_dashboard'),

    # إعدادات العمولات
    path('accounting/commission-settings/', views.commission_settings_list, name='commission_settings_list'),
    path('accounting/commission-settings/create/', views.commission_settings_create, name='commission_settings_create'),
    path('accounting/commission-settings/<int:pk>/edit/', views.commission_settings_edit, name='commission_settings_edit'),

    # الفترات المحاسبية
    path('accounting/periods/', views.accounting_periods_list, name='accounting_periods_list'),
    path('accounting/periods/create/', views.accounting_period_create, name='accounting_period_create'),
    path('accounting/periods/<int:pk>/', views.accounting_period_detail, name='accounting_period_detail'),
    path('accounting/periods/<int:pk>/calculate/', views.calculate_period_accounts, name='calculate_period_accounts'),

    # حسابات المتاجر والدفعات
    path('accounting/store-accounts/<int:pk>/', views.store_account_detail, name='store_account_detail'),
    path('accounting/store-accounts/<int:store_account_id>/add-payment/', views.add_payment, name='add_payment'),
    path('accounting/payments/', views.payments_list, name='payments_list'),

    # التقارير
    path('accounting/reports/', views.accounting_reports, name='accounting_reports'),

    # العروض الخاصة
    path('offers/', views.special_offers_list, name='special_offers_list'),
    path('offers/create/', views.special_offer_create, name='special_offer_create'),
    path('offers/<int:pk>/', views.special_offer_detail, name='special_offer_detail'),
    path('offers/<int:pk>/edit/', views.special_offer_edit, name='special_offer_edit'),
    path('offers/<int:pk>/toggle-status/', views.special_offer_toggle_status, name='special_offer_toggle_status'),
    path('offers/<int:pk>/delete/', views.special_offer_delete, name='special_offer_delete'),
    path('offers/sync-firebase/', views.sync_offers_to_firebase, name='sync_offers_to_firebase'),
    
    # الإشعارات
    path('notifications/', views.notifications_list, name='notifications_list'),
    path('notifications/create/', views.notifications_create, name='notifications_create'),
    
    # السلال
    path('baskets/', views.baskets_list, name='baskets_list'),
    path('baskets/<int:pk>/', views.baskets_detail, name='baskets_detail'),
    
    # API
    path('api/stats/', views.api_stats, name='api_stats'),

    # الإضافة المتعددة
    path('bulk-add/areas/', views.bulk_add_areas, name='bulk_add_areas'),
    path('bulk-add/companies/', views.bulk_add_companies, name='bulk_add_companies'),
    path('bulk-add/store-types/', views.bulk_add_store_types, name='bulk_add_store_types'),
    path('bulk-add/categories/', views.bulk_add_categories, name='bulk_add_categories'),
    path('bulk-add/customers/', views.bulk_add_customers, name='bulk_add_customers'),

    # تسعير التوصيل حسب المسافة
    path('delivery-pricing/', views.delivery_pricing_list, name='delivery_pricing_list'),
    path('delivery-pricing/create/', views.delivery_pricing_create, name='delivery_pricing_create'),
    path('delivery-pricing/<int:pricing_id>/edit/', views.delivery_pricing_edit, name='delivery_pricing_edit'),
    path('delivery-pricing/<int:pricing_id>/delete/', views.delivery_pricing_delete, name='delivery_pricing_delete'),
    path('delivery-pricing/calculator/', views.delivery_pricing_calculator, name='delivery_pricing_calculator'),
    path('delivery-pricing/sync-firebase/', views.sync_delivery_pricing_to_firebase, name='sync_delivery_pricing_to_firebase'),
    path('api/delivery-pricing/calculate/', views.delivery_pricing_calculate_api, name='delivery_pricing_calculate_api'),

    # متابعة الطلبات من Firebase
    path('orders/', views.orders_dashboard, name='orders_dashboard'),
    path('orders/history/', views.orders_history, name='orders_history'),
    path('orders/analytics/', views.orders_analytics, name='orders_analytics'),
    path('orders/<str:store_name>/<str:order_id>/', views.order_details, name='order_details'),
    path('api/orders/realtime/', views.orders_realtime_api, name='orders_realtime_api'),
    path('api/orders/stats/', views.orders_stats_api, name='orders_stats_api'),
]
