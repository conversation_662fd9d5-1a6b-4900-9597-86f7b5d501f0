# 🗺️ خريطة المتاجر التفاعلية

## 🌟 نظرة عامة
تم إنشاء خريطة تفاعلية متطورة لعرض جميع المتاجر والمطاعم المتعاقدة مع النظام، مع إمكانيات فلترة متقدمة وتجربة مستخدم استثنائية.

## ✨ الميزات الرئيسية

### 🎯 **1. عرض شامل للمتاجر:**
- **عرض جميع المتاجر** على خريطة تفاعلية
- **تمييز بصري** حسب حالة المتجر ونوعه
- **معلومات تفصيلية** لكل متجر
- **روابط مباشرة** لصفحة تفاصيل المتجر

### 🔍 **2. فلترة متقدمة:**
- **فلترة حسب المنطقة** - اختيار منطقة محددة
- **فلترة حسب نوع المتجر** - مطاعم، سوبر ماركت، إلخ
- **فلترة حسب الحالة** - نشط أو غير نشط
- **فلترة المتاجر 24 ساعة** - المتوفرة طوال اليوم
- **البحث النصي** - بحث في اسم المتجر أو المسؤول
- **تطبيق فوري** للفلاتر مع تحديث الخريطة

### 🎨 **3. تصميم متطور:**
- **واجهة عصرية** مع تدرجات لونية جميلة
- **إحصائيات سريعة** في أعلى الصفحة
- **لوحة فلاتر منظمة** وسهلة الاستخدام
- **أدوات تحكم متقدمة** للخريطة
- **مفتاح توضيحي** لفهم الرموز

### 🗺️ **4. تقنية الخرائط:**
- **OpenStreetMap** - مجاني وموثوق
- **Leaflet** - مكتبة خرائط متطورة
- **تجميع ذكي** للعلامات المتقاربة
- **تكبير وتصغير** سلس
- **توسيط تلقائي** على المتاجر

## 🎯 **التصميم والألوان:**

### **🎨 نظام الألوان:**
```css
/* رأس الصفحة */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)

/* العلامات على الخريطة */
🟢 أخضر (#28a745) - متاجر نشطة
🔴 أحمر (#dc3545) - متاجر غير نشطة  
🟡 أصفر (#ffc107) - متاجر مميزة
🔵 أزرق (#17a2b8) - متاجر 24 ساعة
```

### **📊 الإحصائيات:**
```
┌─────────────────────────────────────────────────────────────┐
│ 📊 إجمالي المتاجر    📈 متاجر نشطة    🕐 متوفر 24 ساعة    ⭐ متاجر مميزة │
│      [العدد]           [العدد]         [العدد]           [العدد]    │
└─────────────────────────────────────────────────────────────┘
```

### **🔧 لوحة الفلاتر:**
```
┌─────────────────────────────────────────────────────────────┐
│ 🔍 فلترة المتاجر على الخريطة                                │
├─────────────────────────────────────────────────────────────┤
│ [المنطقة ▼] [نوع المتجر ▼] [الحالة ▼] [24 ساعة ▼] [البحث...] [🔍] │
│                                                             │
│ [❌ مسح الفلاتر]                    عدد المتاجر المعروضة: XX │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ **التقنيات المستخدمة:**

### **🗺️ الخرائط:**
```html
<!-- Leaflet للخرائط -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<!-- MarkerCluster للتجميع -->
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
```

### **🎯 تهيئة الخريطة:**
```javascript
// إنشاء الخريطة
map = L.map('map').setView([33.3152, 44.3661], 11); // بغداد

// إضافة طبقة OpenStreetMap
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors',
    maxZoom: 19
}).addTo(map);

// إنشاء مجموعة التجميع
markerCluster = L.markerClusterGroup({
    iconCreateFunction: function(cluster) {
        const count = cluster.getChildCount();
        let color = count > 10 ? '#dc3545' : count > 5 ? '#ffc107' : '#28a745';
        return L.divIcon({
            html: `<div class="cluster-icon" style="background: ${color};">${count}</div>`,
            className: 'custom-cluster-icon'
        });
    }
});
```

### **📍 إنشاء العلامات:**
```javascript
function createStoreMarker(store) {
    // تحديد لون العلامة حسب حالة المتجر
    let iconColor = store.is_active ? 
        (store.is_featured ? '#ffc107' : 
         store.is_24_hours ? '#17a2b8' : '#28a745') : '#dc3545';
    
    // إنشاء أيقونة مخصصة
    const icon = L.divIcon({
        html: `
            <div style="background: ${iconColor}; border-radius: 50% 50% 50% 0; 
                        width: 25px; height: 25px; transform: rotate(-45deg); 
                        border: 3px solid white; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                <i class="fas fa-store" style="color: white; font-size: 10px; 
                   position: absolute; top: 50%; left: 50%; 
                   transform: translate(-50%, -50%) rotate(45deg);"></i>
            </div>
        `,
        iconSize: [25, 25],
        iconAnchor: [12, 25]
    });
    
    return L.marker([store.lat, store.lng], { icon: icon, title: store.name });
}
```

## 🔧 **الوظائف المتقدمة:**

### **🎛️ أدوات التحكم:**
```javascript
// التوسيط على بغداد
function centerOnBaghdad() {
    map.setView([33.3152, 44.3661], 11);
}

// عرض جميع المتاجر
function fitAllStores() {
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

// تبديل تجميع العلامات
function toggleClustering() {
    clusteringEnabled = !clusteringEnabled;
    if (clusteringEnabled) {
        applyMarkerClustering();
    } else {
        markers.forEach(marker => marker.addTo(map));
    }
}
```

### **🔍 نظام الفلترة:**
```javascript
function applyFilters() {
    const filters = {
        area: document.querySelector('[name="area"]').value,
        store_type: document.querySelector('[name="store_type"]').value,
        status: document.querySelector('[name="status"]').value,
        is_24_hours: document.querySelector('[name="is_24_hours"]').value,
        search: document.querySelector('[name="search"]').value
    };
    
    // إرسال طلب AJAX للخادم
    fetch(`/stores/map/?${new URLSearchParams(filters)}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.json())
    .then(data => {
        updateMap(data.stores);
        updateResultsCount(data.total_count);
    });
}
```

### **💬 نوافذ المعلومات:**
```javascript
function showStoreInfo(store, marker) {
    const content = `
        <div class="info-window">
            ${store.image ? `<img src="${store.image}" alt="${store.name}">` : ''}
            <h6>${store.name}</h6>
            <span class="store-type">${store.type}</span>
            
            <div class="detail-item">
                <i class="fas fa-map-marker-alt"></i> ${store.area}
            </div>
            <div class="detail-item">
                <i class="fas fa-user"></i> ${store.person_name}
            </div>
            <div class="detail-item">
                <i class="fas fa-phone"></i> 
                <a href="tel:${store.phone}">${store.phone}</a>
            </div>
            
            <div class="badges">
                ${store.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>'}
                ${store.is_featured ? '<span class="badge bg-warning">مميز</span>' : ''}
                ${store.is_24_hours ? '<span class="badge bg-info">24 ساعة</span>' : ''}
            </div>
            
            <a href="${store.detail_url}" class="btn-detail">
                <i class="fas fa-eye me-1"></i> عرض التفاصيل
            </a>
        </div>
    `;
    
    marker.bindPopup(content, { maxWidth: 350 }).openPopup();
}
```

## 📊 **Backend - معالجة البيانات:**

### **🔧 View الخريطة:**
```python
@login_required
def stores_map(request):
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # معالجة طلبات AJAX للفلترة
        stores = Stores.objects.filter(lat__isnull=False, long__isnull=False)
        
        # تطبيق الفلاتر
        if request.GET.get('area'):
            stores = stores.filter(area_id=request.GET.get('area'))
        
        if request.GET.get('store_type'):
            stores = stores.filter(store_type_id=request.GET.get('store_type'))
        
        if request.GET.get('status') == 'active':
            stores = stores.filter(enable=True)
        elif request.GET.get('status') == 'inactive':
            stores = stores.filter(enable=False)
        
        if request.GET.get('is_24_hours') == 'true':
            stores = stores.filter(is_found_24_for_food_stor=True)
        
        if request.GET.get('search'):
            stores = stores.filter(
                Q(store_name__icontains=request.GET.get('search')) |
                Q(store_person_name__icontains=request.GET.get('search'))
            )
        
        # تحويل البيانات إلى JSON
        stores_data = []
        for store in stores:
            stores_data.append({
                'id': store.id,
                'name': store.store_name,
                'lat': float(store.lat),
                'lng': float(store.long),
                'type': store.store_type.store_type,
                'area': store.area.area_name,
                'person_name': store.store_person_name,
                'phone': store.store_person_phone_number,
                'is_active': store.enable,
                'is_24_hours': store.is_found_24_for_food_stor,
                'is_featured': store.is_static_in_main_screen,
                'image': store.store_picture.url if store.store_picture else None,
                'description': store.description or '',
                'created_date': store.date_joined.strftime('%Y/%m/%d'),
                'detail_url': f'/stores/{store.id}/'
            })
        
        return JsonResponse({
            'stores': stores_data,
            'total_count': len(stores_data)
        })
    
    # عرض الصفحة العادية مع الإحصائيات
    context = {
        'areas': Areas.objects.all().order_by('area_name'),
        'store_types': StoreType.objects.all().order_by('store_type'),
        'total_stores': Stores.objects.filter(lat__isnull=False, long__isnull=False).count(),
        'active_stores': Stores.objects.filter(enable=True, lat__isnull=False, long__isnull=False).count(),
        'stores_24h': Stores.objects.filter(is_found_24_for_food_stor=True, lat__isnull=False, long__isnull=False).count(),
        'featured_stores': Stores.objects.filter(is_static_in_main_screen=True, lat__isnull=False, long__isnull=False).count(),
    }
    
    return render(request, 'dashboard/stores/map.html', context)
```

## 🎯 **سيناريوهات الاستخدام:**

### **📍 عرض جميع المتاجر:**
1. اذهب إلى `/stores/map/`
2. شاهد الإحصائيات السريعة
3. استكشف المتاجر على الخريطة
4. انقر على أي علامة لعرض التفاصيل

### **🔍 فلترة المتاجر:**
1. استخدم لوحة الفلاتر في أعلى الصفحة
2. اختر المنطقة المطلوبة
3. حدد نوع المتجر
4. فلتر حسب الحالة أو التوفر 24 ساعة
5. ابحث بالاسم
6. شاهد النتائج تتحدث فوراً

### **🎛️ استخدام أدوات التحكم:**
1. **وسط بغداد** - للعودة للعرض الافتراضي
2. **عرض جميع المتاجر** - لتوسيط الخريطة على جميع النتائج
3. **تجميع المتاجر** - لتفعيل/إلغاء تجميع العلامات المتقاربة

### **💬 عرض تفاصيل المتجر:**
1. انقر على علامة المتجر
2. شاهد النافذة المنبثقة بالمعلومات
3. انقر على "عرض التفاصيل" للانتقال لصفحة المتجر

## 🎉 **المزايا المحققة:**

### **✅ للإدارة:**
- **رؤية شاملة** لجميع المتاجر المتعاقدة
- **فلترة سريعة** للعثور على متاجر محددة
- **إحصائيات فورية** عن حالة المتاجر
- **وصول سريع** لتفاصيل أي متجر

### **✅ للمستخدمين:**
- **واجهة بديهية** وسهلة الاستخدام
- **تحديث فوري** للنتائج
- **معلومات شاملة** لكل متجر
- **تجربة تفاعلية** ممتعة

### **✅ للنظام:**
- **أداء ممتاز** مع تحميل سريع
- **مجاني بالكامل** - لا يحتاج مفاتيح API
- **مرونة في التطوير** والتحديث
- **تكامل مثالي** مع باقي النظام

## 🚀 **الخلاصة:**

تم إنشاء خريطة تفاعلية متطورة تشمل:

1. **🗺️ عرض شامل** لجميع المتاجر والمطاعم
2. **🔍 فلترة متقدمة** بمعايير متعددة
3. **🎨 تصميم عصري** وجذاب
4. **⚡ أداء سريع** ومستقر
5. **📱 تفاعل متطور** مع المستخدم
6. **📊 إحصائيات مفيدة** ومعلومات شاملة

**النظام الآن يوفر رؤية جغرافية شاملة ومتطورة لجميع المتاجر المتعاقدة!** 🗺️✨
