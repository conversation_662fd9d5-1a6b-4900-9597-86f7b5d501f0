# 🔐 نظام التوليد التلقائي لبيانات المتاجر

## 🌟 نظرة عامة
تم إنشاء نظام متطور لتوليد بيانات الحساب تلقائياً عند إضافة متجر جديد، يشمل اسم المستخدم وكلمة المرور ومعرف المتجر الفريد للتعامل مع Firebase.

## ✨ الميزات المضافة

### 🔧 **1. الحقول الجديدة في نموذج المتجر:**

```python
class Stores(User):
    # معرف فريد للمتجر للتعامل مع Firebase
    id_store = models.CharField(max_length=50, unique=True, verbose_name="معرف المتجر")
    
    # حقول إضافية لتتبع بيانات الحساب المُولدة
    generated_username = models.Char<PERSON>ield(max_length=150, blank=True, null=True)
    generated_password = models.CharField(max_length=128, blank=True, null=True)
    account_created_date = models.DateTimeField(auto_now_add=True)
```

### 🎯 **2. نظام التوليد التلقائي:**

#### **🆔 معرف المتجر (id_store):**
```python
# تنسيق: STORE_[timestamp]_[random_4_digits]
# مثال: STORE_1720534200_7834
timestamp = str(int(timezone.now().timestamp()))
random_suffix = ''.join(random.choices(string.digits, k=4))
id_store = f"STORE_{timestamp}_{random_suffix}"
```

#### **👤 اسم المستخدم:**
```python
# يتم توليده من اسم المتجر مع ضمان الفرادة
base_username = store_name.replace(' ', '_').lower()
username = f"store_{base_username}"

# في حالة التكرار يتم إضافة رقم
# مثال: store_مطعم_البركة، store_مطعم_البركة_1
```

#### **🔒 كلمة المرور:**
```python
# كلمة مرور قوية مُولدة تلقائياً
# 12 حرف + رقم في البداية والنهاية = 14 حرف
password_chars = string.ascii_letters + string.digits
password = random.choice(string.digits) + 
           ''.join(random.choices(password_chars, k=12)) + 
           random.choice(string.digits)
# مثال: 3kL9mN2pQ8rT5
```

#### **📧 البريد الإلكتروني:**
```python
# يتم إنشاؤه تلقائياً من اسم المستخدم
email = f"{username}@store.local"
# مثال: store_مطعم_البركة@store.local
```

### 🎨 **3. واجهة عرض البيانات المُولدة:**

#### **📋 قسم خاص في صفحة التفاصيل:**
```html
┌─────────────────────────────────────────────────────────────┐
│ 🔐 بيانات الحساب المُولدة تلقائياً                          │
├─────────────────────────────────────────────────────────────┤
│ 🆔 معرف المتجر: STORE_1720534200_7834        [📋 نسخ]     │
│ 👤 اسم المستخدم: store_مطعم_البركة           [📋 نسخ]     │
│ 🔒 كلمة المرور: ••••••••••••                [👁️] [📋 نسخ] │
│ 📧 البريد: store_مطعم_البركة@store.local      [📋 نسخ]     │
│                                                             │
│ 📅 تاريخ الإنشاء: 2025/07/09 15:23          [🔄 إعادة توليد] │
└─────────────────────────────────────────────────────────────┘
```

#### **🎛️ الوظائف التفاعلية:**
- **📋 نسخ سريع** لجميع البيانات
- **👁️ إظهار/إخفاء** كلمة المرور
- **🔄 إعادة توليد** كلمة مرور جديدة
- **📱 إشعارات تفاعلية** للعمليات

### 🔧 **4. الوظائف المتقدمة:**

#### **حفظ تلقائي عند الإنشاء:**
```python
def save(self, *args, **kwargs):
    # توليد البيانات عند الإنشاء فقط
    if not self.pk:  # متجر جديد
        self.generate_store_credentials()
    super().save(*args, **kwargs)
```

#### **إعادة توليد كلمة المرور:**
```python
def regenerate_password(self):
    # توليد كلمة مرور جديدة
    new_password = generate_strong_password()
    self.generated_password = new_password
    self.set_password(new_password)
    self.save()
    return new_password
```

#### **الحصول على البيانات:**
```python
def get_store_credentials(self):
    return {
        'id_store': self.id_store,
        'username': self.generated_username or self.username,
        'password': self.generated_password,
        'email': self.email,
        'created_date': self.account_created_date
    }
```

### 🎯 **5. تحديثات النموذج (Form):**

#### **إزالة الحقول المُولدة تلقائياً:**
```python
# تم إزالة هذه الحقول من النموذج:
# - username (سيتم توليده تلقائياً)
# - email (سيتم إنشاؤه تلقائياً)
# - password (سيتم توليده تلقائياً)

# الحقول المتبقية:
fields = ['first_name', 'last_name', 'area', 'store_type', 'store_name', 
         'lat', 'long', 'store_person_name', 'store_person_phone_number', 
         'store_picture', 'enable', 'notification', 'description', 'note', 
         'is_static_in_main_screen', 'is_found_24_for_food_stor', 'price_stor']
```

#### **رسالة توضيحية في النموذج:**
```html
<div class="alert alert-success">
    <i class="fas fa-magic me-2"></i>
    <strong>توليد تلقائي:</strong> سيتم إنشاء اسم المستخدم وكلمة المرور 
    ومعرف المتجر (id_store) تلقائياً عند حفظ المتجر.
</div>
```

### 🚀 **6. تحديثات العرض (Views):**

#### **عرض البيانات بعد الإنشاء:**
```python
@login_required
def stores_create(request):
    if request.method == 'POST':
        form = StoresForm(request.POST, request.FILES)
        if form.is_valid():
            store = form.save()  # سيتم التوليد التلقائي
            
            # الحصول على البيانات المُولدة
            credentials = store.get_store_credentials()
            
            # رسالة نجاح مع البيانات
            success_message = f'''
            تم إنشاء المتجر "{store.store_name}" بنجاح!<br>
            <strong>بيانات الحساب المُولدة:</strong><br>
            • معرف المتجر: {credentials['id_store']}<br>
            • اسم المستخدم: {credentials['username']}<br>
            • كلمة المرور: {credentials['password']}<br>
            • البريد الإلكتروني: {credentials['email']}
            '''
            messages.success(request, success_message)
            
            return redirect('stores_detail', pk=store.pk)
```

#### **إعادة توليد كلمة المرور (AJAX):**
```python
@login_required
def stores_regenerate_password(request, pk):
    if request.method == 'POST':
        store = get_object_or_404(Stores, pk=pk)
        new_password = store.regenerate_password()
        return JsonResponse({
            'success': True,
            'new_password': new_password,
            'message': 'تم إعادة توليد كلمة المرور بنجاح'
        })
```

### 🎨 **7. JavaScript التفاعلي:**

#### **نسخ البيانات:**
```javascript
function copyToClipboard(text, label) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification(`تم نسخ ${label} بنجاح!`, 'success');
    });
}
```

#### **إظهار/إخفاء كلمة المرور:**
```javascript
function togglePassword(storeId, password) {
    const passwordElement = document.getElementById(`password-${storeId}`);
    const eyeElement = document.getElementById(`eye-${storeId}`);
    
    if (passwordElement.textContent.includes('••••')) {
        passwordElement.textContent = password;
        eyeElement.className = 'fas fa-eye-slash';
    } else {
        passwordElement.textContent = '••••••••••••';
        eyeElement.className = 'fas fa-eye';
    }
}
```

#### **إعادة توليد كلمة المرور:**
```javascript
function regeneratePassword(storeId) {
    if (confirm('هل أنت متأكد من إعادة توليد كلمة مرور جديدة؟')) {
        fetch(`/stores/${storeId}/regenerate-password/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث البيانات في الصفحة
                updatePasswordDisplay(storeId, data.new_password);
                showNotification('تم إعادة توليد كلمة مرور جديدة!', 'success');
            }
        });
    }
}
```

### 🎯 **8. سيناريوهات الاستخدام:**

#### **إضافة متجر جديد:**
1. اذهب إلى `/stores/create/`
2. املأ المعلومات الأساسية فقط:
   - اسم المتجر (مطلوب)
   - نوع المتجر (مطلوب)
   - المنطقة (مطلوبة)
   - اسم المسؤول (مطلوب)
   - رقم الهاتف (مطلوب)
   - الموقع على الخريطة
3. احفظ المتجر
4. سيتم توليد البيانات تلقائياً:
   - ✅ معرف المتجر الفريد
   - ✅ اسم المستخدم
   - ✅ كلمة مرور قوية
   - ✅ البريد الإلكتروني

#### **عرض بيانات المتجر:**
1. اذهب لصفحة تفاصيل المتجر
2. شاهد قسم "بيانات الحساب المُولدة"
3. انسخ البيانات المطلوبة:
   - 📋 معرف المتجر للـ Firebase
   - 📋 اسم المستخدم للدخول
   - 📋 كلمة المرور (مع إمكانية الإظهار)
   - 📋 البريد الإلكتروني

#### **إعادة توليد كلمة المرور:**
1. في صفحة تفاصيل المتجر
2. انقر على "إعادة توليد كلمة المرور"
3. أكد العملية
4. ستظهر كلمة المرور الجديدة
5. انسخها واحفظها

### 🎉 **9. الفوائد المحققة:**

#### **✅ للمطورين:**
- **تكامل سهل مع Firebase** باستخدام id_store
- **أمان عالي** مع كلمات مرور قوية
- **فرادة مضمونة** لجميع المعرفات
- **سهولة الإدارة** والصيانة

#### **✅ للمستخدمين:**
- **توليد تلقائي** بدون تدخل يدوي
- **واجهة واضحة** لعرض البيانات
- **نسخ سريع** للبيانات المطلوبة
- **إعادة توليد آمنة** عند الحاجة

#### **✅ للنظام:**
- **تنظيم أفضل** لبيانات المتاجر
- **تتبع دقيق** لتواريخ الإنشاء
- **مرونة في التعديل** والتحديث
- **تكامل مع Firebase** جاهز

### 🎯 **10. أمثلة البيانات المُولدة:**

#### **متجر مطعم البركة:**
```
معرف المتجر: STORE_1720534200_7834
اسم المستخدم: store_مطعم_البركة
كلمة المرور: 3kL9mN2pQ8rT5
البريد الإلكتروني: store_مطعم_البركة@store.local
```

#### **متجر سوبر ماركت النور:**
```
معرف المتجر: STORE_1720534315_9162
اسم المستخدم: store_سوبر_ماركت_النور
كلمة المرور: 7mP4nK8qR2sV9
البريد الإلكتروني: store_سوبر_ماركت_النور@store.local
```

### 🚀 **الخلاصة:**

تم إنشاء نظام توليد تلقائي متطور يشمل:

1. **🆔 معرف فريد للمتجر** للتعامل مع Firebase
2. **👤 اسم مستخدم فريد** مُولد من اسم المتجر
3. **🔒 كلمة مرور قوية** مُولدة تلقائياً
4. **📧 بريد إلكتروني** مُنشأ تلقائياً
5. **🎨 واجهة جميلة** لعرض وإدارة البيانات
6. **🔄 إعادة توليد آمنة** لكلمات المرور
7. **📋 نسخ سريع** لجميع البيانات
8. **📱 تفاعل متطور** مع إشعارات

**النظام الآن جاهز للتكامل مع Firebase ويوفر إدارة شاملة وآمنة لبيانات المتاجر!** 🔐✨
