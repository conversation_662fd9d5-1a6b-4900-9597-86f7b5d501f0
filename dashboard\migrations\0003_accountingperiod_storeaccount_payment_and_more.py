# Generated by Django 4.1.4 on 2025-07-09 02:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dashboard', '0002_alter_order_order_state'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountingPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم الفترة')),
                ('period_type', models.CharField(choices=[('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('custom', 'فترة مخصصة')], max_length=20, verbose_name='نوع الفترة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('closed', 'مغلقة'), ('finalized', 'نهائية')], default='open', max_length=20, verbose_name='الحالة')),
                ('total_orders', models.IntegerField(default=0, verbose_name='إجمالي الطلبات')),
                ('total_revenue', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='إجمالي الإيرادات')),
                ('total_commission', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='إجمالي العمولات')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('closed_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
            ],
            options={
                'verbose_name': 'فترة محاسبية',
                'verbose_name_plural': 'الفترات المحاسبية',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='StoreAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('orders_count', models.IntegerField(default=0, verbose_name='عدد الطلبات')),
                ('total_orders_value', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='قيمة الطلبات الإجمالية')),
                ('commission_amount', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='مبلغ العمولة')),
                ('paid_amount', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='المبلغ المدفوع')),
                ('remaining_amount', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='المبلغ المتبقي')),
                ('last_payment_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر دفعة')),
                ('is_settled', models.BooleanField(default=False, verbose_name='مسدد بالكامل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('period', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.accountingperiod', verbose_name='الفترة المحاسبية')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.stores', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'حساب متجر',
                'verbose_name_plural': 'حسابات المتاجر',
                'unique_together': {('period', 'store')},
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=0, max_digits=15, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('online', 'دفع إلكتروني')], max_length=20, verbose_name='طريقة الدفع')),
                ('payment_date', models.DateTimeField(verbose_name='تاريخ الدفع')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('store_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='dashboard.storeaccount', verbose_name='حساب المتجر')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='CommissionSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_type', models.CharField(choices=[('fixed', 'مبلغ ثابت لكل طلب'), ('percentage', 'نسبة مئوية من قيمة الطلب'), ('tiered', 'نظام متدرج حسب عدد الطلبات')], default='fixed', max_length=20, verbose_name='نوع العمولة')),
                ('fixed_amount', models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='المبلغ الثابت (د.ع)')),
                ('percentage_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='النسبة المئوية (%)')),
                ('minimum_commission', models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأدنى للعمولة (د.ع)')),
                ('maximum_commission', models.DecimalField(decimal_places=0, default=0, max_digits=10, verbose_name='الحد الأقصى للعمولة (د.ع)')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعل')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='commission_settings', to='dashboard.stores', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'إعدادات العمولة',
                'verbose_name_plural': 'إعدادات العمولات',
            },
        ),
    ]
