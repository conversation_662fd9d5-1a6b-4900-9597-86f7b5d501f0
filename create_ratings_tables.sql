-- إنشاء جدول التقييمات
CREATE TABLE IF NOT EXISTS dashboard_rating (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rating_type VARCHAR(20) NOT NULL,
    rating_value INTEGER NOT NULL,
    comment TEXT,
    rated_delivery_person VARCHAR(255),
    is_public BOOLEAN NOT NULL DEFAULT 1,
    is_verified BOOLEAN NOT NULL DEFAULT 0,
    admin_response TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    order_id INTEGER,
    rated_customer_id INTEGER,
    rated_store_id INTEGER,
    reviewer_customer_id INTEGER,
    reviewer_store_id INTEGER,
    FOREIGN KEY (order_id) REFERENCES dashboard_order (id),
    FOREIGN KEY (rated_customer_id) REFERENCES dashboard_customer (id),
    FOREIG<PERSON> KEY (rated_store_id) REFERENCES dashboard_stores (id),
    FOREIG<PERSON> KEY (reviewer_customer_id) REFERENCES dashboard_customer (id),
    <PERSON>OREIG<PERSON> KEY (reviewer_store_id) REFERENCES dashboard_stores (id)
);

-- <PERSON>ن<PERSON>اء جدول إحصائيات التقييمات
CREATE TABLE IF NOT EXISTS dashboard_ratingstatistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_person_name VARCHAR(255) UNIQUE,
    total_ratings INTEGER NOT NULL DEFAULT 0,
    average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00,
    five_stars INTEGER NOT NULL DEFAULT 0,
    four_stars INTEGER NOT NULL DEFAULT 0,
    three_stars INTEGER NOT NULL DEFAULT 0,
    two_stars INTEGER NOT NULL DEFAULT 0,
    one_star INTEGER NOT NULL DEFAULT 0,
    last_updated DATETIME NOT NULL,
    customer_id INTEGER UNIQUE,
    store_id INTEGER UNIQUE,
    FOREIGN KEY (customer_id) REFERENCES dashboard_customer (id),
    FOREIGN KEY (store_id) REFERENCES dashboard_stores (id)
);

-- إدراج سجل في جدول الهجرات
INSERT OR IGNORE INTO django_migrations (app, name, applied) 
VALUES ('dashboard', '0008_rating_ratingstatistics', datetime('now'));
