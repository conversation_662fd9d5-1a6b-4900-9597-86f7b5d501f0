{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}متابعة الطلبات اليومية{% endblock %}

{% block extra_css %}
<style>
.order-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.order-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.order-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.35rem 0.35rem 0 0;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-new { background: #e3f2fd; color: #1976d2; }
.status-pending { background: #fff3e0; color: #f57c00; }
.status-accepted { background: #e8f5e8; color: #388e3c; }
.status-preparing { background: #fff8e1; color: #f9a825; }
.status-ready { background: #e1f5fe; color: #0288d1; }
.status-out_for_delivery { background: #f3e5f5; color: #7b1fa2; }
.status-delivered { background: #e8f5e8; color: #2e7d32; }
.status-cancelled { background: #ffebee; color: #d32f2f; }
.status-rejected { background: #fce4ec; color: #c2185b; }

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.realtime-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    z-index: 1000;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.order-items {
    max-height: 150px;
    overflow-y: auto;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fc;
}

.item-row:last-child {
    border-bottom: none;
}

.refresh-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات عرض المنتجات */
.order-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.order-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.order-header {
    background: #f8f9fc;
    padding: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.item-row {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    transition: all 0.3s ease;
}

.item-row:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.item-name {
    color: #2c3e50;
    font-size: 0.95rem;
}

.quantity-price {
    font-size: 0.9rem;
}

.item-total {
    font-size: 0.85rem;
    margin-top: 2px;
}

.order-items {
    max-height: 300px;
    overflow-y: auto;
}

.item-info {
    flex: 1;
}

.item-details {
    min-width: 120px;
}

/* أزرار فلترة الحالات */
.status-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.status-filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid #e3e6f0;
    border-radius: 0.5rem;
    background: white;
    color: #5a5c69;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
}

.status-filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: #5a5c69;
}

.status-filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 0.25rem 1rem rgba(102, 126, 234, 0.3);
}

.status-filter-btn.active:hover {
    color: white;
}

.filter-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: bold;
    min-width: 1.5rem;
    text-align: center;
}

.status-filter-btn.active .filter-count {
    background: rgba(255, 255, 255, 0.3);
}

/* ألوان الحالات للأزرار */
.status-filter-btn.status-new:not(.active) {
    border-color: #17a2b8;
    color: #17a2b8;
}

.status-filter-btn.status-new:not(.active):hover {
    background: #17a2b8;
    color: white;
}

.status-filter-btn.status-pending:not(.active) {
    border-color: #ffc107;
    color: #ffc107;
}

.status-filter-btn.status-pending:not(.active):hover {
    background: #ffc107;
    color: white;
}

.status-filter-btn.status-accepted:not(.active) {
    border-color: #28a745;
    color: #28a745;
}

.status-filter-btn.status-accepted:not(.active):hover {
    background: #28a745;
    color: white;
}

.status-filter-btn.status-preparing:not(.active) {
    border-color: #fd7e14;
    color: #fd7e14;
}

.status-filter-btn.status-preparing:not(.active):hover {
    background: #fd7e14;
    color: white;
}

.status-filter-btn.status-ready:not(.active) {
    border-color: #20c997;
    color: #20c997;
}

.status-filter-btn.status-ready:not(.active):hover {
    background: #20c997;
    color: white;
}

.status-filter-btn.status-out_for_delivery:not(.active) {
    border-color: #6f42c1;
    color: #6f42c1;
}

.status-filter-btn.status-out_for_delivery:not(.active):hover {
    background: #6f42c1;
    color: white;
}

.status-filter-btn.status-delivered:not(.active) {
    border-color: #198754;
    color: #198754;
}

.status-filter-btn.status-delivered:not(.active):hover {
    background: #198754;
    color: white;
}

.status-filter-btn.status-cancelled:not(.active) {
    border-color: #dc3545;
    color: #dc3545;
}

.status-filter-btn.status-cancelled:not(.active):hover {
    background: #dc3545;
    color: white;
}

.status-filter-btn.status-rejected:not(.active) {
    border-color: #e83e8c;
    color: #e83e8c;
}

.status-filter-btn.status-rejected:not(.active):hover {
    background: #e83e8c;
    color: white;
}

.active-filter-indicator {
    padding: 0.75rem;
    background: #f8f9fc;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* إخفاء/إظهار الطلبات المفلترة */
.order-card.filtered-out {
    display: none !important;
}

/* تجاوب الأزرار */
@media (max-width: 768px) {
    .status-filters {
        flex-direction: column;
    }

    .status-filter-btn {
        justify-content: space-between;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- تحذير خطأ Firebase -->
    {% if firebase_error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h4 class="alert-heading">
            <i class="fas fa-exclamation-triangle"></i>
            خطأ في الاتصال بـ Firebase
        </h4>
        <p>لا يمكن الاتصال بقاعدة بيانات Firebase. يرجى التحقق من:</p>
        <ul>
            <li>اتصال الإنترنت</li>
            <li>إعدادات Firebase</li>
            <li>صحة ملف firebase_config.json</li>
        </ul>
        <hr>
        <p class="mb-0">سيتم إعادة المحاولة تلقائياً كل 30 ثانية.</p>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- مؤشر الوقت الفعلي -->
    <div class="realtime-indicator" id="realtimeIndicator">
        {% if firebase_error %}
        <i class="fas fa-exclamation-triangle"></i>
        خطأ في الاتصال
        {% else %}
        <i class="fas fa-wifi"></i>
        متصل - تحديث مباشر
        {% endif %}
    </div>

    <!-- زر التحديث -->
    <button class="refresh-btn" onclick="refreshData()" id="refreshBtn">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line text-primary"></i>
            متابعة الطلبات اليومية
        </h1>
        <div>
            <span class="badge badge-info">{{ current_date }}</span>
            <a href="{% url 'orders_history' %}" class="btn btn-secondary btn-sm ml-2">
                <i class="fas fa-history"></i>
                تاريخ الطلبات
            </a>
            <a href="{% url 'orders_analytics' %}" class="btn btn-info btn-sm">
                <i class="fas fa-chart-bar"></i>
                التحليلات
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4" id="statsContainer">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="stats-number">{{ today_stats.total_orders|default:0 }}</div>
                <div class="stats-label">إجمالي الطلبات</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stats-number">{{ today_stats.total_revenue|default:0|floatformat:0 }}</div>
                <div class="stats-label">إجمالي الإيرادات (ريال)</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stats-number">{{ today_stats.average_order_value|default:0|floatformat:0 }}</div>
                <div class="stats-label">متوسط قيمة الطلب (ريال)</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
                <div class="stats-number">{{ today_stats.orders_by_status.delivered|default:0 }}</div>
                <div class="stats-label">طلبات مكتملة</div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الحالات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter"></i>
                        فلترة الطلبات حسب الحالة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="status-filters">
                        <!-- زر عرض الكل -->
                        <button class="status-filter-btn active" data-status="all" onclick="filterByStatus('all')">
                            <i class="fas fa-list"></i>
                            <span class="filter-label">جميع الطلبات</span>
                            <span class="filter-count">{{ today_stats.total_orders|default:0 }}</span>
                        </button>

                        <!-- أزرار الحالات -->
                        {% for status, count in today_stats.orders_by_status.items %}
                        <button class="status-filter-btn status-{{ status }}" data-status="{{ status }}" onclick="filterByStatus('{{ status }}')">
                            <i class="fas fa-{% if status == 'new' %}plus{% elif status == 'pending' %}clock{% elif status == 'accepted' %}check{% elif status == 'preparing' %}utensils{% elif status == 'ready' %}bell{% elif status == 'out_for_delivery' %}truck{% elif status == 'delivered' %}check-circle{% elif status == 'cancelled' %}times{% elif status == 'rejected' %}ban{% else %}circle{% endif %}"></i>
                            <span class="filter-label">
                                {% if status == 'new' %}جديد
                                {% elif status == 'pending' %}قيد الانتظار
                                {% elif status == 'accepted' %}مقبول
                                {% elif status == 'preparing' %}قيد التحضير
                                {% elif status == 'ready' %}جاهز
                                {% elif status == 'out_for_delivery' %}في الطريق
                                {% elif status == 'delivered' %}تم التوصيل
                                {% elif status == 'cancelled' %}ملغي
                                {% elif status == 'rejected' %}مرفوض
                                {% else %}{{ status }}
                                {% endif %}
                            </span>
                            <span class="filter-count">{{ count }}</span>
                        </button>
                        {% endfor %}
                    </div>

                    <!-- مؤشر الفلتر النشط -->
                    <div class="active-filter-indicator mt-3">
                        <span id="activeFilterText">عرض جميع الطلبات ({{ today_stats.total_orders|default:0 }} طلب)</span>
                        <button id="clearFilterBtn" class="btn btn-sm btn-outline-secondary ml-2" onclick="clearFilter()" style="display: none;">
                            <i class="fas fa-times"></i>
                            إلغاء الفلتر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الطلبات -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i>
                        طلبات اليوم ({{ today_orders|length }})
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div id="ordersContainer">
                        {% for order in today_orders %}
                        <div class="order-card" data-status="{{ order.status }}">
                            <div class="order-header">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-1">
                                            <i class="fas fa-store"></i>
                                            {{ order.store_name }}
                                        </h6>
                                        <small>طلب #{{ order.order_id }}</small>
                                    </div>
                                    <div class="col-md-6 text-md-right">
                                        <span class="status-badge status-{{ order.status }}">
                                            {{ order.status_arabic }}
                                        </span>
                                        <div class="mt-1">
                                            <small>{{ order.createdAt_formatted }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">معلومات العميل:</h6>
                                        <p class="mb-1">
                                            <i class="fas fa-user"></i>
                                            {{ order.customerName }}
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-phone"></i>
                                            {{ order.customerPhone }}
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-map-marker-alt"></i>
                                            {{ order.full_address_formatted }}
                                        </p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6 class="text-success">تفاصيل الطلب:</h6>
                                        <div class="order-items">
                                            {% for item in order.items %}
                                            <div class="item-row mb-2 p-2 border rounded">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="item-info">
                                                        <strong class="item-name">{{ item.name }}</strong>
                                                        {% if item.description %}
                                                        <br><small class="text-muted">{{ item.description }}</small>
                                                        {% endif %}
                                                    </div>
                                                    <div class="item-details text-right">
                                                        <div class="quantity-price">
                                                            <span class="badge badge-primary">{{ item.quantity }}</span>
                                                            ×
                                                            <span class="text-success">{{ item.price }} ريال</span>
                                                        </div>
                                                        <div class="item-total">
                                                            <strong class="text-primary">
                                                                {% widthratio item.price 1 item.quantity %} ريال
                                                            </strong>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% if item.category %}
                                                <small class="text-muted">
                                                    <i class="fas fa-tag"></i>
                                                    {{ item.category }}
                                                </small>
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                        <hr>
                                        <div class="order-summary">
                                            {% if order.subtotal %}
                                            <div class="d-flex justify-content-between">
                                                <span>المبلغ الفرعي:</span>
                                                <span>{{ order.subtotal }} ريال</span>
                                            </div>
                                            {% endif %}

                                            {% if order.tax and order.tax > 0 %}
                                            <div class="d-flex justify-content-between">
                                                <span>الضريبة:</span>
                                                <span>{{ order.tax }} ريال</span>
                                            </div>
                                            {% endif %}

                                            {% if order.deliveryFee and order.deliveryFee > 0 %}
                                            <div class="d-flex justify-content-between">
                                                <span>رسوم التوصيل:</span>
                                                <span>{{ order.deliveryFee }} ريال</span>
                                            </div>
                                            {% endif %}

                                            {% if order.discount and order.discount > 0 %}
                                            <div class="d-flex justify-content-between text-success">
                                                <span>الخصم:</span>
                                                <span>-{{ order.discount }} ريال</span>
                                            </div>
                                            {% endif %}

                                            <div class="d-flex justify-content-between border-top pt-2 mt-2">
                                                <strong>الإجمالي:</strong>
                                                <strong class="text-success">{{ order.storeSubtotal }} ريال</strong>
                                            </div>

                                            <div class="d-flex justify-content-between mt-2">
                                                <span>طريقة الدفع:</span>
                                                <span class="badge badge-info">{{ order.payment_method_arabic }}</span>
                                            </div>

                                            {% if order.estimatedDeliveryMinutes %}
                                            <div class="d-flex justify-content-between mt-2">
                                                <span>وقت التوصيل المتوقع:</span>
                                                <span class="text-warning">{{ order.estimatedDeliveryMinutes }} دقيقة</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3 text-center">
                                    <a href="{% url 'order_details' order.store_name order.order_id %}" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-600">لا توجد طلبات اليوم</h5>
                            <p class="text-gray-500">لم يتم استلام أي طلبات حتى الآن</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let refreshInterval;
let isRefreshing = false;

// تحديث البيانات
function refreshData() {
    if (isRefreshing) return;
    
    isRefreshing = true;
    const refreshBtn = document.getElementById('refreshBtn');
    const icon = refreshBtn.querySelector('i');
    
    // تغيير أيقونة التحديث
    icon.classList.add('loading');
    
    fetch('{% url "orders_realtime_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateOrdersDisplay(data.orders);
                updateStatsDisplay(data.stats);
                updateRealtimeIndicator(true);
            } else {
                console.error('خطأ في جلب البيانات:', data.message);
                updateRealtimeIndicator(false);
            }
        })
        .catch(error => {
            console.error('خطأ في الاتصال:', error);
            updateRealtimeIndicator(false);
        })
        .finally(() => {
            isRefreshing = false;
            icon.classList.remove('loading');
        });
}

// تحديث عرض الطلبات
function updateOrdersDisplay(orders) {
    // هنا يمكن تحديث قائمة الطلبات ديناميكياً
    // للبساطة، سنقوم بإعادة تحميل الصفحة إذا كان هناك طلبات جديدة
    const currentOrdersCount = document.querySelectorAll('.order-card').length;
    if (orders.length !== currentOrdersCount) {
        location.reload();
    }
}

// تحديث عرض الإحصائيات
function updateStatsDisplay(stats) {
    // تحديث الأرقام في البطاقات
    const statsNumbers = document.querySelectorAll('.stats-number');
    if (statsNumbers.length >= 4) {
        statsNumbers[0].textContent = stats.total_orders || 0;
        statsNumbers[1].textContent = Math.round(stats.total_revenue || 0);
        statsNumbers[2].textContent = Math.round(stats.average_order_value || 0);
        statsNumbers[3].textContent = stats.orders_by_status?.delivered || 0;
    }
}

// تحديث مؤشر الوقت الفعلي
function updateRealtimeIndicator(isConnected) {
    const indicator = document.getElementById('realtimeIndicator');
    if (isConnected) {
        indicator.innerHTML = '<i class="fas fa-wifi"></i> متصل - تحديث مباشر';
        indicator.style.background = '#28a745';
        indicator.style.color = 'white';
    } else {
        indicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ في الاتصال';
        indicator.style.background = '#dc3545';
        indicator.style.color = 'white';
    }
}

// متغيرات الفلترة
let currentFilter = 'all';
let allOrders = [];

// فلترة الطلبات حسب الحالة
function filterByStatus(status) {
    currentFilter = status;

    // تحديث الأزرار
    document.querySelectorAll('.status-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    const activeBtn = document.querySelector(`[data-status="${status}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // فلترة الطلبات
    const orderCards = document.querySelectorAll('.order-card');
    let visibleCount = 0;

    orderCards.forEach(card => {
        const orderStatus = card.getAttribute('data-status');

        if (status === 'all' || orderStatus === status) {
            card.classList.remove('filtered-out');
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.classList.add('filtered-out');
            card.style.display = 'none';
        }
    });

    // تحديث مؤشر الفلتر
    updateFilterIndicator(status, visibleCount);

    // إظهار/إخفاء زر إلغاء الفلتر
    const clearBtn = document.getElementById('clearFilterBtn');
    if (status === 'all') {
        clearBtn.style.display = 'none';
    } else {
        clearBtn.style.display = 'inline-block';
    }
}

// تحديث مؤشر الفلتر النشط
function updateFilterIndicator(status, count) {
    const indicator = document.getElementById('activeFilterText');

    if (status === 'all') {
        indicator.textContent = `عرض جميع الطلبات (${count} طلب)`;
    } else {
        const statusNames = {
            'new': 'جديد',
            'pending': 'قيد الانتظار',
            'accepted': 'مقبول',
            'preparing': 'قيد التحضير',
            'ready': 'جاهز',
            'out_for_delivery': 'في الطريق',
            'delivered': 'تم التوصيل',
            'cancelled': 'ملغي',
            'rejected': 'مرفوض'
        };

        const statusName = statusNames[status] || status;
        indicator.textContent = `عرض الطلبات: ${statusName} (${count} طلب)`;
    }
}

// إلغاء الفلتر
function clearFilter() {
    filterByStatus('all');
}

// إضافة data-status للطلبات عند تحميل الصفحة (إذا لم تكن موجودة)
function addStatusDataToOrders() {
    document.querySelectorAll('.order-card').forEach(card => {
        // التحقق من وجود data-status مسبقاً
        if (!card.getAttribute('data-status')) {
            const statusBadge = card.querySelector('.status-badge, [class*="status-"]');
            if (statusBadge) {
                const statusClasses = statusBadge.className.split(' ');
                const statusClass = statusClasses.find(cls => cls.startsWith('status-'));
                if (statusClass) {
                    const status = statusClass.replace('status-', '');
                    card.setAttribute('data-status', status);
                }
            }
        }
    });
}

// بدء التحديث التلقائي
document.addEventListener('DOMContentLoaded', function() {
    // إضافة data-status للطلبات
    addStatusDataToOrders();

    // تحديث كل 30 ثانية
    refreshInterval = setInterval(refreshData, 30000);

    // تحديث فوري عند تحميل الصفحة
    setTimeout(refreshData, 2000);
});

// إيقاف التحديث عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
{% endblock %}
