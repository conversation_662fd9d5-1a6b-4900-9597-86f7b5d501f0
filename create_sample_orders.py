#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append('c:/Users/<USER>/Desktop/control_zad')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'delivery_control.settings')
django.setup()

from dashboard.models import Order, Customer, Products, Stores, Categ, StoreType, Areas, Companies
from decimal import Decimal
import random

def create_sample_data():
    print("إنشاء بيانات تجريبية...")
    
    # إنشاء منطقة إذا لم تكن موجودة
    area, created = Areas.objects.get_or_create(
        area_name="منطقة تجريبية"
    )
    
    # إنشاء نوع متجر إذا لم يكن موجود
    store_type, created = StoreType.objects.get_or_create(
        store_type="متجر إلكتروني"
    )
    
    # إنشاء شركة إذا لم تكن موجودة
    company, created = Companies.objects.get_or_create(
        name="شركة تجريبية"
    )
    
    # إنشاء متجر إذا لم يكن موجود
    store, created = Stores.objects.get_or_create(
        store_name="متجر تجريبي",
        defaults={
            'username': 'test_store',
            'email': '<EMAIL>',
            'description': 'متجر للاختبار',
            'store_person_name': 'مالك تجريبي',
            'store_person_phone_number': '1234567890',
            'lat': '33.3152',
            'long': '44.3661',
            'area': area,
            'store_type': store_type,
            'enable': True,
        }
    )
    
    # إنشاء فئة إذا لم تكن موجودة
    category, created = Categ.objects.get_or_create(
        categ_name="فئة تجريبية",
        defaults={'note': 'فئة للاختبار'}
    )
    
    # إنشاء منتج إذا لم يكن موجود
    product, created = Products.objects.get_or_create(
        product_name="منتج تجريبي",
        defaults={
            'product_description': 'منتج للاختبار',
            'price': Decimal('100'),
            'store': store,
            'categ': category,
            'company': company,
            'enable': True,
        }
    )
    
    # إنشاء عميل إذا لم يكن موجود
    customer, created = Customer.objects.get_or_create(
        name="عميل تجريبي",
        defaults={
            'phone_number': '1234567890',
            'place': 'عنوان تجريبي',
        }
    )
    
    # إنشاء طلبات تجريبية بحالات مختلفة
    order_statuses = [
        'pending', 'waiting_shipping', 'shipped', 'on_way', 
        'delivered', 'no_answer', 'postponed', 'wrong_address'
    ]
    
    # إنشاء طلبات لكل حالة
    for status in order_statuses:
        # إنشاء عدد عشوائي من الطلبات لكل حالة (بين 5 و 15)
        count = random.randint(5, 15)
        for i in range(count):
            Order.objects.create(
                custom=customer,
                products=product,
                price=Decimal('100'),
                count=random.randint(1, 5),
                all_price=Decimal('100') * random.randint(1, 5),
                order_hawalah_number=f"HAW{status.upper()}{i+1:03d}",
                order_state=status,
            )
    
    print("تم إنشاء البيانات التجريبية بنجاح!")
    
    # طباعة الإحصائيات
    print("\nإحصائيات الطلبات:")
    for status_code, status_name in Order.ORDER_STATUS_CHOICES:
        count = Order.objects.filter(order_state=status_code).count()
        print(f"{status_name}: {count} طلب")

if __name__ == "__main__":
    create_sample_data()
