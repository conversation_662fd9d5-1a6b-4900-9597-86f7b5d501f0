{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تعديل نطاق التسعير{% endblock %}

{% block extra_css %}
<style>
.form-section {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.section-title {
    color: #5a5c69;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3e6f0;
}

.distance-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    font-weight: bold;
    margin-top: 0.5rem;
    display: inline-block;
    min-width: 200px;
    text-align: center;
}

.price-preview {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1rem;
    margin-top: 0.5rem;
    display: inline-block;
    min-width: 200px;
    text-align: center;
}

.distance-converter {
    background: #e9ecef;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-top: 0.5rem;
}

.converter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-warning"></i>
            تعديل نطاق التسعير
        </h1>
        <a href="{% url 'delivery_pricing_list' %}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <form method="POST" id="pricingForm">
                {% csrf_token %}
                
                <!-- معلومات النطاق الحالي -->
                <div class="alert alert-info">
                    <h6 class="mb-2">النطاق الحالي:</h6>
                    <p class="mb-0">
                        <strong>{{ pricing.distance_range_km }}</strong> - 
                        <strong>{{ pricing.price_per_delivery }} ريال يمني</strong>
                    </p>
                </div>
                
                <!-- نطاق المسافة -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="fas fa-route text-primary"></i>
                        نطاق المسافة
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="distance_from">المسافة من (متر) <span class="text-danger">*</span></label>
                                <input type="number" name="distance_from" id="distance_from" class="form-control" 
                                       min="0" step="1" value="{{ pricing.distance_from }}" required>
                                <small class="form-text text-muted">بداية النطاق بالمتر</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="distance_to">المسافة إلى (متر) <span class="text-danger">*</span></label>
                                <input type="number" name="distance_to" id="distance_to" class="form-control" 
                                       min="1" step="1" value="{{ pricing.distance_to }}" required>
                                <small class="form-text text-muted">نهاية النطاق بالمتر</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- محول المسافات -->
                    <div class="distance-converter">
                        <h6 class="mb-2">محول المسافات:</h6>
                        <div class="converter-item">
                            <span>بالمتر:</span>
                            <span id="meters-display">{{ pricing.distance_from }} - {{ pricing.distance_to }} متر</span>
                        </div>
                        <div class="converter-item">
                            <span>بالكيلومتر:</span>
                            <span id="km-display">{{ pricing.distance_range_km }}</span>
                        </div>
                    </div>
                    
                    <div id="distancePreview">
                        <div class="distance-preview">
                            <i class="fas fa-route"></i>
                            {{ pricing.distance_range_km }}
                        </div>
                    </div>
                </div>

                <!-- السعر -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="fas fa-money-bill-wave text-success"></i>
                        سعر التوصيل
                    </h5>
                    
                    <div class="form-group">
                        <label for="price_per_delivery">السعر (ريال يمني) <span class="text-danger">*</span></label>
                        <input type="number" name="price_per_delivery" id="price_per_delivery" class="form-control" 
                               min="0" step="0.01" value="{{ pricing.price_per_delivery }}" required>
                        <small class="form-text text-muted">سعر التوصيل لهذا النطاق بالريال اليمني</small>
                    </div>
                    
                    <div id="pricePreview">
                        <div class="price-preview">
                            <i class="fas fa-money-bill-wave"></i>
                            {{ pricing.price_per_delivery }} ريال يمني
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="fas fa-cog text-secondary"></i>
                        إعدادات إضافية
                    </h5>
                    
                    <div class="form-group">
                        <label for="description">وصف النطاق</label>
                        <input type="text" name="description" id="description" class="form-control"
                               value="{{ pricing.description }}"
                               placeholder="مثل: قريب، متوسط، بعيد، داخل المدينة، خارج المدينة">
                        <small class="form-text text-muted">وصف اختياري لتوضيح النطاق</small>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                               {% if pricing.is_active %}checked{% endif %}>
                        <label for="is_active" class="form-check-label">نطاق نشط</label>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="form-section">
                    <button type="submit" class="btn btn-warning btn-block btn-lg">
                        <i class="fas fa-save"></i>
                        حفظ التعديلات
                    </button>
                    <a href="{% url 'delivery_pricing_list' %}" class="btn btn-secondary btn-block mt-2">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث معاينة المسافة
function updateDistancePreview() {
    const from = parseInt(document.getElementById('distance_from').value) || 0;
    const to = parseInt(document.getElementById('distance_to').value) || 0;
    
    if (to > from) {
        // تحديث العرض بالمتر
        document.getElementById('meters-display').textContent = `${from} - ${to} متر`;
        
        // تحديث العرض بالكيلومتر
        const fromKm = (from / 1000).toFixed(1);
        const toKm = (to / 1000).toFixed(1);
        document.getElementById('km-display').textContent = `${fromKm} - ${toKm} كم`;
        
        // معاينة النطاق
        let rangeText;
        if (from === 0) {
            rangeText = `حتى ${toKm} كم`;
        } else if (to >= 999999) {
            rangeText = `أكثر من ${fromKm} كم`;
        } else {
            rangeText = `${fromKm} - ${toKm} كم`;
        }
        
        document.getElementById('distancePreview').innerHTML = `
            <div class="distance-preview">
                <i class="fas fa-route"></i>
                ${rangeText}
            </div>
        `;
    } else {
        document.getElementById('meters-display').textContent = '0 - 0 متر';
        document.getElementById('km-display').textContent = '0 - 0 كم';
        document.getElementById('distancePreview').innerHTML = '';
    }
}

// تحديث معاينة السعر
function updatePricePreview() {
    const price = document.getElementById('price_per_delivery').value;
    if (price && price > 0) {
        document.getElementById('pricePreview').innerHTML = `
            <div class="price-preview">
                <i class="fas fa-money-bill-wave"></i>
                ${price} ريال يمني
            </div>
        `;
    } else {
        document.getElementById('pricePreview').innerHTML = '';
    }
}

// أحداث النموذج
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المعاينة عند تغيير المسافة
    document.getElementById('distance_from').addEventListener('input', updateDistancePreview);
    document.getElementById('distance_to').addEventListener('input', updateDistancePreview);
    
    // تحديث المعاينة عند تغيير السعر
    document.getElementById('price_per_delivery').addEventListener('input', updatePricePreview);
    
    // التحقق من صحة النموذج
    document.getElementById('pricingForm').addEventListener('submit', function(e) {
        const from = parseInt(document.getElementById('distance_from').value) || 0;
        const to = parseInt(document.getElementById('distance_to').value) || 0;
        const price = parseFloat(document.getElementById('price_per_delivery').value) || 0;
        
        if (from >= to) {
            e.preventDefault();
            alert('المسافة "إلى" يجب أن تكون أكبر من المسافة "من"');
            return false;
        }
        
        if (price <= 0) {
            e.preventDefault();
            alert('يجب إدخال سعر صحيح أكبر من صفر');
            return false;
        }
    });
});
</script>
{% endblock %}
