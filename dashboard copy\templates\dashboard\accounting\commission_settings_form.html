{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item"><a href="{% url 'commission_settings_list' %}">إعدادات العمولات</a></li>
<li class="breadcrumb-item active">
    {% if settings %}تعديل{% else %}إنشاء جديد{% endif %}
</li>
{% endblock %}

{% block extra_css %}
<style>
.commission-form-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.commission-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.commission-type-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.commission-type-card:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.commission-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.commission-type-card.selected::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.commission-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.dynamic-fields {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-left: 4px solid var(--primary-color);
}

.calculator-preview {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.preview-example {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
    border-left: 3px solid var(--success-color);
}

.help-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="commission-form-card">
            <!-- رأس النموذج -->
            <div class="form-header">
                <div class="mb-3">
                    <i class="fas fa-cogs fa-3x"></i>
                </div>
                <h3 class="mb-0">{{ title }}</h3>
                <p class="mb-0 mt-2 opacity-75">
                    {% if settings %}
                        تعديل إعدادات العمولة لمتجر {{ settings.store.store_name }}
                    {% else %}
                        إنشاء إعدادات عمولة جديدة للمتجر
                    {% endif %}
                </p>
            </div>

            <!-- النموذج -->
            <div class="card-body p-4">
                <form method="post" id="commissionForm">
                    {% csrf_token %}
                    
                    <!-- اختيار المتجر -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="{{ form.store.id_for_label }}" class="form-label">
                                <i class="fas fa-store me-2"></i>
                                المتجر
                            </label>
                            {{ form.store }}
                            {% if form.store.errors %}
                                <div class="text-danger small">{{ form.store.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">
                                <i class="fas fa-toggle-on me-2"></i>
                                حالة التفعيل
                            </label>
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    تفعيل إعدادات العمولة
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار نوع العمولة -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-calculator me-2"></i>
                            نوع العمولة
                        </label>
                        <div class="commission-type-selector">
                            <div class="commission-type-card" data-type="fixed">
                                <div class="commission-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <h6>مبلغ ثابت</h6>
                                <p class="small text-muted mb-0">مبلغ ثابت لكل طلب</p>
                            </div>
                            <div class="commission-type-card" data-type="percentage">
                                <div class="commission-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <h6>نسبة مئوية</h6>
                                <p class="small text-muted mb-0">نسبة من قيمة الطلب</p>
                            </div>
                            <div class="commission-type-card" data-type="tiered">
                                <div class="commission-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h6>نظام متدرج</h6>
                                <p class="small text-muted mb-0">يقل مع زيادة الطلبات</p>
                            </div>
                        </div>
                        {{ form.commission_type }}
                        {% if form.commission_type.errors %}
                            <div class="text-danger small">{{ form.commission_type.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- الحقول الديناميكية -->
                    <div class="dynamic-fields" id="dynamicFields">
                        <!-- حقول المبلغ الثابت -->
                        <div id="fixedFields" class="commission-fields" style="display: none;">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-coins me-2"></i>
                                إعدادات المبلغ الثابت
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.fixed_amount.id_for_label }}" class="form-label">المبلغ الثابت (د.ع)</label>
                                    {{ form.fixed_amount }}
                                    {% if form.fixed_amount.errors %}
                                        <div class="text-danger small">{{ form.fixed_amount.errors }}</div>
                                    {% endif %}
                                    <div class="form-text">المبلغ المستحق لكل طلب</div>
                                </div>
                            </div>
                        </div>

                        <!-- حقول النسبة المئوية -->
                        <div id="percentageFields" class="commission-fields" style="display: none;">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-percentage me-2"></i>
                                إعدادات النسبة المئوية
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.percentage_rate.id_for_label }}" class="form-label">النسبة المئوية (%)</label>
                                    {{ form.percentage_rate }}
                                    {% if form.percentage_rate.errors %}
                                        <div class="text-danger small">{{ form.percentage_rate.errors }}</div>
                                    {% endif %}
                                    <div class="form-text">النسبة من قيمة الطلب (مثال: 3.5)</div>
                                </div>
                            </div>
                        </div>

                        <!-- حقول النظام المتدرج -->
                        <div id="tieredFields" class="commission-fields" style="display: none;">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-chart-line me-2"></i>
                                إعدادات النظام المتدرج
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.fixed_amount.id_for_label }}" class="form-label">المبلغ الأساسي (د.ع)</label>
                                    {{ form.fixed_amount }}
                                    <div class="form-text">المبلغ للطلبات الأولى (1-10 طلبات)</div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <strong>نظام التدرج:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>1-10 طلبات: المبلغ الكامل</li>
                                    <li>11-50 طلب: خصم 10% (90% من المبلغ)</li>
                                    <li>51-100 طلب: خصم 20% (80% من المبلغ)</li>
                                    <li>أكثر من 100: خصم 30% (70% من المبلغ)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- الحدود -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.minimum_commission.id_for_label }}" class="form-label">
                                <i class="fas fa-arrow-down me-2"></i>
                                الحد الأدنى للعمولة (د.ع)
                            </label>
                            {{ form.minimum_commission }}
                            {% if form.minimum_commission.errors %}
                                <div class="text-danger small">{{ form.minimum_commission.errors }}</div>
                            {% endif %}
                            <div class="form-text">أقل مبلغ عمولة (اختياري)</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.maximum_commission.id_for_label }}" class="form-label">
                                <i class="fas fa-arrow-up me-2"></i>
                                الحد الأقصى للعمولة (د.ع)
                            </label>
                            {{ form.maximum_commission }}
                            {% if form.maximum_commission.errors %}
                                <div class="text-danger small">{{ form.maximum_commission.errors }}</div>
                            {% endif %}
                            <div class="form-text">أعلى مبلغ عمولة (اختياري)</div>
                        </div>
                    </div>

                    <!-- معاينة الحساب -->
                    <div class="calculator-preview" id="calculatorPreview" style="display: none;">
                        <h6 class="mb-3">
                            <i class="fas fa-calculator me-2"></i>
                            معاينة حساب العمولة
                        </h6>
                        <div id="previewContent"></div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'commission_settings_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="previewCalculation()">
                                <i class="fas fa-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if settings %}تحديث الإعدادات{% else %}حفظ الإعدادات{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قسم المساعدة -->
        <div class="help-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>
                دليل أنواع العمولات
            </h6>
            <div class="row">
                <div class="col-md-4">
                    <h6 class="text-primary">المبلغ الثابت</h6>
                    <p class="small">مناسب للمتاجر ذات الطلبات المنتظمة. مثال: 5000 د.ع لكل طلب.</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-success">النسبة المئوية</h6>
                    <p class="small">مناسب للطلبات متغيرة القيمة. مثال: 3% من قيمة الطلب.</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-info">النظام المتدرج</h6>
                    <p class="small">يحفز على زيادة الطلبات. العمولة تقل مع زيادة عدد الطلبات.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const commissionTypeSelect = document.getElementById('{{ form.commission_type.id_for_label }}');
    const typeCards = document.querySelectorAll('.commission-type-card');
    const fieldsContainers = document.querySelectorAll('.commission-fields');
    
    // تحديد النوع المحدد حالياً
    function updateSelectedType() {
        const selectedType = commissionTypeSelect.value;
        
        // تحديث البطاقات
        typeCards.forEach(card => {
            card.classList.remove('selected');
            if (card.dataset.type === selectedType) {
                card.classList.add('selected');
            }
        });
        
        // إظهار/إخفاء الحقول
        fieldsContainers.forEach(container => {
            container.style.display = 'none';
        });
        
        if (selectedType) {
            const targetContainer = document.getElementById(selectedType + 'Fields');
            if (targetContainer) {
                targetContainer.style.display = 'block';
            }
        }
        
        updatePreview();
    }
    
    // ربط النقر على البطاقات
    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            commissionTypeSelect.value = this.dataset.type;
            updateSelectedType();
        });
    });
    
    // ربط تغيير القائمة المنسدلة
    commissionTypeSelect.addEventListener('change', updateSelectedType);
    
    // تحديث المعاينة
    function updatePreview() {
        const type = commissionTypeSelect.value;
        const fixedAmount = document.getElementById('{{ form.fixed_amount.id_for_label }}').value;
        const percentageRate = document.getElementById('{{ form.percentage_rate.id_for_label }}').value;
        const minCommission = document.getElementById('{{ form.minimum_commission.id_for_label }}').value;
        const maxCommission = document.getElementById('{{ form.maximum_commission.id_for_label }}').value;
        
        if (!type) return;
        
        let previewHTML = '<div class="row">';
        
        if (type === 'fixed') {
            previewHTML += `
                <div class="col-md-6">
                    <div class="preview-example">
                        <strong>مثال:</strong> 10 طلبات × ${fixedAmount || 0} ريال يمني = ${(fixedAmount || 0) * 10} ريال يمني
                    </div>
                </div>
            `;
        } else if (type === 'percentage') {
            previewHTML += `
                <div class="col-md-6">
                    <div class="preview-example">
                        <strong>مثال:</strong> طلب بقيمة 100,000 ريال يمني × ${percentageRate || 0}% = ${((percentageRate || 0) * 100000 / 100)} ريال يمني
                    </div>
                </div>
            `;
        } else if (type === 'tiered') {
            const baseAmount = fixedAmount || 0;
            previewHTML += `
                <div class="col-12">
                    <div class="preview-example">
                        <strong>أمثلة النظام المتدرج:</strong>
                        <ul class="mt-2 mb-0">
                            <li>5 طلبات: 5 × ${baseAmount} = ${5 * baseAmount} ريال يمني</li>
                            <li>25 طلب: 25 × ${baseAmount * 0.9} = ${25 * baseAmount * 0.9} د.ع</li>
                            <li>75 طلب: 75 × ${baseAmount * 0.8} = ${75 * baseAmount * 0.8} د.ع</li>
                            <li>150 طلب: 150 × ${baseAmount * 0.7} = ${150 * baseAmount * 0.7} د.ع</li>
                        </ul>
                    </div>
                </div>
            `;
        }
        
        previewHTML += '</div>';
        
        document.getElementById('previewContent').innerHTML = previewHTML;
        document.getElementById('calculatorPreview').style.display = 'block';
    }
    
    // ربط تحديث المعاينة بالحقول
    document.getElementById('{{ form.fixed_amount.id_for_label }}').addEventListener('input', updatePreview);
    document.getElementById('{{ form.percentage_rate.id_for_label }}').addEventListener('input', updatePreview);
    
    // معاينة الحساب
    window.previewCalculation = function() {
        updatePreview();
        document.getElementById('calculatorPreview').scrollIntoView({ behavior: 'smooth' });
    };
    
    // تهيئة أولية
    updateSelectedType();
});
</script>
{% endblock %}
