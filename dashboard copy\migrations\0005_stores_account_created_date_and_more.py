# Generated by Django 4.1.4 on 2025-07-09 12:22

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0004_specialoffer_offerusage'),
    ]

    operations = [
        migrations.AddField(
            model_name='stores',
            name='account_created_date',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='تاريخ إنشاء الحساب'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='stores',
            name='generated_password',
            field=models.CharField(blank=True, max_length=128, null=True, verbose_name='كلمة المرور المُولدة'),
        ),
        migrations.AddField(
            model_name='stores',
            name='generated_username',
            field=models.CharField(blank=True, max_length=150, null=True, verbose_name='اسم المستخدم المُولد'),
        ),
        migrations.AddField(
            model_name='stores',
            name='id_store',
            field=models.Char<PERSON>ield(default='STORE_DEFAULT', help_text='معرف فريد للتعامل مع Firebase', max_length=50, unique=True, verbose_name='معرف المتجر'),
            preserve_default=False,
        ),
    ]
