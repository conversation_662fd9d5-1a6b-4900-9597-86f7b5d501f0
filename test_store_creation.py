#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'delivery_control.settings')
django.setup()

from dashboard.models import Stores, Areas, StoreType
from dashboard.firebase_service import firebase_service

def test_store_creation():
    """اختبار إنشاء متجر وإرساله إلى Firebase"""
    print("🏪 اختبار إنشاء متجر جديد...")
    
    try:
        # الحصول على منطقة ونوع متجر موجودين
        area = Areas.objects.first()
        store_type = StoreType.objects.first()
        
        if not area:
            print("❌ لا توجد مناطق في قاعدة البيانات")
            return
            
        if not store_type:
            print("❌ لا توجد أنواع متاجر في قاعدة البيانات")
            return
        
        print(f"📍 المنطقة المختارة: {area.name}")
        print(f"🏷️ نوع المتجر المختار: {store_type.name}")
        
        # إنشاء متجر جديد
        store = Stores.objects.create(
            store_name="متجر تجريبي للاختبار",
            area=area,
            store_type=store_type,
            lat="15.3694",
            long="44.1910",
            store_person_name="أحمد محمد",
            store_person_phone_number="777123456",
            email="<EMAIL>",
            enable=True,
            notification=True,
            is_static_in_main_screen=False
        )
        
        print(f"✅ تم إنشاء المتجر محلياً: {store.store_name} (ID: {store.id})")
        
        # الحصول على بيانات الحساب المُولدة
        credentials = store.get_store_credentials()
        print(f"🔑 بيانات الحساب المُولدة:")
        print(f"   - معرف المتجر: {credentials['id_store']}")
        print(f"   - اسم المستخدم: {credentials['username']}")
        print(f"   - كلمة المرور: {credentials['password']}")
        
        # إرسال المتجر إلى Firebase
        print("📤 محاولة إرسال المتجر إلى Firebase...")
        
        store_data = {
            'id': store.id,
            'name': store.store_name,
            'description': '',
            'category': store.store_type.name,
            'phone': store.store_person_phone_number,
            'email': store.email,
            'address': '',
            'area_id': store.area.id,
            'area_name': store.area.name,
            'is_active': store.enable,
            'is_featured': store.is_static_in_main_screen,
            'rating': 0,
            'total_reviews': 0,
            'delivery_time': 30,
            'minimum_order': 0,
            'delivery_fee': 0,
            'latitude': float(store.lat) if store.lat else None,
            'longitude': float(store.long) if store.long else None,
            'opening_hours': {},
            'tags': [],
            'image_url': '',
            'cover_image_url': '',
            'created_by': 'admin',
            'firebase_username': credentials['username'],
            'firebase_password': credentials['password'],
            'id_store': credentials['id_store'],
            'license_number': '',
            'tax_number': '',
            'owner_name': store.store_person_name,
            'bank_account': '',
            'auto_accept_orders': False,
            'notification_enabled': store.notification,
            'commission_rate': 0
        }
        
        success = firebase_service.send_store_to_firestore(store_data)
        
        if success:
            print("✅ تم إرسال المتجر إلى Firebase بنجاح!")
        else:
            print("❌ فشل في إرسال المتجر إلى Firebase")
            
        # حذف المتجر التجريبي
        print("🗑️ حذف المتجر التجريبي...")
        store.delete()
        print("✅ تم حذف المتجر التجريبي")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    test_store_creation()
