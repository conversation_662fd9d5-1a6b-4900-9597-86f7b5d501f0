{% extends 'dashboard/base.html' %}

{% block title %}الإشعارات - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة الإشعارات</h1>
        <p class="text-muted">عرض وإدارة جميع الإشعارات في النظام</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'notifications_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة إشعار جديد
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-bell me-2"></i>
            قائمة الإشعارات ({{ notifications.count }})
        </h5>
    </div>
    <div class="card-body">
        {% if notifications %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>المتجر</th>
                            <th>نص الإشعار</th>
                            <th>من تاريخ</th>
                            <th>إلى تاريخ</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for notification in notifications %}
                        <tr>
                            <td><strong>#{{ notification.id }}</strong></td>
                            <td>{{ notification.store.store_name }}</td>
                            <td>{{ notification.notifications_text|truncatechars:50 }}</td>
                            <td>{{ notification.from_date|date:"Y/m/d" }}</td>
                            <td>{{ notification.to_date|date:"Y/m/d" }}</td>
                            <td>{{ notification.date|date:"Y/m/d" }}</td>
                            <td>
                                {% now "Y-m-d" as today %}
                                {% if notification.from_date|date:"Y-m-d" <= today and notification.to_date|date:"Y-m-d" >= today %}
                                    <span class="badge bg-success">نشط</span>
                                {% elif notification.from_date|date:"Y-m-d" > today %}
                                    <span class="badge bg-warning">قادم</span>
                                {% else %}
                                    <span class="badge bg-secondary">منتهي</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-bell fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد إشعارات</h5>
                <p class="text-muted">لم يتم إضافة أي إشعارات حتى الآن</p>
                <a href="{% url 'notifications_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول إشعار
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
