{% extends 'dashboard/base.html' %}

{% block title %}إعدادات العمولات - النظام المحاسبي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounting_dashboard' %}">النظام المحاسبي</a></li>
<li class="breadcrumb-item active">إعدادات العمولات</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-cogs me-2"></i>
            إعدادات العمولات
        </h1>
        <p class="text-muted">إدارة أنواع وقيم العمولات للمتاجر المختلفة</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'commission_settings_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة إعدادات جديدة
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ total_settings }}</h3>
                <p class="text-muted mb-0">إجمالي الإعدادات</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ active_settings }}</h3>
                <p class="text-muted mb-0">الإعدادات النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ total_settings|add:"-"|add:active_settings }}</h3>
                <p class="text-muted mb-0">الإعدادات المعطلة</p>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الإعدادات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة إعدادات العمولات
        </h5>
    </div>
    <div class="card-body">
        {% if settings %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المتجر</th>
                            <th>نوع العمولة</th>
                            <th>القيمة</th>
                            <th>الحد الأدنى</th>
                            <th>الحد الأقصى</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for setting in settings %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-store text-primary"></i>
                                    </div>
                                    <div>
                                        <strong>{{ setting.store.store_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ setting.store.store_type.type_name }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{% if setting.commission_type == 'fixed' %}primary{% elif setting.commission_type == 'percentage' %}success{% else %}info{% endif %}">
                                    {{ setting.get_commission_type_display }}
                                </span>
                            </td>
                            <td>
                                {% if setting.commission_type == 'fixed' %}
                                    <strong>{{ setting.fixed_amount|floatformat:0 }} ريال يمني</strong>
                                {% elif setting.commission_type == 'percentage' %}
                                    <strong>{{ setting.percentage_rate }}%</strong>
                                {% else %}
                                    <strong>متدرج</strong>
                                {% endif %}
                            </td>
                            <td>
                                {% if setting.minimum_commission > 0 %}
                                    {{ setting.minimum_commission|floatformat:0 }} ريال يمني
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if setting.maximum_commission > 0 %}
                                    {{ setting.maximum_commission|floatformat:0 }} ريال يمني
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if setting.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">معطل</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'commission_settings_edit' setting.pk %}" 
                                       class="btn btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#previewModal{{ setting.pk }}" 
                                            title="معاينة">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <!-- Modal معاينة الإعدادات -->
                        <div class="modal fade" id="previewModal{{ setting.pk }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">معاينة إعدادات عمولة {{ setting.store.store_name }}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-6 mb-3">
                                                <strong>نوع العمولة:</strong>
                                                <br>{{ setting.get_commission_type_display }}
                                            </div>
                                            <div class="col-6 mb-3">
                                                <strong>الحالة:</strong>
                                                <br>
                                                {% if setting.is_active %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">معطل</span>
                                                {% endif %}
                                            </div>
                                            {% if setting.commission_type == 'fixed' %}
                                            <div class="col-12 mb-3">
                                                <strong>المبلغ الثابت:</strong>
                                                <br>{{ setting.fixed_amount|floatformat:0 }} د.ع لكل طلب
                                            </div>
                                            {% elif setting.commission_type == 'percentage' %}
                                            <div class="col-12 mb-3">
                                                <strong>النسبة المئوية:</strong>
                                                <br>{{ setting.percentage_rate }}% من قيمة الطلب
                                            </div>
                                            {% else %}
                                            <div class="col-12 mb-3">
                                                <strong>النظام المتدرج:</strong>
                                                <ul class="list-unstyled mt-2">
                                                    <li>• 1-10 طلبات: {{ setting.fixed_amount|floatformat:0 }} د.ع</li>
                                                    <li>• 11-50 طلب: خصم 10%</li>
                                                    <li>• 51-100 طلب: خصم 20%</li>
                                                    <li>• أكثر من 100: خصم 30%</li>
                                                </ul>
                                            </div>
                                            {% endif %}
                                            {% if setting.minimum_commission > 0 %}
                                            <div class="col-6 mb-3">
                                                <strong>الحد الأدنى:</strong>
                                                <br>{{ setting.minimum_commission|floatformat:0 }} د.ع
                                            </div>
                                            {% endif %}
                                            {% if setting.maximum_commission > 0 %}
                                            <div class="col-6 mb-3">
                                                <strong>الحد الأقصى:</strong>
                                                <br>{{ setting.maximum_commission|floatformat:0 }} د.ع
                                            </div>
                                            {% endif %}
                                            <div class="col-12">
                                                <strong>تاريخ الإنشاء:</strong>
                                                <br>{{ setting.created_date|date:"Y/m/d H:i" }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                        <a href="{% url 'commission_settings_edit' setting.pk %}" class="btn btn-primary">تعديل</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-cogs fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد إعدادات عمولة</h5>
                <p class="text-muted mb-4">ابدأ بإنشاء إعدادات العمولة للمتاجر</p>
                <a href="{% url 'commission_settings_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء إعدادات جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    أنواع العمولات
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-primary me-2">ثابت</span>
                        مبلغ ثابت لكل طلب
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-success me-2">نسبة</span>
                        نسبة مئوية من قيمة الطلب
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-info me-2">متدرج</span>
                        يقل المبلغ مع زيادة عدد الطلبات
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم النظام المتدرج لتشجيع المتاجر على زيادة الطلبات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد حد أدنى وأقصى للعمولة لضمان العدالة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        راجع الإعدادات دورياً وعدلها حسب الأداء
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
