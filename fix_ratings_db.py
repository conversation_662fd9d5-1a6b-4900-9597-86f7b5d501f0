import sqlite3
import os
from datetime import datetime

def fix_ratings_database():
    """إصلاح قاعدة البيانات وإنشاء جداول التقييمات"""
    
    db_path = 'db.sqlite3'
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء إنشاء جداول التقييمات...")
        
        # إنشاء جدول التقييمات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dashboard_rating (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rating_type VARCHAR(20) NOT NULL,
                rating_value INTEGER NOT NULL,
                comment TEXT,
                rated_delivery_person VARCHAR(255),
                is_public BOOLEAN NOT NULL DEFAULT 1,
                is_verified BOOLEAN NOT NULL DEFAULT 0,
                admin_response TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                order_id INTEGER,
                rated_customer_id INTEGER,
                rated_store_id INTEGER,
                reviewer_customer_id INTEGER,
                reviewer_store_id INTEGER
            )
        ''')
        
        print("✅ تم إنشاء جدول dashboard_rating")
        
        # إنشاء جدول إحصائيات التقييمات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dashboard_ratingstatistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                delivery_person_name VARCHAR(255) UNIQUE,
                total_ratings INTEGER NOT NULL DEFAULT 0,
                average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00,
                five_stars INTEGER NOT NULL DEFAULT 0,
                four_stars INTEGER NOT NULL DEFAULT 0,
                three_stars INTEGER NOT NULL DEFAULT 0,
                two_stars INTEGER NOT NULL DEFAULT 0,
                one_star INTEGER NOT NULL DEFAULT 0,
                last_updated DATETIME NOT NULL,
                customer_id INTEGER UNIQUE,
                store_id INTEGER UNIQUE
            )
        ''')
        
        print("✅ تم إنشاء جدول dashboard_ratingstatistics")
        
        # إضافة سجل الهجرة
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        cursor.execute('''
            INSERT OR IGNORE INTO django_migrations (app, name, applied) 
            VALUES ('dashboard', '0008_rating_ratingstatistics', ?)
        ''', (current_time,))
        
        print("✅ تم تحديث جدول django_migrations")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dashboard_rating%'")
        tables = cursor.fetchall()
        
        print(f"📋 الجداول المُنشأة: {[table[0] for table in tables]}")
        print("🎉 تم إنشاء جميع الجداول بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = fix_ratings_database()
    if success:
        print("\n🎯 يمكنك الآن الوصول إلى صفحة التقييمات!")
    else:
        print("\n❌ فشل في إنشاء الجداول")
