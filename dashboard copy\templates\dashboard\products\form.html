{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <p class="text-muted">إدارة بيانات المنتجات</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'products_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <!-- معلومات أساسية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.product_name.id_for_label }}" class="form-label">
                                    اسم المنتج <span class="text-danger">*</span>
                                </label>
                                {{ form.product_name }}
                                {% if form.product_name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.product_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.store.id_for_label }}" class="form-label">المتجر</label>
                                {{ form.store }}
                                {% if form.store.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.store.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.categ.id_for_label }}" class="form-label">الفئة</label>
                                {{ form.categ }}
                                {% if form.categ.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.categ.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.family.id_for_label }}" class="form-label">العائلة</label>
                                {{ form.family }}
                                {% if form.family.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.family.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.company.id_for_label }}" class="form-label">الشركة</label>
                                {{ form.company }}
                                {% if form.company.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.company.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- التفاصيل والسعر -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">التفاصيل والسعر</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.price.id_for_label }}" class="form-label">
                                    السعر (دينار عراقي) <span class="text-danger">*</span>
                                </label>
                                {{ form.price }}
                                {% if form.price.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.price.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.product_description.id_for_label }}" class="form-label">وصف المنتج</label>
                                {{ form.product_description }}
                                {% if form.product_description.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.product_description.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.seo.id_for_label }}" class="form-label">كلمات مفتاحية (SEO)</label>
                                {{ form.seo }}
                                {% if form.seo.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.seo.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.note.id_for_label }}" class="form-label">ملاحظات</label>
                                {{ form.note }}
                                {% if form.note.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.note.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- الصور -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">الصور</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product_picture.id_for_label }}" class="form-label">
                                    صورة المنتج الرئيسية <span class="text-danger">*</span>
                                </label>
                                {{ form.product_picture }}
                                {% if form.product_picture.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.product_picture.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product_picture_backend.id_for_label }}" class="form-label">
                                    صورة المنتج الخلفية <span class="text-danger">*</span>
                                </label>
                                {{ form.product_picture_backend }}
                                {% if form.product_picture_backend.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.product_picture_backend.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإعدادات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">الإعدادات</h6>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                {{ form.enable }}
                                <label class="form-check-label" for="{{ form.enable.id_for_label }}">
                                    مفعل
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                {{ form.notification }}
                                <label class="form-check-label" for="{{ form.notification.id_for_label }}">
                                    الإشعارات
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                {{ form.is_static_in_main_screen }}
                                <label class="form-check-label" for="{{ form.is_static_in_main_screen.id_for_label }}">
                                    ثابت في الشاشة الرئيسية
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{% url 'products_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
