import firebase_admin
from firebase_admin import credentials, firestore, db
import json
from datetime import datetime, timedelta
from django.conf import settings
import os
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class FirebaseService:
    """خدمة Firebase لإدارة الطلبات"""
    
    _instance = None
    _db = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FirebaseService, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """تهيئة Firebase"""
        try:
            # التحقق من وجود Firebase مهيأ مسبقاً
            if not firebase_admin._apps:
                # مسار ملف الإعدادات
                config_path = os.path.join(settings.BASE_DIR, 'firebase_config.json')
                
                if os.path.exists(config_path):
                    cred = credentials.Certificate(config_path)
                    # تهيئة Firebase مع Realtime Database
                    firebase_admin.initialize_app(cred, {
                        'databaseURL': 'https://zad-k22-default-rtdb.asia-southeast1.firebasedatabase.app/'
                    })
                    logger.info("تم تهيئة Firebase بنجاح")
                    print("تم تهيئة Firebase بنجاح")
                else:
                    logger.error("ملف إعدادات Firebase غير موجود")
                    raise FileNotFoundError("ملف firebase_config.json غير موجود")

            # الحصول على قواعد البيانات
            self._db = firestore.client()
            self._realtime_db = db.reference()
            logger.info("تم الاتصال بقاعدة بيانات Firestore و Realtime Database")
            print("تم الاتصال بقاعدة بيانات Firestore و Realtime Database")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة Firebase: {str(e)}")
            raise
    
    @property
    def db(self):
        """الحصول على قاعدة بيانات Firestore"""
        if self._db is None:
            self._initialize()
        return self._db

    @property
    def realtime_db(self):
        """الحصول على قاعدة بيانات Realtime"""
        if not hasattr(self, '_realtime_db') or self._realtime_db is None:
            self._initialize()
        return self._realtime_db
    
    def get_all_orders(self) -> Dict[str, Any]:
        """الحصول على جميع الطلبات من Firebase"""
        try:
            # الحصول على جميع الطلبات من مجموعة orders
            orders_ref = self.db.collection('orders')
            docs = orders_ref.stream()

            # تنظيم الطلبات حسب المتجر
            organized_orders = {}

            for doc in docs:
                order_data = doc.to_dict()
                if order_data:
                    store_name = order_data.get('storeName', 'متجر غير محدد')
                    order_id = order_data.get('id', doc.id)

                    if store_name not in organized_orders:
                        organized_orders[store_name] = {}

                    organized_orders[store_name][order_id] = order_data

            logger.info(f"تم جلب {len(organized_orders)} متجر من Firebase")
            return organized_orders

        except Exception as e:
            logger.error(f"خطأ في جلب الطلبات من Firebase: {str(e)}")
            return {}
    
    def get_orders_by_date_range(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على الطلبات في نطاق تاريخ معين"""
        try:
            all_orders = self.get_all_orders()
            filtered_orders = {}

            for store_name, store_orders in all_orders.items():
                filtered_store_orders = {}

                for order_id, order_data in store_orders.items():
                    # تحويل تاريخ الطلب
                    created_at = order_data.get('createdAt')
                    if created_at:
                        try:
                            # التعامل مع DatetimeWithNanoseconds أو string
                            if hasattr(created_at, 'replace'):
                                # إذا كان DatetimeWithNanoseconds
                                order_date = created_at.replace(tzinfo=None)
                            else:
                                # إذا كان string
                                order_date = datetime.fromisoformat(str(created_at).replace('Z', '+00:00'))
                                order_date = order_date.replace(tzinfo=None)

                            # التحقق من وجود التاريخ في النطاق
                            if start_date <= order_date <= end_date:
                                filtered_store_orders[order_id] = order_data
                        except (ValueError, AttributeError) as e:
                            logger.warning(f"تاريخ غير صحيح في الطلب {order_id}: {created_at} - {str(e)}")
                            continue

                if filtered_store_orders:
                    filtered_orders[store_name] = filtered_store_orders

            logger.info(f"تم فلترة الطلبات للفترة من {start_date} إلى {end_date}")
            return filtered_orders
            
        except Exception as e:
            logger.error(f"خطأ في فلترة الطلبات بالتاريخ: {str(e)}")
            return {}
    
    def get_today_orders(self) -> Dict[str, Any]:
        """الحصول على طلبات اليوم"""
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow = today + timedelta(days=1)
        return self.get_orders_by_date_range(today, tomorrow)
    
    def get_orders_statistics(self, orders_data: Dict[str, Any]) -> Dict[str, Any]:
        """حساب إحصائيات الطلبات"""
        try:
            stats = {
                'total_orders': 0,
                'total_revenue': 0,
                'orders_by_status': defaultdict(int),
                'orders_by_store': defaultdict(int),
                'orders_by_payment_method': defaultdict(int),
                'top_products': defaultdict(int),
                'top_customers': defaultdict(int),
                'hourly_distribution': defaultdict(int),
                'daily_distribution': defaultdict(int),
                'average_order_value': 0,
                'delivery_areas': defaultdict(int)
            }
            
            all_order_values = []
            
            for store_name, store_orders in orders_data.items():
                for _, order_data in store_orders.items():
                    # إحصائيات أساسية
                    stats['total_orders'] += 1
                    stats['orders_by_store'][store_name] += 1

                    # الإيرادات - استخدام total بدلاً من storeSubtotal
                    total_amount = order_data.get('total', 0)
                    stats['total_revenue'] += total_amount
                    all_order_values.append(total_amount)

                    # الحالة
                    status = order_data.get('status', 'unknown')
                    stats['orders_by_status'][status] += 1

                    # طريقة الدفع
                    payment_method = order_data.get('paymentMethod', 'unknown')
                    stats['orders_by_payment_method'][payment_method] += 1

                    # العملاء - استخدام userName بدلاً من customerName
                    customer_name = order_data.get('userName', 'غير محدد')
                    stats['top_customers'][customer_name] += 1

                    # المناطق
                    delivery_address = order_data.get('deliveryAddress', {})
                    area = delivery_address.get('district', 'غير محدد')
                    stats['delivery_areas'][area] += 1

                    # المنتجات - استخدام name بدلاً من productName
                    items = order_data.get('items', [])
                    for item in items:
                        product_name = item.get('name', 'غير محدد')
                        quantity = item.get('quantity', 1)
                        stats['top_products'][product_name] += quantity

                    # التوزيع الزمني - استخدام createdAt بدلاً من orderDate
                    created_at = order_data.get('createdAt')
                    if created_at:
                        try:
                            # التعامل مع DatetimeWithNanoseconds أو string
                            if hasattr(created_at, 'replace'):
                                # إذا كان DatetimeWithNanoseconds
                                order_date = created_at.replace(tzinfo=None)
                            else:
                                # إذا كان string
                                order_date = datetime.fromisoformat(str(created_at).replace('Z', '+00:00'))
                                order_date = order_date.replace(tzinfo=None)

                            hour = order_date.hour
                            day = order_date.strftime('%Y-%m-%d')

                            stats['hourly_distribution'][hour] += 1
                            stats['daily_distribution'][day] += 1
                        except (ValueError, AttributeError):
                            continue
            
            # حساب متوسط قيمة الطلب
            if all_order_values:
                stats['average_order_value'] = sum(all_order_values) / len(all_order_values)
            
            # تحويل defaultdict إلى dict عادي وترتيب النتائج
            stats['orders_by_status'] = dict(stats['orders_by_status'])
            stats['orders_by_store'] = dict(sorted(stats['orders_by_store'].items(), key=lambda x: x[1], reverse=True))
            stats['orders_by_payment_method'] = dict(stats['orders_by_payment_method'])
            stats['top_products'] = dict(sorted(stats['top_products'].items(), key=lambda x: x[1], reverse=True)[:10])
            stats['top_customers'] = dict(sorted(stats['top_customers'].items(), key=lambda x: x[1], reverse=True)[:10])
            stats['delivery_areas'] = dict(sorted(stats['delivery_areas'].items(), key=lambda x: x[1], reverse=True))
            stats['hourly_distribution'] = dict(sorted(stats['hourly_distribution'].items()))
            stats['daily_distribution'] = dict(sorted(stats['daily_distribution'].items()))
            
            logger.info(f"تم حساب الإحصائيات لـ {stats['total_orders']} طلب")
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {str(e)}")
            return {}
    
    def listen_to_orders_changes(self, callback):
        """الاستماع للتغييرات في الطلبات (Real-time)"""
        try:
            def on_snapshot(col_snapshot, changes, read_time):
                for change in changes:
                    if change.type.name == 'ADDED' or change.type.name == 'MODIFIED':
                        logger.info(f"تم تحديث الطلبات: {change.type.name}")
                        callback(change.document.to_dict())

            # الاستماع للتغييرات في مجموعة الطلبات
            collection_ref = self.db.collection('orders')
            collection_watch = collection_ref.on_snapshot(on_snapshot)

            logger.info("تم بدء الاستماع للتغييرات في الطلبات")
            return collection_watch

        except Exception as e:
            logger.error(f"خطأ في الاستماع للتغييرات: {str(e)}")
            return None

    # ==================== دوال إدارة العروض ====================

    def send_offer_to_realtime(self, offer_data: Dict[str, Any]) -> bool:
        """إرسال عرض إلى Firebase Realtime Database"""
        try:
            # تحضير بيانات العرض
            offer_payload = {
                'id': offer_data.get('id'),
                'title': offer_data.get('title', ''),
                'description': offer_data.get('description', ''),
                'discount_percentage': float(offer_data.get('discount_percentage', 0)),
                'start_date': offer_data.get('start_date').isoformat() if offer_data.get('start_date') else None,
                'end_date': offer_data.get('end_date').isoformat() if offer_data.get('end_date') else None,
                'is_active': offer_data.get('is_active', True),
                'offer_type': offer_data.get('offer_type', 'general'),
                'minimum_order_amount': float(offer_data.get('minimum_order_amount', 0)),
                'maximum_discount_amount': float(offer_data.get('maximum_discount_amount', 0)),
                'usage_limit': offer_data.get('usage_limit'),
                'used_count': offer_data.get('used_count', 0),
                'applicable_stores': offer_data.get('applicable_stores', []),
                'applicable_categories': offer_data.get('applicable_categories', []),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'created_by': offer_data.get('created_by', 'admin'),
                'image_url': offer_data.get('image_url', ''),
                'terms_and_conditions': offer_data.get('terms_and_conditions', ''),
                'promo_code': offer_data.get('promo_code', ''),
                'target_audience': offer_data.get('target_audience', 'all'),
                'priority': offer_data.get('priority', 1)
            }

            # إرسال إلى Realtime Database
            offers_ref = self.realtime_db.child('offers')
            offer_ref = offers_ref.child(str(offer_data.get('id')))
            offer_ref.set(offer_payload)

            logger.info(f"تم إرسال العرض {offer_data.get('id')} إلى Firebase Realtime Database")
            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال العرض إلى Firebase: {str(e)}")
            return False

    def update_offer_in_realtime(self, offer_id: int, offer_data: Dict[str, Any]) -> bool:
        """تحديث عرض في Firebase Realtime Database"""
        try:
            # تحضير بيانات التحديث
            update_payload = {
                'title': offer_data.get('title', ''),
                'description': offer_data.get('description', ''),
                'discount_percentage': float(offer_data.get('discount_percentage', 0)),
                'start_date': offer_data.get('start_date').isoformat() if offer_data.get('start_date') else None,
                'end_date': offer_data.get('end_date').isoformat() if offer_data.get('end_date') else None,
                'is_active': offer_data.get('is_active', True),
                'offer_type': offer_data.get('offer_type', 'general'),
                'minimum_order_amount': float(offer_data.get('minimum_order_amount', 0)),
                'maximum_discount_amount': float(offer_data.get('maximum_discount_amount', 0)),
                'usage_limit': offer_data.get('usage_limit'),
                'applicable_stores': offer_data.get('applicable_stores', []),
                'applicable_categories': offer_data.get('applicable_categories', []),
                'updated_at': datetime.now().isoformat(),
                'image_url': offer_data.get('image_url', ''),
                'terms_and_conditions': offer_data.get('terms_and_conditions', ''),
                'promo_code': offer_data.get('promo_code', ''),
                'target_audience': offer_data.get('target_audience', 'all'),
                'priority': offer_data.get('priority', 1)
            }

            # تحديث في Realtime Database
            offer_ref = self.realtime_db.child('offers').child(str(offer_id))
            offer_ref.update(update_payload)

            logger.info(f"تم تحديث العرض {offer_id} في Firebase Realtime Database")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث العرض في Firebase: {str(e)}")
            return False

    def delete_offer_from_realtime(self, offer_id: int) -> bool:
        """حذف عرض من Firebase Realtime Database"""
        try:
            offer_ref = self.realtime_db.child('offers').child(str(offer_id))
            offer_ref.delete()

            logger.info(f"تم حذف العرض {offer_id} من Firebase Realtime Database")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف العرض من Firebase: {str(e)}")
            return False

    def get_offers_from_realtime(self) -> Dict[str, Any]:
        """الحصول على جميع العروض من Firebase Realtime Database"""
        try:
            offers_ref = self.realtime_db.child('offers')
            offers_data = offers_ref.get()

            if offers_data:
                logger.info(f"تم جلب {len(offers_data)} عرض من Firebase Realtime Database")
                return offers_data
            else:
                logger.info("لا توجد عروض في Firebase Realtime Database")
                return {}

        except Exception as e:
            logger.error(f"خطأ في جلب العروض من Firebase: {str(e)}")
            return {}

    def toggle_offer_status_in_realtime(self, offer_id: int, is_active: bool) -> bool:
        """تغيير حالة العرض في Firebase Realtime Database"""
        try:
            offer_ref = self.realtime_db.child('offers').child(str(offer_id))
            offer_ref.update({
                'is_active': is_active,
                'updated_at': datetime.now().isoformat()
            })

            status_text = "تفعيل" if is_active else "إلغاء تفعيل"
            logger.info(f"تم {status_text} العرض {offer_id} في Firebase Realtime Database")
            return True

        except Exception as e:
            logger.error(f"خطأ في تغيير حالة العرض في Firebase: {str(e)}")
            return False

    # ==================== دوال إدارة تسعير التوصيل ====================

    def send_delivery_pricing_to_firestore(self, pricing_data: Dict[str, Any]) -> bool:
        """إرسال تسعير التوصيل إلى Firebase Firestore"""
        try:
            # تحضير بيانات التسعير
            pricing_payload = {
                'id': pricing_data.get('id'),
                'area_id': pricing_data.get('area_id'),
                'area_name': pricing_data.get('area_name', ''),
                'distance_from_center': float(pricing_data.get('distance_from_center', 0)),
                'delivery_cost': float(pricing_data.get('delivery_cost', 0)),
                'estimated_time_minutes': pricing_data.get('estimated_time_minutes', 30),
                'is_active': pricing_data.get('is_active', True),
                'priority': pricing_data.get('priority', 1),
                'special_instructions': pricing_data.get('special_instructions', ''),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'created_by': pricing_data.get('created_by', 'admin'),
                'coordinates': {
                    'latitude': float(pricing_data.get('latitude', 0)) if pricing_data.get('latitude') else None,
                    'longitude': float(pricing_data.get('longitude', 0)) if pricing_data.get('longitude') else None
                },
                'coverage_radius': float(pricing_data.get('coverage_radius', 5000)),  # بالمتر
                'peak_hour_multiplier': float(pricing_data.get('peak_hour_multiplier', 1.0)),
                'minimum_order_amount': float(pricing_data.get('minimum_order_amount', 0)),
                'free_delivery_threshold': float(pricing_data.get('free_delivery_threshold', 0))
            }

            # إرسال إلى Firestore
            doc_ref = self.db.collection('delivery_pricing').document(str(pricing_data.get('id')))
            doc_ref.set(pricing_payload)

            logger.info(f"تم إرسال تسعير التوصيل {pricing_data.get('id')} إلى Firebase Firestore")
            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال تسعير التوصيل إلى Firebase: {str(e)}")
            return False

    def update_delivery_pricing_in_firestore(self, pricing_id: int, pricing_data: Dict[str, Any]) -> bool:
        """تحديث تسعير التوصيل في Firebase Firestore"""
        try:
            # تحضير بيانات التحديث
            update_payload = {
                'area_name': pricing_data.get('area_name', ''),
                'distance_from_center': float(pricing_data.get('distance_from_center', 0)),
                'delivery_cost': float(pricing_data.get('delivery_cost', 0)),
                'estimated_time_minutes': pricing_data.get('estimated_time_minutes', 30),
                'is_active': pricing_data.get('is_active', True),
                'priority': pricing_data.get('priority', 1),
                'special_instructions': pricing_data.get('special_instructions', ''),
                'updated_at': datetime.now().isoformat(),
                'coordinates': {
                    'latitude': float(pricing_data.get('latitude', 0)) if pricing_data.get('latitude') else None,
                    'longitude': float(pricing_data.get('longitude', 0)) if pricing_data.get('longitude') else None
                },
                'coverage_radius': float(pricing_data.get('coverage_radius', 5000)),
                'peak_hour_multiplier': float(pricing_data.get('peak_hour_multiplier', 1.0)),
                'minimum_order_amount': float(pricing_data.get('minimum_order_amount', 0)),
                'free_delivery_threshold': float(pricing_data.get('free_delivery_threshold', 0))
            }

            # تحديث في Firestore
            doc_ref = self.db.collection('delivery_pricing').document(str(pricing_id))
            doc_ref.update(update_payload)

            logger.info(f"تم تحديث تسعير التوصيل {pricing_id} في Firebase Firestore")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث تسعير التوصيل في Firebase: {str(e)}")
            return False

    def delete_delivery_pricing_from_firestore(self, pricing_id: int) -> bool:
        """حذف تسعير التوصيل من Firebase Firestore"""
        try:
            doc_ref = self.db.collection('delivery_pricing').document(str(pricing_id))
            doc_ref.delete()

            logger.info(f"تم حذف تسعير التوصيل {pricing_id} من Firebase Firestore")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف تسعير التوصيل من Firebase: {str(e)}")
            return False

    def get_delivery_pricing_from_firestore(self) -> Dict[str, Any]:
        """الحصول على جميع تسعيرات التوصيل من Firebase Firestore"""
        try:
            collection_ref = self.db.collection('delivery_pricing')
            docs = collection_ref.stream()

            pricing_data = {}
            for doc in docs:
                pricing_data[doc.id] = doc.to_dict()

            logger.info(f"تم جلب {len(pricing_data)} تسعير توصيل من Firebase Firestore")
            return pricing_data

        except Exception as e:
            logger.error(f"خطأ في جلب تسعيرات التوصيل من Firebase: {str(e)}")
            return {}

    def toggle_delivery_pricing_status_in_firestore(self, pricing_id: int, is_active: bool) -> bool:
        """تغيير حالة تسعير التوصيل في Firebase Firestore"""
        try:
            doc_ref = self.db.collection('delivery_pricing').document(str(pricing_id))
            doc_ref.update({
                'is_active': is_active,
                'updated_at': datetime.now().isoformat()
            })

            status_text = "تفعيل" if is_active else "إلغاء تفعيل"
            logger.info(f"تم {status_text} تسعير التوصيل {pricing_id} في Firebase Firestore")
            return True

        except Exception as e:
            logger.error(f"خطأ في تغيير حالة تسعير التوصيل في Firebase: {str(e)}")
            return False
    
    def get_order_details(self, store_name: str, order_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل طلب معين"""
        try:
            all_orders = self.get_all_orders()
            store_orders = all_orders.get(store_name, {})
            return store_orders.get(order_id)
            
        except Exception as e:
            logger.error(f"خطأ في جلب تفاصيل الطلب {order_id}: {str(e)}")
            return None
    
    def format_order_data(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق بيانات الطلب للعرض"""
        try:
            # تحويل التواريخ
            formatted_order = order_data.copy()

            # تحويل التواريخ - التعامل مع DatetimeWithNanoseconds و string
            date_fields = ['createdAt', 'updatedAt']
            for field in date_fields:
                if field in formatted_order and formatted_order[field]:
                    try:
                        date_value = formatted_order[field]
                        if hasattr(date_value, 'replace'):
                            # إذا كان DatetimeWithNanoseconds
                            date_obj = date_value.replace(tzinfo=None)
                        else:
                            # إذا كان string
                            date_obj = datetime.fromisoformat(str(date_value).replace('Z', '+00:00'))
                            date_obj = date_obj.replace(tzinfo=None)

                        formatted_order[f'{field}_formatted'] = date_obj.strftime('%Y-%m-%d %H:%M:%S')
                        formatted_order[f'{field}_date'] = date_obj
                    except (ValueError, AttributeError):
                        formatted_order[f'{field}_formatted'] = str(formatted_order[field])

            # تنسيق العنوان
            delivery_address = formatted_order.get('deliveryAddress', {})
            if delivery_address:
                formatted_order['full_address_formatted'] = delivery_address.get('fullAddress', '')
                formatted_order['delivery_area'] = delivery_address.get('district', 'غير محدد')
            else:
                formatted_order['full_address_formatted'] = 'غير محدد'
                formatted_order['delivery_area'] = 'غير محدد'

            # حساب إجمالي الكمية
            items = formatted_order.get('items', [])
            total_quantity = sum(item.get('quantity', 0) for item in items)
            formatted_order['total_quantity'] = total_quantity

            # إضافة معلومات العميل
            formatted_order['customerName'] = formatted_order.get('userName', 'غير محدد')
            formatted_order['customerPhone'] = formatted_order.get('userPhone', 'غير محدد')
            formatted_order['customerId'] = formatted_order.get('userId', 'غير محدد')

            # إضافة معلومات المبلغ
            formatted_order['storeSubtotal'] = formatted_order.get('total', 0)

            # ترجمة الحالة
            status_translations = {
                'new': 'جديد',
                'pending': 'قيد الانتظار',
                'accepted': 'مقبول',
                'preparing': 'قيد التحضير',
                'ready': 'جاهز',
                'out_for_delivery': 'في الطريق',
                'delivered': 'تم التوصيل',
                'cancelled': 'ملغي',
                'rejected': 'مرفوض'
            }

            status = formatted_order.get('status', 'unknown')
            formatted_order['status_arabic'] = status_translations.get(status, status)

            # ترجمة طريقة الدفع
            payment_translations = {
                'cash': 'نقداً',
                'card': 'بطاقة',
                'online': 'دفع إلكتروني'
            }

            payment_method = formatted_order.get('paymentMethod', 'unknown')
            formatted_order['payment_method_arabic'] = payment_translations.get(payment_method, payment_method)

            return formatted_order

        except Exception as e:
            logger.error(f"خطأ في تنسيق بيانات الطلب: {str(e)}")
            return order_data

# إنشاء instance واحد للاستخدام
firebase_service = FirebaseService()
