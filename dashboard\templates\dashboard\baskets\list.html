{% extends 'dashboard/base.html' %}

{% block title %}السلال - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">إدارة السلال</h1>
        <p class="text-muted">عرض وإدارة جميع سلال التسوق في النظام</p>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-basket-shopping me-2"></i>
            قائمة السلال ({{ baskets.count }})
        </h5>
    </div>
    <div class="card-body">
        {% if baskets %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>المدينة</th>
                            <th>عدد العناصر</th>
                            <th>السعر الإجمالي</th>
                            <th>الحالة</th>
                            <th>تاريخ الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for basket in baskets %}
                        <tr>
                            <td><strong>{{ basket.bill_numbers }}</strong></td>
                            <td>{{ basket.peson_name|default:"غير محدد" }}</td>
                            <td>
                                {% if basket.phone_number %}
                                    <a href="tel:{{ basket.phone_number }}" class="text-decoration-none">
                                        <i class="fas fa-phone me-1"></i>
                                        {{ basket.phone_number }}
                                    </a>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>{{ basket.city|default:"غير محدد" }}</td>
                            <td>
                                <span class="badge bg-info">{{ basket.all_items }}</span>
                            </td>
                            <td>
                                <strong>{{ basket.all_price }} د.ع</strong>
                            </td>
                            <td>
                                {% if basket.enables == 1 %}
                                    <span class="badge bg-success">مؤكد</span>
                                {% else %}
                                    <span class="badge bg-warning">غير مؤكد</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    {{ basket.request_date|date:"Y/m/d" }}<br>
                                    <small class="text-muted">{{ basket.request_date|time:"H:i" }}</small>
                                </div>
                            </td>
                            <td>
                                <a href="{% url 'baskets_detail' basket.pk %}" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                    عرض التفاصيل
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5>{{ baskets.count }}</h5>
                            <small>إجمالي السلال</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>0</h5>
                            <small>سلال مؤكدة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>0</h5>
                            <small>سلال معلقة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h5>0</h5>
                            <small>إجمالي المبيعات</small>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-basket-shopping fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد سلال</h5>
                <p class="text-muted">لم يتم إنشاء أي سلال تسوق حتى الآن</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
