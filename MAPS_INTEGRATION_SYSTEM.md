# 🗺️ نظام الخرائط التفاعلية للمتاجر

## 🌟 نظرة عامة
تم إضافة نظام خرائط تفاعلي متطور باستخدام Google Maps API لتحديد وعرض مواقع المتاجر بطريقة سهلة ودقيقة.

## ✨ الميزات المضافة

### 🎯 **1. خريطة تفاعلية لإضافة/تعديل المتجر:**

#### **الوظائف الأساسية:**
- **خريطة تفاعلية** بحجم 400px مع تصميم جميل
- **علامة قابلة للسحب** لتحديد الموقع بدقة
- **النقر على الخريطة** لتحديد موقع جديد
- **تحديث تلقائي** لحقول خط العرض والطول
- **عرض العنوان** التلقائي للموقع المحدد

#### **أزرار التحكم المتقدمة:**
```html
<!-- موقعي الحالي -->
<button onclick="getCurrentLocation()">
    <i class="fas fa-crosshairs"></i>
    موقعي الحالي
</button>

<!-- البحث عن عنوان -->
<button onclick="searchLocation()">
    <i class="fas fa-search"></i>
    البحث عن عنوان
</button>

<!-- وسط بغداد -->
<button onclick="centerOnBaghdad()">
    <i class="fas fa-city"></i>
    وسط بغداد
</button>
```

#### **البحث الذكي:**
- **حقل بحث تفاعلي** مع اقتراحات تلقائية
- **تقييد البحث** على العراق فقط
- **بحث بالعربية والإنجليزية**
- **اقتراحات فورية** أثناء الكتابة

### 🗺️ **2. خريطة عرض موقع المتجر:**

#### **في صفحة تفاصيل المتجر:**
- **خريطة عرض** بحجم 400px
- **علامة مخصصة** بتصميم جميل
- **نافذة معلومات** تفاعلية
- **دائرة تغطية** لإظهار منطقة الخدمة
- **روابط خارجية** لخرائط جوجل والاتجاهات

#### **نافذة المعلومات التفاعلية:**
```html
<div class="info-window">
    <!-- صورة المتجر -->
    <img src="store-image.jpg" alt="Store">
    
    <!-- معلومات أساسية -->
    <h6>اسم المتجر</h6>
    <small>نوع المتجر</small>
    
    <!-- تفاصيل الاتصال -->
    <div>📍 المنطقة</div>
    <div>👤 اسم المسؤول</div>
    <div>📞 رقم الهاتف</div>
    
    <!-- شارات خاصة -->
    <span class="badge">متوفر 24 ساعة</span>
    
    <!-- زر الاتجاهات -->
    <a href="google-maps-directions">
        🧭 الحصول على الاتجاهات
    </a>
</div>
```

### 🎨 **3. التصميم والواجهة:**

#### **تحسينات CSS:**
```css
.map-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.map-controls {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 0.5rem;
}

.search-container .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.map-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

#### **تأثيرات تفاعلية:**
- **تأثيرات hover** على الأزرار
- **انتقالات سلسة** للعناصر
- **تأثير bounce** للعلامة عند التحديد
- **إشعارات جميلة** للعمليات

### 🔧 **4. الوظائف JavaScript المتقدمة:**

#### **تهيئة الخريطة:**
```javascript
function initMap() {
    // الحصول على الإحداثيات الحالية
    const currentLat = parseFloat(latInput.value) || BAGHDAD_LAT;
    const currentLng = parseFloat(lngInput.value) || BAGHDAD_LNG;
    
    // إنشاء الخريطة مع إعدادات متقدمة
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 13,
        center: { lat: currentLat, lng: currentLng },
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        styles: [/* تخصيص المظهر */]
    });
    
    // إنشاء العلامة القابلة للسحب
    marker = new google.maps.Marker({
        position: { lat: currentLat, lng: currentLng },
        map: map,
        draggable: true,
        animation: google.maps.Animation.DROP
    });
}
```

#### **تحديد الموقع الحالي:**
```javascript
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            
            moveMarker(lat, lng);
            map.setCenter({ lat: lat, lng: lng });
            map.setZoom(16);
            
            showNotification('تم تحديد موقعك الحالي بنجاح!', 'success');
        }, handleLocationError);
    }
}
```

#### **البحث الجغرافي:**
```javascript
function performSearch() {
    const query = searchInput.value.trim();
    
    geocoder.geocode({ 
        address: query,
        componentRestrictions: { country: 'iq' }
    }, function(results, status) {
        if (status === 'OK' && results[0]) {
            const location = results[0].geometry.location;
            moveMarker(location.lat(), location.lng());
            showNotification('تم العثور على الموقع بنجاح!', 'success');
        }
    });
}
```

#### **تحديث الإحداثيات:**
```javascript
function updateCoordinates(lat, lng) {
    document.getElementById('lat-field').value = lat.toFixed(6);
    document.getElementById('lng-field').value = lng.toFixed(6);
    
    // الحصول على العنوان
    getAddressFromCoordinates(lat, lng);
}
```

### 🎯 **5. خريطة عرض المتجر:**

#### **علامة مخصصة:**
```javascript
const storeIcon = {
    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#dc3545"/>
            <circle cx="12" cy="9" r="1.5" fill="white"/>
        </svg>
    `),
    scaledSize: new google.maps.Size(40, 40),
    anchor: new google.maps.Point(20, 40)
};
```

#### **دائرة التغطية:**
```javascript
const coverageCircle = new google.maps.Circle({
    strokeColor: '#28a745',
    strokeOpacity: 0.8,
    strokeWeight: 2,
    fillColor: '#28a745',
    fillOpacity: 0.1,
    map: map,
    center: storeLocation,
    radius: 2000 // 2 كيلومتر
});
```

### 🔗 **6. التكامل مع النظام:**

#### **حفظ الإحداثيات:**
- **تحديث تلقائي** لحقول lat و long
- **تزامن مع النموذج** عند الحفظ
- **التحقق من صحة البيانات** قبل الإرسال

#### **عرض الموقع:**
- **تحميل تلقائي** للخريطة في صفحة التفاصيل
- **عرض معلومات شاملة** في نافذة المعلومات
- **روابط خارجية** لخرائط جوجل

### 🚀 **7. الميزات المتقدمة:**

#### **الإشعارات التفاعلية:**
```javascript
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    notification.innerHTML = `
        <i class="fas fa-${getIcon(type)}"></i>
        ${message}
        <button class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 5000);
}
```

#### **البحث التلقائي:**
```javascript
autocomplete = new google.maps.places.Autocomplete(searchInput, {
    componentRestrictions: { country: 'iq' },
    fields: ['place_id', 'geometry', 'name', 'formatted_address']
});

autocomplete.addListener('place_changed', function() {
    const place = autocomplete.getPlace();
    if (place.geometry) {
        moveMarker(place.geometry.location.lat(), place.geometry.location.lng());
    }
});
```

### 🎯 **8. سيناريوهات الاستخدام:**

#### **إضافة متجر جديد:**
1. افتح صفحة إضافة متجر `/stores/create/`
2. املأ المعلومات الأساسية
3. انتقل لقسم "الموقع الجغرافي"
4. استخدم إحدى الطرق لتحديد الموقع:
   - **النقر على الخريطة**
   - **سحب العلامة**
   - **البحث عن عنوان**
   - **استخدام الموقع الحالي**
5. تأكد من صحة الإحداثيات
6. احفظ المتجر

#### **عرض موقع متجر:**
1. اذهب لصفحة تفاصيل المتجر
2. انتقل لقسم "موقع المتجر على الخريطة"
3. شاهد الموقع على الخريطة التفاعلية
4. انقر على العلامة لعرض المعلومات
5. استخدم "الحصول على الاتجاهات" للتنقل

### 🔧 **9. الإعدادات التقنية:**

#### **Google Maps API:**
```html
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key=API_KEY&libraries=places&callback=initMap">
</script>
```

#### **المكتبات المستخدمة:**
- **Maps JavaScript API** - للخرائط الأساسية
- **Places API** - للبحث والاقتراحات
- **Geocoding API** - لتحويل الإحداثيات لعناوين

#### **الإحداثيات الافتراضية:**
```javascript
const BAGHDAD_LAT = 33.3152;  // خط عرض بغداد
const BAGHDAD_LNG = 44.3661;  // خط طول بغداد
```

### 🎉 **10. الفوائد المحققة:**

#### **✅ للمستخدمين:**
- **سهولة تحديد الموقع** بدقة عالية
- **واجهة بديهية** وسهلة الاستخدام
- **بحث ذكي** مع اقتراحات فورية
- **عرض تفاعلي** لمواقع المتاجر

#### **✅ للنظام:**
- **دقة عالية** في حفظ المواقع
- **تكامل سلس** مع قاعدة البيانات
- **أداء محسن** مع تحميل تدريجي
- **تجربة مستخدم ممتازة**

#### **✅ للعملاء:**
- **عرض دقيق** لمواقع المتاجر
- **اتجاهات سهلة** للوصول للمتجر
- **معلومات شاملة** في مكان واحد
- **تكامل مع خرائط جوجل**

### 🎯 **الخلاصة:**

تم إضافة نظام خرائط تفاعلي متطور يشمل:

1. **خريطة تفاعلية** لتحديد مواقع المتاجر
2. **بحث ذكي** مع اقتراحات تلقائية
3. **تحديد الموقع الحالي** تلقائياً
4. **عرض مواقع المتاجر** بطريقة جميلة
5. **نوافذ معلومات** تفاعلية وشاملة
6. **تكامل مع خرائط جوجل** للاتجاهات
7. **تصميم متجاوب** وجميل
8. **إشعارات تفاعلية** للعمليات

**النظام الآن يوفر تجربة خرائط متكاملة ومتطورة لإدارة مواقع المتاجر!** 🗺️✨
