{% extends 'dashboard/base.html' %}
{% load static %}

{% block title %}تفاصيل التقييم{% endblock %}

{% block extra_css %}
<style>
    .rating-stars {
        color: #f6c23e;
        font-size: 2rem;
    }
    .info-card {
        background: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #4e73df;
    }
    .rating-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 50px;
    }
    .comment-box {
        background: #fff;
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    .admin-response {
        background: #e7f3ff;
        border-left: 4px solid #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-star text-warning"></i>
            تفاصيل التقييم #{{ rating.id }}
        </h1>
        <div>
            <a href="{% url 'ratings_edit' rating.id %}" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i>
                تعديل
            </a>
            <a href="{% url 'ratings_respond' rating.id %}" class="btn btn-success btn-sm">
                <i class="fas fa-reply"></i>
                رد الإدارة
            </a>
            <a href="{% url 'ratings_list' %}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- معلومات التقييم الأساسية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        معلومات التقييم
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <!-- نوع التقييم -->
                            <div class="info-card">
                                <h6 class="text-primary mb-2">نوع التقييم</h6>
                                {% if rating.rating_type == 'store' %}
                                    <span class="badge badge-primary rating-badge">
                                        <i class="fas fa-store"></i>
                                        تقييم متجر
                                    </span>
                                {% elif rating.rating_type == 'customer' %}
                                    <span class="badge badge-success rating-badge">
                                        <i class="fas fa-user"></i>
                                        تقييم عميل
                                    </span>
                                {% elif rating.rating_type == 'delivery' %}
                                    <span class="badge badge-warning rating-badge">
                                        <i class="fas fa-motorcycle"></i>
                                        تقييم عامل توصيل
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- التقييم بالنجوم -->
                            <div class="info-card">
                                <h6 class="text-primary mb-2">التقييم</h6>
                                <div class="rating-stars">
                                    {% for i in "12345" %}
                                        {% if rating.rating_value >= forloop.counter %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="text-muted h5">({{ rating.rating_value }}/5)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <!-- المُقيَّم -->
                            <div class="info-card">
                                <h6 class="text-primary mb-2">المُقيَّم</h6>
                                <div class="h5 text-dark">{{ rating.rated_name }}</div>
                                {% if rating.rated_store %}
                                    <small class="text-muted">
                                        <i class="fas fa-store"></i>
                                        متجر
                                    </small>
                                {% elif rating.rated_customer %}
                                    <small class="text-muted">
                                        <i class="fas fa-user"></i>
                                        عميل
                                    </small>
                                {% elif rating.rated_delivery_person %}
                                    <small class="text-muted">
                                        <i class="fas fa-motorcycle"></i>
                                        عامل توصيل
                                    </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- المُقيِّم -->
                            <div class="info-card">
                                <h6 class="text-primary mb-2">المُقيِّم</h6>
                                <div class="h5 text-dark">{{ rating.reviewer_name }}</div>
                                {% if rating.reviewer_customer %}
                                    <small class="text-muted">
                                        <i class="fas fa-user"></i>
                                        عميل
                                    </small>
                                {% elif rating.reviewer_store %}
                                    <small class="text-muted">
                                        <i class="fas fa-store"></i>
                                        متجر
                                    </small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التعليق -->
            {% if rating.comment %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comment"></i>
                        التعليق
                    </h6>
                </div>
                <div class="card-body">
                    <div class="comment-box">
                        <p class="mb-0">{{ rating.comment }}</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- رد الإدارة -->
            {% if rating.admin_response %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-reply"></i>
                        رد الإدارة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="comment-box admin-response">
                        <p class="mb-0">{{ rating.admin_response }}</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            <!-- معلومات إضافية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <!-- حالة التقييم -->
                    <div class="mb-3">
                        <h6 class="text-primary">حالة التقييم:</h6>
                        <div>
                            {% if rating.is_verified %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check-circle"></i>
                                    موثق
                                </span>
                            {% else %}
                                <span class="badge badge-warning">
                                    <i class="fas fa-clock"></i>
                                    غير موثق
                                </span>
                            {% endif %}
                        </div>
                        <div class="mt-2">
                            {% if rating.is_public %}
                                <span class="badge badge-info">
                                    <i class="fas fa-eye"></i>
                                    عام
                                </span>
                            {% else %}
                                <span class="badge badge-secondary">
                                    <i class="fas fa-eye-slash"></i>
                                    خاص
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الطلب المرتبط -->
                    {% if rating.order %}
                    <div class="mb-3">
                        <h6 class="text-primary">الطلب المرتبط:</h6>
                        <a href="{% url 'orders_detail' rating.order.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-shopping-cart"></i>
                            طلب #{{ rating.order.id }}
                        </a>
                    </div>
                    {% endif %}

                    <!-- التواريخ -->
                    <div class="mb-3">
                        <h6 class="text-primary">التواريخ:</h6>
                        <div class="small text-muted">
                            <div class="mb-1">
                                <i class="fas fa-plus-circle"></i>
                                <strong>تاريخ الإنشاء:</strong><br>
                                {{ rating.created_at|date:"d/m/Y H:i" }}
                            </div>
                            {% if rating.updated_at != rating.created_at %}
                            <div>
                                <i class="fas fa-edit"></i>
                                <strong>آخر تحديث:</strong><br>
                                {{ rating.updated_at|date:"d/m/Y H:i" }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-tools"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'ratings_edit' rating.id %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i>
                            تعديل التقييم
                        </a>
                        
                        <a href="{% url 'ratings_respond' rating.id %}" class="btn btn-success btn-sm">
                            <i class="fas fa-reply"></i>
                            إضافة رد الإدارة
                        </a>
                        
                        {% if rating.rated_store %}
                        <a href="{% url 'store_ratings' rating.rated_store.id %}" class="btn btn-info btn-sm">
                            <i class="fas fa-star"></i>
                            تقييمات المتجر
                        </a>
                        {% endif %}
                        
                        {% if rating.rated_customer %}
                        <a href="{% url 'customer_ratings' rating.rated_customer.id %}" class="btn btn-info btn-sm">
                            <i class="fas fa-star"></i>
                            تقييمات العميل
                        </a>
                        {% endif %}
                        
                        <a href="{% url 'ratings_delete' rating.id %}" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا التقييم؟')">
                            <i class="fas fa-trash"></i>
                            حذف التقييم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.info-card').hover(
        function() {
            $(this).addClass('shadow-sm');
        },
        function() {
            $(this).removeClass('shadow-sm');
        }
    );
});
</script>
{% endblock %}
