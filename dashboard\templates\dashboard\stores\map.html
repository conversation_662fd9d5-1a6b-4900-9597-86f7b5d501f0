{% extends 'dashboard/base.html' %}

{% block title %}خريطة المتاجر التفاعلية - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'stores_list' %}">المتاجر</a></li>
<li class="breadcrumb-item active">الخريطة التفاعلية</li>
{% endblock %}

{% block extra_css %}
<style>
.map-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.map-container {
    height: 70vh;
    min-height: 500px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    position: relative;
}

.filters-panel {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.total { border-left-color: #667eea; }
.stat-card.active { border-left-color: #28a745; }
.stat-card.hours24 { border-left-color: #17a2b8; }
.stat-card.featured { border-left-color: #ffc107; }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.map-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    min-width: 200px;
}

.legend {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: 10px;
    border: 2px solid white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.icon-active { background: #28a745; }
.icon-inactive { background: #dc3545; }
.icon-featured { background: #ffc107; }
.icon-24hours { background: #17a2b8; }

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    border-radius: 16px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.filter-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.clear-btn {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    color: white;
}

.info-window {
    max-width: 300px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.info-window img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.info-window h6 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.info-window .store-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    margin-bottom: 10px;
    display: inline-block;
}

.info-window .detail-item {
    margin-bottom: 5px;
    font-size: 13px;
    color: #666;
}

.info-window .detail-item i {
    width: 16px;
    margin-left: 5px;
    color: #667eea;
}

.info-window .badges {
    margin: 10px 0;
}

.info-window .badge {
    font-size: 10px;
    padding: 3px 8px;
    margin-left: 3px;
    border-radius: 10px;
}

.info-window .btn-detail {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    text-decoration: none;
    display: inline-block;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.info-window .btn-detail:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}

.cluster-icon {
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 3px solid white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    text-align: center;
    font-size: 14px;
}

/* تخصيص العلامات */
.custom-store-marker {
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

/* تحسين استقرار العلامة */
.custom-store-marker > div {
    position: relative !important;
    transform-origin: center bottom !important;
    transition: filter 0.2s ease !important;
}

/* تأثير hover محسن وثابت */
.custom-store-marker:hover .store-marker-container {
    filter: brightness(1.15) drop-shadow(0 4px 10px rgba(0,0,0,0.4)) !important;
    z-index: 1000 !important;
}

/* منع تحرك العلامة */
.leaflet-marker-icon {
    transition: filter 0.3s ease !important;
}

.leaflet-marker-icon:hover {
    filter: brightness(1.1) drop-shadow(0 3px 6px rgba(0,0,0,0.3)) !important;
}



/* منع تحرك العلامة عند hover */
.leaflet-marker-pane .leaflet-marker-icon {
    transform-origin: center bottom !important;
}



/* إصلاح مشكلة الهروب نهائياً */
.leaflet-zoom-animated .leaflet-marker-icon {
    transition: transform 0.25s cubic-bezier(0,0,0.25,1) !important;
}



/* تخصيص النوافذ المنبثقة */
.leaflet-popup-content-wrapper {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-tip {
    background: white;
}

.leaflet-popup-content {
    margin: 0;
    padding: 0;
}

/* تخصيص أزرار التحكم */
.leaflet-control-zoom a {
    border-radius: 6px;
    font-size: 16px;
    background: white;
    color: #333;
    border: 1px solid #ccc;
    transition: all 0.3s ease;
}

.leaflet-control-zoom a:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

/* تخصيص التجميع */
.marker-cluster-small {
    background-color: rgba(40, 167, 69, 0.6);
}

.marker-cluster-small div {
    background-color: rgba(40, 167, 69, 0.8);
}

.marker-cluster-medium {
    background-color: rgba(255, 193, 7, 0.6);
}

.marker-cluster-medium div {
    background-color: rgba(255, 193, 7, 0.8);
}

.marker-cluster-large {
    background-color: rgba(220, 53, 69, 0.6);
}

.marker-cluster-large div {
    background-color: rgba(220, 53, 69, 0.8);
}

.marker-cluster {
    border-radius: 50%;
}

.marker-cluster div {
    border-radius: 50%;
    text-align: center;
    font-weight: bold;
    color: white;
}

/* تحسين النافذة المنبثقة للمتاجر */
.store-info-popup {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 380px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.popup-header {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    overflow: hidden;
}

.store-image-container {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.store-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.store-image:hover {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.store-image-placeholder {
    height: 120px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: rgba(255, 255, 255, 0.8);
}

.store-basic-info {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.1);
}

.store-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.store-type-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.popup-body {
    padding: 20px;
}

.info-section {
    margin-bottom: 20px;
}

.info-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #f0f0f0;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
}

.info-icon {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 12px;
    flex-shrink: 0;
}

.info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    margin-bottom: 2px;
}

.info-value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.phone-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.phone-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.coordinates {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
}

.price {
    font-weight: 700;
    color: #28a745;
    background: #d4edda;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #c3e6cb;
}

.store-description {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin: 0;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.status-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.featured {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.hours24 {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.additional-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.store-id {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.popup-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.button-row {
    display: flex;
    gap: 8px;
}

.button-row .btn-action {
    flex: 1;
}

.btn-action {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496, #5a32a3);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #e8590c);
    color: #212529;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #3d4142);
    color: white;
}

.btn-dark {
    background: linear-gradient(135deg, #343a40, #212529);
    color: white;
}

.btn-dark:hover {
    background: linear-gradient(135deg, #23272b, #16181b);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

/* تحسينات إضافية للنافذة المتقدمة */
.enhanced-popup .leaflet-popup-content {
    margin: 0 !important;
    padding: 0 !important;
}

.store-info-popup {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 380px;
}

.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    padding: 20px;
    text-align: center;
}

.store-image-container {
    position: relative;
    margin-bottom: 15px;
}

.store-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.store-image:hover {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px;
    border-radius: 50%;
    font-size: 14px;
}

.store-image-placeholder {
    height: 120px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    color: #6c757d;
    font-size: 48px;
}

.store-basic-info {
    text-align: center;
}

.store-name {
    margin: 0 0 10px 0;
    font-size: 20px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.store-type-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.popup-body {
    padding: 20px;
    background: white;
}

.info-section {
    margin-bottom: 20px;
}

.info-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
    display: flex;
    align-items: center;
}

.section-title i {
    color: #667eea;
    margin-left: 8px;
}

.info-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-row:last-child {
    border-bottom: none;
}

.info-icon {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 12px;
}

.info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 2px;
}

.info-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.phone-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
}

.phone-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.coordinates {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    color: #495057;
}

.price {
    font-weight: 700;
    color: #28a745;
    background: #d4edda;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #c3e6cb;
}

.store-description, .store-note {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    color: #495057;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    border-left: 4px solid #667eea;
}

.status-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.featured {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.hours24 {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.additional-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .info-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.info-item .info-value {
    font-size: 13px;
    color: #333;
    font-weight: 600;
}

.store-id {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.popup-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.button-row {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-action {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    flex: 1;
    min-width: 0;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.btn-action i {
    margin-left: 6px;
}

/* تحسينات للنافذة الشاملة */
.complete-store-popup .leaflet-popup-content {
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 8px;
    overflow: hidden;
    max-height: 500px;
    overflow-y: auto;
}

.complete-store-popup .leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-height: 500px;
}

.complete-store-popup .leaflet-popup-tip {
    background: white;
}

/* تحسين التمرير */
.complete-store-popup .leaflet-popup-content::-webkit-scrollbar {
    width: 6px;
}

.complete-store-popup .leaflet-popup-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.complete-store-popup .leaflet-popup-content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.complete-store-popup .leaflet-popup-content::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تأثيرات hover للأزرار في النافذة الشاملة */
.complete-store-popup a:hover,
.complete-store-popup button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين عرض النافذة على الشاشات الصغيرة */
@media (max-height: 600px) {
    .complete-store-popup .leaflet-popup-content {
        max-height: 400px;
    }

    .complete-store-popup .leaflet-popup-content-wrapper {
        max-height: 400px;
    }
}

@media (max-height: 500px) {
    .complete-store-popup .leaflet-popup-content {
        max-height: 300px;
    }

    .complete-store-popup .leaflet-popup-content-wrapper {
        max-height: 300px;
    }
}

/* تحسين النافذة المنبثقة في Leaflet */
.enhanced-popup .leaflet-popup-content-wrapper {
    padding: 0;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: none;
}

.enhanced-popup .leaflet-popup-content {
    margin: 0;
    padding: 0;
    border-radius: 16px;
    overflow: hidden;
}

.enhanced-popup .leaflet-popup-tip {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block content %}
<!-- رأس الصفحة -->
<div class="map-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">
                <i class="fas fa-map-marked-alt me-2"></i>
                خريطة المتاجر التفاعلية
            </h2>
            <p class="mb-0 opacity-75">عرض جميع المتاجر والمطاعم المتعاقدة على الخريطة مع إمكانيات فلترة متقدمة</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex gap-2 justify-content-end">
                <a href="{% url 'stores_list' %}" class="btn btn-light">
                    <i class="fas fa-list me-2"></i>
                    عرض القائمة
                </a>
                <a href="{% url 'stores_create' %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة متجر
                </a>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<div class="stats-grid">
    <div class="stat-card total">
        <div class="stat-number text-primary">{{ total_stores }}</div>
        <div class="stat-label">إجمالي المتاجر</div>
    </div>
    <div class="stat-card active">
        <div class="stat-number text-success">{{ active_stores }}</div>
        <div class="stat-label">متاجر نشطة</div>
    </div>
    <div class="stat-card hours24">
        <div class="stat-number text-info">{{ stores_24h }}</div>
        <div class="stat-label">متوفر 24 ساعة</div>
    </div>
    <div class="stat-card featured">
        <div class="stat-number text-warning">{{ featured_stores }}</div>
        <div class="stat-label">متاجر مميزة</div>
    </div>
</div>

<!-- لوحة الفلاتر -->
<div class="filters-panel">
    <h6 class="mb-3">
        <i class="fas fa-filter me-2"></i>
        فلترة المتاجر على الخريطة
    </h6>
    
    <form id="mapFilters">
        <div class="row">
            <div class="col-md-2 filter-group">
                <label class="form-label">المنطقة</label>
                <select name="area" class="form-select">
                    <option value="">جميع المناطق</option>
                    {% for area in areas %}
                    <option value="{{ area.id }}">{{ area.area_name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2 filter-group">
                <label class="form-label">نوع المتجر</label>
                <select name="store_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for store_type in store_types %}
                    <option value="{{ store_type.id }}">{{ store_type.store_type }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2 filter-group">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            
            <div class="col-md-2 filter-group">
                <label class="form-label">متوفر 24 ساعة</label>
                <select name="is_24_hours" class="form-select">
                    <option value="">الكل</option>
                    <option value="true">نعم</option>
                    <option value="false">لا</option>
                </select>
            </div>
            
            <div class="col-md-3 filter-group">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="اسم المتجر أو المسؤول...">
            </div>
            
            <div class="col-md-1 filter-group d-flex align-items-end">
                <button type="button" class="filter-btn w-100" onclick="applyFilters()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <button type="button" class="clear-btn" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>
                    مسح الفلاتر
                </button>
                <span class="ms-3 text-muted" id="resultsCount">جاري التحميل...</span>
            </div>
        </div>
    </form>
</div>

<!-- الخريطة -->
<div class="map-container">
    <div id="map" style="width: 100%; height: 100%;"></div>
    
    <!-- تحكم في الخريطة -->
    <div class="map-controls">
        <h6 class="mb-3">أدوات الخريطة</h6>
        <div class="d-grid gap-2">
            <button class="btn btn-sm btn-outline-primary" onclick="centerOnSanaa()">
                <i class="fas fa-city me-2"></i>
                وسط صنعاء
            </button>
            <button class="btn btn-sm btn-outline-success" onclick="fitAllStores()">
                <i class="fas fa-expand-arrows-alt me-2"></i>
                عرض جميع المتاجر
            </button>
            <button class="btn btn-sm btn-outline-info" onclick="toggleClustering()">
                <i class="fas fa-layer-group me-2"></i>
                تجميع المتاجر
            </button>
        </div>
    </div>
    
    <!-- مفتاح الخريطة -->
    <div class="legend">
        <h6 class="mb-2">مفتاح الخريطة</h6>
        <div class="legend-item">
            <div class="legend-icon icon-active"></div>
            <span>متجر نشط</span>
        </div>
        <div class="legend-item">
            <div class="legend-icon icon-inactive"></div>
            <span>متجر غير نشط</span>
        </div>
        <div class="legend-item">
            <div class="legend-icon icon-featured"></div>
            <span>متجر مميز</span>
        </div>
        <div class="legend-item">
            <div class="legend-icon icon-24hours"></div>
            <span>متوفر 24 ساعة</span>
        </div>
    </div>
    
    <!-- شاشة التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-3 text-muted">جاري تحميل المتاجر...</p>
        </div>
    </div>
</div>

<!-- تحميل Leaflet للخرائط -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<!-- تحميل MarkerCluster للـ Leaflet -->
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<script>
let map;
let markers = [];
let markerCluster;
let allStoresData = [];
let clusteringEnabled = true;

// إحداثيات صنعاء
const SANAA_CENTER = [15.3694, 44.1910];

// تهيئة الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initMap();
});

// تهيئة الخريطة
function initMap() {
    // إنشاء الخريطة باستخدام Leaflet
    map = L.map('map').setView(SANAA_CENTER, 11);

    // إضافة طبقة الخريطة
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);

    // إنشاء مجموعة التجميع
    markerCluster = L.markerClusterGroup({
        iconCreateFunction: function(cluster) {
            const count = cluster.getChildCount();
            let color = '#28a745';
            let size = 'small';

            if (count > 10) {
                color = '#dc3545';
                size = 'large';
            } else if (count > 5) {
                color = '#ffc107';
                size = 'medium';
            }

            return L.divIcon({
                html: `<div class="cluster-icon cluster-${size}" style="background: ${color}; width: ${size === 'large' ? '50px' : size === 'medium' ? '40px' : '30px'}; height: ${size === 'large' ? '50px' : size === 'medium' ? '40px' : '30px'}; line-height: ${size === 'large' ? '50px' : size === 'medium' ? '40px' : '30px'};">${count}</div>`,
                className: 'custom-cluster-icon',
                iconSize: L.point(size === 'large' ? 50 : size === 'medium' ? 40 : 30, size === 'large' ? 50 : size === 'medium' ? 40 : 30)
            });
        }
    });

    // إضافة مراقب لأحداث تحريك الخريطة
    map.on('moveend', function() {
        // التحقق من النوافذ المفتوحة وضمان بقائها ضمن الحدود
        markers.forEach(marker => {
            if (marker.isPopupOpen()) {
                ensurePopupInView(marker);
            }
        });
    });

    // مراقب لأحداث تغيير حجم الخريطة
    map.on('resize', function() {
        setTimeout(() => {
            markers.forEach(marker => {
                if (marker.isPopupOpen()) {
                    ensurePopupInView(marker);
                }
            });
        }, 100);
    });

    // مراقب لأحداث التكبير والتصغير
    map.on('zoomend', function() {
        setTimeout(() => {
            markers.forEach(marker => {
                if (marker.isPopupOpen()) {
                    ensurePopupInView(marker);
                }
            });
        }, 200);
    });

    // مراقب دوري للتحقق من النوافذ المفتوحة
    setInterval(() => {
        markers.forEach(marker => {
            if (marker.isPopupOpen()) {
                const bounds = map.getBounds();
                const markerLatLng = marker.getLatLng();

                // إذا كان المتجر خارج الحدود المرئية
                if (!bounds.contains(markerLatLng)) {
                    // إضافة إشعار للمستخدم
                    if (!marker._outOfBoundsNotified) {
                        marker._outOfBoundsNotified = true;
                        showNotification(`نافذة معلومات ${marker.storeData.name} خارج الحدود المرئية`, 'info');

                        // إزالة الإشعار بعد 5 ثوان
                        setTimeout(() => {
                            marker._outOfBoundsNotified = false;
                        }, 5000);
                    }
                } else {
                    marker._outOfBoundsNotified = false;
                }
            }
        });
    }, 3000); // فحص كل 3 ثوان

    // تحميل المتاجر
    loadStores();
}

// تحميل بيانات المتاجر
function loadStores(filters = {}) {
    showLoading(true);

    // بناء URL مع الفلاتر
    const params = new URLSearchParams(filters);
    const url = `{% url 'stores_map' %}?${params.toString()}`;

    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        allStoresData = data.stores;
        updateMap(data.stores);
        updateResultsCount(data.total_count);
        showLoading(false);
    })
    .catch(error => {
        console.error('خطأ في تحميل البيانات:', error);
        showLoading(false);
        showNotification('حدث خطأ في تحميل بيانات المتاجر', 'error');
    });
}

// تحديث الخريطة بالمتاجر
function updateMap(stores) {
    // مسح العلامات السابقة
    clearMarkers();

    // إضافة علامات جديدة
    stores.forEach(store => {
        const marker = createStoreMarker(store);
        if (marker) {
            markers.push(marker);
        }
    });

    // تطبيق التجميع إذا كان مفعلاً
    if (clusteringEnabled) {
        applyMarkerClustering();
    } else {
        // إضافة العلامات مباشرة للخريطة
        markers.forEach(marker => {
            marker.addTo(map);
        });
    }

    // توسيط الخريطة على المتاجر إذا وجدت
    if (markers.length > 0) {
        fitMarkersInView();
    }
}

// إنشاء علامة للمتجر
function createStoreMarker(store) {
    // تحديد لون العلامة حسب حالة المتجر
    let iconColor = '#dc3545'; // أحمر للمتاجر غير النشطة

    if (store.is_active) {
        if (store.is_featured) {
            iconColor = '#ffc107'; // أصفر للمتاجر المميزة
        } else if (store.is_24_hours) {
            iconColor = '#17a2b8'; // أزرق للمتاجر 24 ساعة
        } else {
            iconColor = '#28a745'; // أخضر للمتاجر النشطة
        }
    }

    // إنشاء أيقونة مخصصة بسيطة وثابتة
    const icon = L.divIcon({
        html: `
            <div style="
                background: ${iconColor};
                border-radius: 50% 50% 50% 0;
                width: 25px;
                height: 25px;
                position: relative;
                transform: rotate(-45deg);
                border: 3px solid white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                cursor: pointer;
            ">
                <i class="fas fa-store" style="
                    color: white;
                    font-size: 10px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) rotate(45deg);
                "></i>
            </div>
        `,
        iconSize: [25, 25],
        iconAnchor: [12, 25],
        className: 'custom-store-marker'
    });

    // إنشاء العلامة
    const marker = L.marker([store.lat, store.lng], {
        icon: icon,
        title: store.name
    });

    // حفظ بيانات المتجر في العلامة
    marker.storeData = store;

    // ربط نافذة معلومات شاملة مع إمكانية إعادة الفتح
    marker.on('click', function(e) {
        console.log('تم النقر على المتجر:', store.name);
        console.log('حالة النافذة - مفتوحة:', marker.isPopupOpen());

        // دائماً اعرض المعلومات عند النقر
        showCompleteStoreInfo(store, marker);
    });





    return marker;
}

// عرض جميع معلومات المتجر الشاملة
function showCompleteStoreInfo(store, marker) {
    console.log('عرض جميع معلومات المتجر:', store.name);

    // إغلاق أي نوافذ مفتوحة أخرى
    map.closePopup();

    // إزالة أي popup مربوط مسبقاً لضمان إعادة الإنشاء
    if (marker.getPopup()) {
        marker.unbindPopup();
    }

    const content = `
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 400px; padding: 0; margin: 0;">
            <!-- رأس النافذة المحسن -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; text-align: center; border-radius: 8px 8px 0 0;">
                ${store.image ? `
                    <img src="${store.image}" alt="${store.name}" style="width: 60px; height: 60px; border-radius: 50%; border: 2px solid white; margin-bottom: 8px; object-fit: cover;">
                ` : `
                    <div style="width: 60px; height: 60px; border-radius: 50%; border: 2px solid white; margin: 0 auto 8px; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-store" style="font-size: 24px; color: white;"></i>
                    </div>
                `}
                <h3 style="margin: 0 0 4px 0; font-size: 16px; font-weight: bold;">${store.name}</h3>
                <span style="background: rgba(255,255,255,0.2); padding: 3px 10px; border-radius: 12px; font-size: 11px; border: 1px solid rgba(255,255,255,0.3);">${store.type}</span>
            </div>

            <!-- جسم النافذة -->
            <div style="padding: 15px; background: white;">
                <!-- معلومات الاتصال -->
                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 8px 0; color: #333; font-size: 13px; font-weight: 600; border-bottom: 1px solid #f0f0f0; padding-bottom: 4px;">
                        <i class="fas fa-info-circle" style="color: #667eea; margin-left: 6px;"></i>
                        معلومات الاتصال
                    </h4>

                    <div style="display: flex; align-items: center; margin: 6px 0; padding: 6px; background: #f8f9fa; border-radius: 4px;">
                        <div style="width: 24px; height: 24px; background: linear-gradient(135deg, #dc3545, #c82333); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 8px;">
                            <i class="fas fa-map-marker-alt" style="font-size: 10px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 10px; color: #6c757d; font-weight: 500;">المنطقة</div>
                            <div style="font-size: 12px; color: #333; font-weight: 600;">${store.area}</div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin: 6px 0; padding: 6px; background: #f8f9fa; border-radius: 4px;">
                        <div style="width: 24px; height: 24px; background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 8px;">
                            <i class="fas fa-user" style="font-size: 10px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 10px; color: #6c757d; font-weight: 500;">المسؤول</div>
                            <div style="font-size: 12px; color: #333; font-weight: 600;">${store.person_name}</div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin: 6px 0; padding: 6px; background: #f8f9fa; border-radius: 4px;">
                        <div style="width: 24px; height: 24px; background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 8px;">
                            <i class="fas fa-phone" style="font-size: 10px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 10px; color: #6c757d; font-weight: 500;">الهاتف</div>
                            <div style="font-size: 12px; color: #333; font-weight: 600;">
                                <a href="tel:${store.phone}" style="color: #007bff; text-decoration: none; font-weight: 600;">${store.phone}</a>
                            </div>
                        </div>
                    </div>

                    ${store.email && store.email !== 'غير محدد' ? `
                    <div style="display: flex; align-items: center; margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 6px;">
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 10px;">
                            <i class="fas fa-envelope" style="font-size: 12px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 11px; color: #6c757d; font-weight: 500;">البريد الإلكتروني</div>
                            <div style="font-size: 13px; color: #333; font-weight: 600;">${store.email}</div>
                        </div>
                    </div>
                    ` : ''}

                    <div style="display: flex; align-items: center; margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 6px;">
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #6c757d, #495057); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 10px;">
                            <i class="fas fa-map" style="font-size: 12px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 11px; color: #6c757d; font-weight: 500;">الإحداثيات</div>
                            <div style="font-size: 12px; color: #333; font-weight: 600; font-family: 'Courier New', monospace; background: #e9ecef; padding: 2px 6px; border-radius: 4px;">${store.coordinates_text}</div>
                        </div>
                    </div>
                </div>

                <!-- المعلومات المالية -->
                ${store.price_stor && store.price_stor > 0 ? `
                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 8px 0; color: #333; font-size: 13px; font-weight: 600; border-bottom: 1px solid #f0f0f0; padding-bottom: 4px;">
                        <i class="fas fa-money-bill-wave" style="color: #28a745; margin-left: 6px;"></i>
                        المعلومات المالية
                    </h4>

                    <div style="display: flex; align-items: center; margin: 8px 0; padding: 8px; background: #d4edda; border-radius: 6px; border: 1px solid #c3e6cb;">
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 10px;">
                            <i class="fas fa-coins" style="font-size: 12px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 11px; color: #155724; font-weight: 500;">إيجار المحل</div>
                            <div style="font-size: 14px; color: #155724; font-weight: 700;">${store.price_stor} ريال يمني</div>
                        </div>
                    </div>
                </div>
                ` : ''}

                <!-- الوصف والملاحظات -->
                ${store.description || store.note ? `
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px; font-weight: 600; border-bottom: 2px solid #f0f0f0; padding-bottom: 5px;">
                        <i class="fas fa-align-left" style="color: #6f42c1; margin-left: 8px;"></i>
                        تفاصيل إضافية
                    </h4>

                    ${store.description ? `
                    <div style="margin: 10px 0; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
                        <div style="font-size: 11px; color: #6c757d; font-weight: 500; margin-bottom: 5px;">الوصف</div>
                        <div style="font-size: 13px; color: #495057; line-height: 1.5;">${store.description}</div>
                    </div>
                    ` : ''}

                    ${store.note && store.note.trim() ? `
                    <div style="margin: 10px 0; padding: 12px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <div style="font-size: 11px; color: #856404; font-weight: 500; margin-bottom: 5px;">ملاحظات</div>
                        <div style="font-size: 13px; color: #856404; line-height: 1.5;">${store.note}</div>
                    </div>
                    ` : ''}
                </div>
                ` : ''}

                <!-- حالة المتجر -->
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px; font-weight: 600; border-bottom: 2px solid #f0f0f0; padding-bottom: 5px;">
                        <i class="fas fa-tags" style="color: #fd7e14; margin-left: 8px;"></i>
                        حالة المتجر
                    </h4>

                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <span style="padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: 500; display: flex; align-items: center; gap: 4px; ${store.is_active ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'}">
                            <i class="fas ${store.is_active ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                            ${store.is_active ? 'نشط' : 'غير نشط'}
                        </span>

                        ${store.is_featured ? `
                        <span style="padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: 500; display: flex; align-items: center; gap: 4px; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7;">
                            <i class="fas fa-star"></i>
                            مميز
                        </span>
                        ` : ''}

                        ${store.is_24_hours ? `
                        <span style="padding: 6px 12px; border-radius: 20px; font-size: 11px; font-weight: 500; display: flex; align-items: center; gap: 4px; background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;">
                            <i class="fas fa-clock"></i>
                            24 ساعة
                        </span>
                        ` : ''}
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px; font-weight: 600; border-bottom: 2px solid #f0f0f0; padding-bottom: 5px;">
                        <i class="fas fa-cog" style="color: #6c757d; margin-left: 8px;"></i>
                        معلومات النظام
                    </h4>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 0; border-bottom: 1px solid #e9ecef;">
                            <span style="font-size: 11px; color: #6c757d; font-weight: 500;">تاريخ الانضمام</span>
                            <span style="font-size: 12px; color: #333; font-weight: 600;">${store.created_date} ${store.created_time || ''}</span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 0; border-bottom: 1px solid #e9ecef;">
                            <span style="font-size: 11px; color: #6c757d; font-weight: 500;">معرف المتجر</span>
                            <span style="font-size: 12px; color: #333; font-weight: 600; font-family: 'Courier New', monospace; background: #e9ecef; padding: 2px 6px; border-radius: 4px;">#${store.id}</span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 0; border-bottom: 1px solid #e9ecef;">
                            <span style="font-size: 11px; color: #6c757d; font-weight: 500;">معرف Firebase</span>
                            <span style="font-size: 12px; color: #333; font-weight: 600; font-family: 'Courier New', monospace; background: #e9ecef; padding: 2px 6px; border-radius: 4px;">${store.id_store || 'غير محدد'}</span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 0; border-bottom: 1px solid #e9ecef;">
                            <span style="font-size: 11px; color: #6c757d; font-weight: 500;">اسم المستخدم</span>
                            <span style="font-size: 12px; color: #333; font-weight: 600;">${store.username || 'غير محدد'}</span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 0;">
                            <span style="font-size: 11px; color: #6c757d; font-weight: 500;">آخر تسجيل دخول</span>
                            <span style="font-size: 12px; color: #333; font-weight: 600;">${store.last_login || 'لم يسجل دخول'}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ذيل النافذة مع الأزرار -->
            <div style="background: #f8f9fa; padding: 12px 15px; border-top: 1px solid #e9ecef; border-radius: 0 0 8px 8px;">
                <div style="display: flex; flex-direction: column; gap: 6px;">
                    <!-- الصف الأول -->
                    <div style="display: flex; gap: 6px; justify-content: center;">
                        <a href="${store.detail_url}" style="flex: 1; padding: 8px 12px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; text-decoration: none; border-radius: 6px; font-size: 11px; font-weight: 500; text-align: center; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                            <i class="fas fa-eye" style="margin-left: 4px;"></i>
                            عرض التفاصيل
                        </a>
                        <a href="${store.edit_url || '#'}" style="flex: 1; padding: 8px 12px; background: linear-gradient(135deg, #ffc107, #fd7e14); color: #212529; text-decoration: none; border-radius: 6px; font-size: 11px; font-weight: 500; text-align: center; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                            <i class="fas fa-edit" style="margin-left: 4px;"></i>
                            تعديل
                        </a>
                    </div>

                    <!-- الصف الثاني -->
                    <div style="display: flex; gap: 6px; justify-content: center;">
                        <button onclick="getDirections(${store.lat}, ${store.lng}, '${store.name}')" style="flex: 1; padding: 8px 12px; background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; border-radius: 6px; font-size: 11px; font-weight: 500; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                            <i class="fas fa-route" style="margin-left: 4px;"></i>
                            الاتجاهات
                        </button>
                        <button onclick="copyCoordinates(${store.lat}, ${store.lng})" style="flex: 1; padding: 8px 12px; background: linear-gradient(135deg, #6c757d, #495057); color: white; border: none; border-radius: 6px; font-size: 11px; font-weight: 500; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                            <i class="fas fa-copy" style="margin-left: 4px;"></i>
                            نسخ الإحداثيات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إنشاء النافذة المنبثقة
    const popup = L.popup({
        maxWidth: 400,
        maxHeight: 500,
        closeButton: true,
        autoClose: false,
        closeOnClick: false,
        className: 'complete-store-popup',
        keepInView: true,
        autoPan: true,
        autoPanPadding: [20, 20]
    }).setContent(content);

    // ربط النافذة بالعلامة
    marker.bindPopup(popup);

    // فتح النافذة
    marker.openPopup();

    // مراقبة أحداث النافذة
    popup.on('remove', function() {
        console.log('تم إغلاق نافذة المتجر:', store.name);
        marker._popupClosed = true;
    });

    marker.on('popupopen', function() {
        console.log('تم فتح نافذة المتجر:', store.name);
        marker._popupClosed = false;
    });

    console.log('تم عرض جميع معلومات المتجر بنجاح');
}

// عرض معلومات المتجر
function showStoreInfo(store, marker) {
    console.log('عرض معلومات المتجر:', store.name);

    const content = `
        <div class="store-info-popup">
            <!-- رأس النافذة -->
            <div class="popup-header">
                ${store.image ? `
                    <div class="store-image-container">
                        <img src="${store.image}" alt="${store.name}" class="store-image">
                        <div class="image-overlay">
                            <i class="fas fa-store"></i>
                        </div>
                    </div>
                ` : `
                    <div class="store-image-placeholder">
                        <i class="fas fa-store"></i>
                    </div>
                `}

                <div class="store-basic-info">
                    <h5 class="store-name">${store.name}</h5>
                    <span class="store-type-badge">${store.type}</span>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="popup-body">
                <div class="info-section">
                    <h6 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الاتصال
                    </h6>

                    <div class="info-row">
                        <div class="info-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">المنطقة:</span>
                            <span class="info-value">${store.area}</span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">المسؤول:</span>
                            <span class="info-value">${store.person_name}</span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">الهاتف:</span>
                            <a href="tel:${store.phone}" class="phone-link">${store.phone}</a>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value">${store.email}</span>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">الإحداثيات:</span>
                            <span class="info-value coordinates">${store.coordinates_text || (store.lat.toFixed(6) + ', ' + store.lng.toFixed(6))}</span>
                        </div>
                    </div>

                    ${store.price_stor && store.price_stor > 0 ? `
                    <div class="info-row">
                        <div class="info-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">إيجار المحل:</span>
                            <span class="info-value price">${store.price_stor} ريال يمني</span>
                        </div>
                    </div>
                    ` : ''}
                </div>

                ${store.description ? `
                <div class="info-section">
                    <h6 class="section-title">
                        <i class="fas fa-align-left me-2"></i>
                        الوصف
                    </h6>
                    <p class="store-description">${store.description}</p>
                </div>
                ` : ''}

                <!-- حالة المتجر -->
                <div class="info-section">
                    <h6 class="section-title">
                        <i class="fas fa-tags me-2"></i>
                        حالة المتجر
                    </h6>
                    <div class="status-badges">
                        ${store.is_active ?
                            '<span class="status-badge active"><i class="fas fa-check-circle"></i> نشط</span>' :
                            '<span class="status-badge inactive"><i class="fas fa-times-circle"></i> غير نشط</span>'
                        }
                        ${store.is_featured ? '<span class="status-badge featured"><i class="fas fa-star"></i> مميز</span>' : ''}
                        ${store.is_24_hours ? '<span class="status-badge hours24"><i class="fas fa-clock"></i> متوفر 24 ساعة</span>' : ''}
                    </div>
                </div>

                ${store.note && store.note.trim() ? `
                <div class="info-section">
                    <h6 class="section-title">
                        <i class="fas fa-sticky-note me-2"></i>
                        ملاحظات
                    </h6>
                    <p class="store-note">${store.note}</p>
                </div>
                ` : ''}

                <!-- معلومات إضافية -->
                <div class="info-section">
                    <h6 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h6>
                    <div class="additional-info">
                        <div class="info-item">
                            <span class="info-label">تاريخ الانضمام:</span>
                            <span class="info-value">${store.created_date} ${store.created_time || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">معرف المتجر:</span>
                            <span class="info-value store-id">#${store.id}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">معرف Firebase:</span>
                            <span class="info-value store-id">${store.id_store || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">اسم المستخدم:</span>
                            <span class="info-value">${store.username || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">آخر تسجيل دخول:</span>
                            <span class="info-value">${store.last_login || 'لم يسجل دخول'}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار العمليات -->
            <div class="popup-footer">
                <div class="action-buttons">
                    <div class="button-row">
                        <a href="${store.detail_url}" class="btn-action btn-primary">
                            <i class="fas fa-eye me-2"></i>
                            عرض التفاصيل
                        </a>
                        <a href="${store.edit_url || '#'}" class="btn-action btn-warning">
                            <i class="fas fa-edit me-2"></i>
                            تعديل
                        </a>
                    </div>

                    <div class="button-row">
                        <button onclick="getDirections(${store.lat}, ${store.lng}, '${store.name}')" class="btn-action btn-success">
                            <i class="fas fa-route me-2"></i>
                            الاتجاهات
                        </button>
                        <button onclick="zoomToStore(${store.lat}, ${store.lng})" class="btn-action btn-info">
                            <i class="fas fa-search-plus me-2"></i>
                            تكبير
                        </button>
                    </div>

                    <div class="button-row">
                        <button onclick="copyCoordinates(${store.lat}, ${store.lng})" class="btn-action btn-secondary">
                            <i class="fas fa-copy me-2"></i>
                            نسخ الإحداثيات
                        </button>
                        <button onclick="shareStore('${store.name}', ${store.lat}, ${store.lng})" class="btn-action btn-dark">
                            <i class="fas fa-share-alt me-2"></i>
                            مشاركة
                        </button>
                    </div>

                    <div class="button-row">
                        <button onclick="centerPopupInView(${store.id})" class="btn-action btn-outline-primary">
                            <i class="fas fa-expand-arrows-alt me-2"></i>
                            توسيط النافذة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إنشاء النافذة المنبثقة مع إعدادات محسنة
    marker.bindPopup(content, {
        maxWidth: 400,
        className: 'enhanced-popup',
        closeButton: true,
        autoClose: false,
        closeOnClick: false,
        keepInView: true,
        autoPan: true,
        autoPanPadding: [20, 20]
    }).openPopup();

    console.log('تم فتح النافذة المتقدمة بنجاح');
}

// تطبيق تجميع العلامات
function applyMarkerClustering() {
    // مسح التجميع السابق
    map.removeLayer(markerCluster);
    markerCluster.clearLayers();

    // إضافة العلامات للتجميع
    markers.forEach(marker => {
        markerCluster.addLayer(marker);
    });

    // إضافة التجميع للخريطة
    map.addLayer(markerCluster);
}

// مسح جميع العلامات
function clearMarkers() {
    // مسح التجميع
    if (markerCluster) {
        map.removeLayer(markerCluster);
        markerCluster.clearLayers();
    }

    // مسح العلامات الفردية
    markers.forEach(marker => {
        map.removeLayer(marker);
    });
    markers = [];
}

// توسيط الخريطة على جميع العلامات
function fitMarkersInView() {
    if (markers.length === 0) return;

    const group = new L.featureGroup(markers);
    map.fitBounds(group.getBounds().pad(0.1));

    // التأكد من عدم التكبير أكثر من اللازم
    if (map.getZoom() > 15) {
        map.setZoom(15);
    }
}

// التوسيط على صنعاء
function centerOnSanaa() {
    map.setView(SANAA_CENTER, 11);
}

// عرض جميع المتاجر
function fitAllStores() {
    if (markers.length > 0) {
        fitMarkersInView();
    } else {
        centerOnSanaa();
    }
}

// تبديل تجميع العلامات
function toggleClustering() {
    clusteringEnabled = !clusteringEnabled;

    if (clusteringEnabled) {
        // إزالة العلامات الفردية وتطبيق التجميع
        markers.forEach(marker => map.removeLayer(marker));
        applyMarkerClustering();
        showNotification('تم تفعيل تجميع المتاجر', 'success');
    } else {
        // إزالة التجميع وإضافة العلامات الفردية
        map.removeLayer(markerCluster);
        markerCluster.clearLayers();
        markers.forEach(marker => marker.addTo(map));
        showNotification('تم إلغاء تجميع المتاجر', 'info');
    }
}

// تطبيق الفلاتر
function applyFilters() {
    const form = document.getElementById('mapFilters');
    const formData = new FormData(form);
    const filters = {};

    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            filters[key] = value.trim();
        }
    }

    loadStores(filters);
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('mapFilters').reset();
    loadStores();
}

// إظهار/إخفاء شاشة التحميل
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// تحديث عدد النتائج
function updateResultsCount(count) {
    document.getElementById('resultsCount').textContent = `عدد المتاجر المعروضة: ${count}`;
}

// الحصول على الاتجاهات
function getDirections(lat, lng, storeName) {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=${encodeURIComponent(storeName)}`;
    window.open(url, '_blank');
    showNotification(`تم فتح الاتجاهات إلى ${storeName}`, 'success');
}

// نسخ الإحداثيات
function copyCoordinates(lat, lng) {
    const coordinates = `${lat}, ${lng}`;
    navigator.clipboard.writeText(coordinates).then(() => {
        showNotification('تم نسخ الإحداثيات بنجاح!', 'success');
    }).catch(() => {
        // طريقة بديلة للنسخ
        const textArea = document.createElement('textarea');
        textArea.value = coordinates;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('تم نسخ الإحداثيات بنجاح!', 'success');
    });
}

// تكبير على المتجر
function zoomToStore(lat, lng) {
    map.setView([lat, lng], 18);
    showNotification('تم التكبير على موقع المتجر', 'info');
}

// مشاركة المتجر
function shareStore(storeName, lat, lng) {
    const shareData = {
        title: `متجر ${storeName}`,
        text: `شاهد موقع ${storeName} على الخريطة`,
        url: `https://www.google.com/maps?q=${lat},${lng}`
    };

    if (navigator.share) {
        navigator.share(shareData).then(() => {
            showNotification('تم مشاركة موقع المتجر بنجاح!', 'success');
        }).catch(() => {
            fallbackShare(shareData.url, storeName);
        });
    } else {
        fallbackShare(shareData.url, storeName);
    }
}

// مشاركة بديلة
function fallbackShare(url, storeName) {
    navigator.clipboard.writeText(url).then(() => {
        showNotification(`تم نسخ رابط موقع ${storeName} إلى الحافظة`, 'success');
    }).catch(() => {
        // فتح في نافذة جديدة كبديل أخير
        window.open(url, '_blank');
        showNotification(`تم فتح موقع ${storeName} في نافذة جديدة`, 'info');
    });
}

// ضمان بقاء النافذة ضمن حدود الخريطة
function ensurePopupInView(marker) {
    if (marker && marker.isPopupOpen()) {
        const popup = marker.getPopup();
        const popupLatLng = marker.getLatLng();

        // التحقق من أن النافذة ضمن حدود الخريطة المرئية
        const bounds = map.getBounds();

        if (!bounds.contains(popupLatLng)) {
            // إذا كانت النافذة خارج الحدود، حرك الخريطة لإظهارها
            map.setView(popupLatLng, map.getZoom(), {
                animate: true,
                duration: 0.5
            });
        }

        // إضافة padding للتأكد من ظهور النافذة بالكامل
        setTimeout(() => {
            if (marker.isPopupOpen()) {
                const popupElement = popup.getElement();
                if (popupElement) {
                    const rect = popupElement.getBoundingClientRect();
                    const mapContainer = map.getContainer().getBoundingClientRect();

                    // التحقق من أن النافذة لا تخرج من حدود الخريطة
                    if (rect.right > mapContainer.right ||
                        rect.left < mapContainer.left ||
                        rect.top < mapContainer.top ||
                        rect.bottom > mapContainer.bottom) {

                        // تحريك الخريطة لإظهار النافذة بالكامل
                        map.panTo(popupLatLng, {
                            animate: true,
                            duration: 0.3
                        });
                    }
                }
            }
        }, 200);
    }
}

// إعادة فتح النافذة المنبثقة للمتجر
function reopenStorePopup(storeId) {
    const marker = markers.find(m => m.storeData && m.storeData.id === storeId);
    if (marker) {
        if (marker._popupClosed || !marker.isPopupOpen()) {
            showStoreInfo(marker.storeData, marker);
            ensurePopupInView(marker);
        }
    }
}

// توسيط النافذة في الخريطة
function centerPopupInView(storeId) {
    const marker = markers.find(m => m.storeData && m.storeData.id === storeId);
    if (marker && marker.isPopupOpen()) {
        // توسيط الخريطة على المتجر
        map.setView(marker.getLatLng(), Math.max(map.getZoom(), 15), {
            animate: true,
            duration: 0.5
        });

        // التأكد من ظهور النافذة بالكامل
        setTimeout(() => {
            ensurePopupInView(marker);
            showNotification('تم توسيط النافذة في الخريطة', 'success');
        }, 600);
    } else {
        // إذا لم تكن النافذة مفتوحة، افتحها أولاً
        reopenStorePopup(storeId);
    }
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ربط أحداث الفلاتر
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق الفلاتر عند تغيير القيم
    const filterInputs = document.querySelectorAll('#mapFilters select, #mapFilters input');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.type !== 'text') {
                applyFilters();
            }
        });

        if (input.type === 'text') {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    applyFilters();
                }, 500);
            });
        }
    });
});
</script>

{% endblock %}
