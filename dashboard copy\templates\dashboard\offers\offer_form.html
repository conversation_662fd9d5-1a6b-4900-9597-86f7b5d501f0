{% extends 'dashboard/base.html' %}

{% block title %}{{ title }} - العروض الخاصة{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'special_offers_list' %}">العروض الخاصة</a></li>
<li class="breadcrumb-item active">
    {% if offer %}تعديل{% else %}إنشاء جديد{% endif %}
</li>
{% endblock %}

{% block extra_css %}
<style>
.offer-form-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #ff6b6b;
}

.form-section h6 {
    color: #ff6b6b;
    font-weight: 600;
    margin-bottom: 1rem;
}

.offer-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.offer-type-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.offer-type-card:hover {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.05);
}

.offer-type-card.selected {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.offer-type-card.selected::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff6b6b;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.offer-icon {
    font-size: 2.5rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.dynamic-fields {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.preview-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.btn-submit {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

.image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    margin-top: 1rem;
}

.help-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="offer-form-card">
            <!-- رأس النموذج -->
            <div class="form-header">
                <div class="mb-3">
                    <i class="fas fa-tags fa-3x"></i>
                </div>
                <h3 class="mb-0">{{ title }}</h3>
                <p class="mb-0 mt-2 opacity-75">
                    {% if offer %}
                        تعديل العرض الخاص وإعداداته
                    {% else %}
                        إنشاء عرض خاص جديد لجذب العملاء
                    {% endif %}
                </p>
            </div>

            <!-- النموذج -->
            <div class="card-body p-4">
                <form method="post" enctype="multipart/form-data" id="offerForm">
                    {% csrf_token %}
                    
                    <!-- المعلومات الأساسية -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-info-circle me-2"></i>
                            المعلومات الأساسية
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.title.id_for_label }}" class="form-label">عنوان العرض</label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="text-danger small">{{ form.title.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.target_audience.id_for_label }}" class="form-label">الجمهور المستهدف</label>
                                {{ form.target_audience }}
                                {% if form.target_audience.errors %}
                                    <div class="text-danger small">{{ form.target_audience.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">وصف العرض</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- نوع العرض -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-tag me-2"></i>
                            نوع العرض
                        </h6>
                        
                        <div class="offer-type-selector">
                            <div class="offer-type-card" data-type="discount_percentage">
                                <div class="offer-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <h6>خصم نسبة مئوية</h6>
                                <p class="small text-muted mb-0">نسبة من قيمة الطلب</p>
                            </div>
                            <div class="offer-type-card" data-type="discount_fixed">
                                <div class="offer-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <h6>خصم مبلغ ثابت</h6>
                                <p class="small text-muted mb-0">مبلغ ثابت من الطلب</p>
                            </div>
                            <div class="offer-type-card" data-type="buy_get">
                                <div class="offer-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <h6>اشتري واحصل على</h6>
                                <p class="small text-muted mb-0">كمية مجانية</p>
                            </div>
                            <div class="offer-type-card" data-type="free_delivery">
                                <div class="offer-icon">
                                    <i class="fas fa-shipping-fast"></i>
                                </div>
                                <h6>توصيل مجاني</h6>
                                <p class="small text-muted mb-0">بدون رسوم توصيل</p>
                            </div>
                            <div class="offer-type-card" data-type="bundle">
                                <div class="offer-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <h6>عرض حزمة</h6>
                                <p class="small text-muted mb-0">منتجات مجمعة</p>
                            </div>
                            <div class="offer-type-card" data-type="seasonal">
                                <div class="offer-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h6>عرض موسمي</h6>
                                <p class="small text-muted mb-0">مناسبات خاصة</p>
                            </div>
                        </div>
                        {{ form.offer_type }}
                        {% if form.offer_type.errors %}
                            <div class="text-danger small">{{ form.offer_type.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- تفاصيل العرض -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-cogs me-2"></i>
                            تفاصيل العرض
                        </h6>
                        
                        <!-- حقول الخصم -->
                        <div id="discountFields" class="dynamic-fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">نسبة الخصم (%)</label>
                                    {{ form.discount_percentage }}
                                    {% if form.discount_percentage.errors %}
                                        <div class="text-danger small">{{ form.discount_percentage.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.discount_amount.id_for_label }}" class="form-label">مبلغ الخصم (د.ع)</label>
                                    {{ form.discount_amount }}
                                    {% if form.discount_amount.errors %}
                                        <div class="text-danger small">{{ form.discount_amount.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.maximum_discount.id_for_label }}" class="form-label">الحد الأقصى للخصم (د.ع)</label>
                                    {{ form.maximum_discount }}
                                    {% if form.maximum_discount.errors %}
                                        <div class="text-danger small">{{ form.maximum_discount.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- حقول اشتري واحصل على -->
                        <div id="buyGetFields" class="dynamic-fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.buy_quantity.id_for_label }}" class="form-label">اشتري كمية</label>
                                    {{ form.buy_quantity }}
                                    {% if form.buy_quantity.errors %}
                                        <div class="text-danger small">{{ form.buy_quantity.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.get_quantity.id_for_label }}" class="form-label">احصل على كمية</label>
                                    {{ form.get_quantity }}
                                    {% if form.get_quantity.errors %}
                                        <div class="text-danger small">{{ form.get_quantity.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- الحد الأدنى لقيمة الطلب -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.minimum_order_value.id_for_label }}" class="form-label">الحد الأدنى لقيمة الطلب (د.ع)</label>
                                {{ form.minimum_order_value }}
                                {% if form.minimum_order_value.errors %}
                                    <div class="text-danger small">{{ form.minimum_order_value.errors }}</div>
                                {% endif %}
                                <div class="form-text">0 = بدون حد أدنى</div>
                            </div>
                        </div>
                    </div>

                    <!-- الفترة الزمنية -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-calendar me-2"></i>
                            الفترة الزمنية
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small">{{ form.start_date.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small">{{ form.end_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- الاستهداف -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-bullseye me-2"></i>
                            الاستهداف والتخصيص
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.target_stores.id_for_label }}" class="form-label">المتاجر المستهدفة</label>
                                {{ form.target_stores }}
                                {% if form.target_stores.errors %}
                                    <div class="text-danger small">{{ form.target_stores.errors }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.target_stores.help_text }}</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.target_areas.id_for_label }}" class="form-label">المناطق المستهدفة</label>
                                {{ form.target_areas }}
                                {% if form.target_areas.errors %}
                                    <div class="text-danger small">{{ form.target_areas.errors }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.target_areas.help_text }}</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.target_products.id_for_label }}" class="form-label">المنتجات المستهدفة</label>
                                {{ form.target_products }}
                                {% if form.target_products.errors %}
                                    <div class="text-danger small">{{ form.target_products.errors }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.target_products.help_text }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- الحدود والقيود -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-shield-alt me-2"></i>
                            الحدود والقيود
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.usage_limit_per_customer.id_for_label }}" class="form-label">حد الاستخدام لكل عميل</label>
                                {{ form.usage_limit_per_customer }}
                                {% if form.usage_limit_per_customer.errors %}
                                    <div class="text-danger small">{{ form.usage_limit_per_customer.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.total_usage_limit.id_for_label }}" class="form-label">حد الاستخدام الإجمالي</label>
                                {{ form.total_usage_limit }}
                                {% if form.total_usage_limit.errors %}
                                    <div class="text-danger small">{{ form.total_usage_limit.errors }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.total_usage_limit.help_text }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- كود الخصم -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-ticket-alt me-2"></i>
                            كود الخصم
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.requires_coupon_code }}
                                    <label class="form-check-label" for="{{ form.requires_coupon_code.id_for_label }}">
                                        يتطلب كود خصم
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.coupon_code.id_for_label }}" class="form-label">كود الخصم</label>
                                {{ form.coupon_code }}
                                {% if form.coupon_code.errors %}
                                    <div class="text-danger small">{{ form.coupon_code.errors }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.coupon_code.help_text }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات الإضافية -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-sliders-h me-2"></i>
                            الإعدادات الإضافية
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.is_featured }}
                                    <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                        عرض مميز
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.show_on_homepage }}
                                    <label class="form-check-label" for="{{ form.show_on_homepage.id_for_label }}">
                                        عرض في الصفحة الرئيسية
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الصور -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-images me-2"></i>
                            الصور والوسائط
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.image.id_for_label }}" class="form-label">صورة العرض</label>
                                {{ form.image }}
                                {% if form.image.errors %}
                                    <div class="text-danger small">{{ form.image.errors }}</div>
                                {% endif %}
                                {% if offer and offer.image %}
                                    <img src="{{ offer.image.url }}" class="image-preview" alt="صورة العرض">
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.banner_image.id_for_label }}" class="form-label">صورة البانر</label>
                                {{ form.banner_image }}
                                {% if form.banner_image.errors %}
                                    <div class="text-danger small">{{ form.banner_image.errors }}</div>
                                {% endif %}
                                {% if offer and offer.banner_image %}
                                    <img src="{{ offer.banner_image.url }}" class="image-preview" alt="صورة البانر">
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- الشروط والملاحظات -->
                    <div class="form-section">
                        <h6>
                            <i class="fas fa-file-alt me-2"></i>
                            الشروط والملاحظات
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.terms_and_conditions.id_for_label }}" class="form-label">الشروط والأحكام</label>
                                {{ form.terms_and_conditions }}
                                {% if form.terms_and_conditions.errors %}
                                    <div class="text-danger small">{{ form.terms_and_conditions.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات داخلية</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'special_offers_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="previewOffer()">
                                <i class="fas fa-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-save me-2"></i>
                                {% if offer %}تحديث العرض{% else %}إنشاء العرض{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قسم المساعدة -->
        <div class="help-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>
                نصائح لإنشاء عروض ناجحة
            </h6>
            <div class="row">
                <div class="col-md-4">
                    <h6 class="text-primary">العنوان والوصف</h6>
                    <p class="small">استخدم عناوين جذابة ووصف واضح يوضح قيمة العرض للعميل.</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-success">التوقيت</h6>
                    <p class="small">حدد فترة زمنية مناسبة - لا قصيرة جداً ولا طويلة جداً.</p>
                </div>
                <div class="col-md-4">
                    <h6 class="text-info">الاستهداف</h6>
                    <p class="small">استهدف الجمهور المناسب والمنتجات ذات الصلة لزيادة الفعالية.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const offerTypeSelect = document.getElementById('{{ form.offer_type.id_for_label }}');
    const typeCards = document.querySelectorAll('.offer-type-card');
    const discountFields = document.getElementById('discountFields');
    const buyGetFields = document.getElementById('buyGetFields');
    
    // تحديد النوع المحدد حالياً
    function updateSelectedType() {
        const selectedType = offerTypeSelect.value;
        
        // تحديث البطاقات
        typeCards.forEach(card => {
            card.classList.remove('selected');
            if (card.dataset.type === selectedType) {
                card.classList.add('selected');
            }
        });
        
        // إظهار/إخفاء الحقول
        discountFields.style.display = 'none';
        buyGetFields.style.display = 'none';
        
        if (selectedType === 'discount_percentage' || selectedType === 'discount_fixed') {
            discountFields.style.display = 'block';
        } else if (selectedType === 'buy_get') {
            buyGetFields.style.display = 'block';
        }
    }
    
    // ربط النقر على البطاقات
    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            offerTypeSelect.value = this.dataset.type;
            updateSelectedType();
        });
    });
    
    // ربط تغيير القائمة المنسدلة
    offerTypeSelect.addEventListener('change', updateSelectedType);
    
    // معاينة العرض
    window.previewOffer = function() {
        const title = document.getElementById('{{ form.title.id_for_label }}').value;
        const description = document.getElementById('{{ form.description.id_for_label }}').value;
        const offerType = offerTypeSelect.options[offerTypeSelect.selectedIndex].text;
        
        let preview = 'معاينة العرض:\n\n';
        preview += `العنوان: ${title}\n`;
        preview += `الوصف: ${description}\n`;
        preview += `النوع: ${offerType}\n`;
        
        alert(preview);
    };
    
    // تهيئة أولية
    updateSelectedType();
});
</script>
{% endblock %}
