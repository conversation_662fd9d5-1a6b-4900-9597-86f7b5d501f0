{% extends 'dashboard/base.html' %}

{% block title %}سجل تغييرات الطلب #{{ order.id }} - لوحة تحكم نظام توصيل الطلبات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'orders_list' %}">الطلبات</a></li>
<li class="breadcrumb-item"><a href="{% url 'orders_detail' order.pk %}">طلب #{{ order.id }}</a></li>
<li class="breadcrumb-item active">سجل التغييرات</li>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-right: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    right: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #28a745);
}

.timeline-item {
    position: relative;
    padding: 1.5rem 0;
    margin-right: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    right: -2.5rem;
    top: 2rem;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    border: 4px solid white;
    box-shadow: 0 0 0 3px #007bff;
    z-index: 1;
}

.timeline-item.status-change::before {
    background: #28a745;
    box-shadow: 0 0 0 3px #28a745;
}

.timeline-item.edit::before {
    background: #ffc107;
    box-shadow: 0 0 0 3px #ffc107;
}

.timeline-item.note::before {
    background: #17a2b8;
    box-shadow: 0 0 0 3px #17a2b8;
}

.timeline-item.delete::before {
    background: #dc3545;
    box-shadow: 0 0 0 3px #dc3545;
}

.timeline-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.timeline-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.timeline-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.timeline-body {
    padding: 1.5rem;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.action-icon.status-change { background: #28a745; }
.action-icon.edit { background: #ffc107; }
.action-icon.note { background: #17a2b8; }
.action-icon.create { background: #007bff; }
.action-icon.delete { background: #dc3545; }

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-history me-2"></i>
            سجل تغييرات الطلب #{{ order.id }}
        </h1>
        <p class="text-muted">تتبع جميع التغييرات والإجراءات المتعلقة بالطلب</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{% url 'orders_detail' order.pk %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للطلب
            </a>
            <a href="{% url 'orders_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>
                قائمة الطلبات
            </a>
        </div>
    </div>
</div>

<!-- معلومات الطلب السريعة -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <div class="d-flex align-items-center">
                    <div class="action-icon create me-3">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">طلب #{{ order.id }}</h6>
                        <small class="text-muted">{{ order.request_date|date:"Y/m/d H:i" }}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <strong>العميل:</strong> {{ order.custom.name|default:"غير محدد" }}
            </div>
            <div class="col-md-3">
                <strong>المنتج:</strong> {{ order.products.product_name|default:"غير محدد" }}
            </div>
            <div class="col-md-3">
                <strong>الحالة:</strong> 
                <span class="badge bg-primary">{{ order.get_order_state_display }}</span>
            </div>
        </div>
    </div>
</div>

<!-- سجل التغييرات -->
<div class="row">
    <div class="col-12">
        {% if history %}
            <div class="timeline">
                {% for entry in history %}
                <div class="timeline-item {{ entry.action_type }}">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <div class="action-icon {{ entry.action_type }} me-3">
                                            {% if entry.action_type == 'status_change' %}
                                                <i class="fas fa-exchange-alt"></i>
                                            {% elif entry.action_type == 'edit' %}
                                                <i class="fas fa-edit"></i>
                                            {% elif entry.action_type == 'note' %}
                                                <i class="fas fa-comment"></i>
                                            {% elif entry.action_type == 'create' %}
                                                <i class="fas fa-plus"></i>
                                            {% elif entry.action_type == 'delete' %}
                                                <i class="fas fa-trash"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ entry.title }}</h6>
                                            <small class="text-muted">{{ entry.timestamp|date:"Y/m/d H:i" }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <small class="text-muted">بواسطة: {{ entry.user.username }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-body">
                            <p class="mb-0">{{ entry.description }}</p>
                            {% if entry.details %}
                                <div class="mt-2">
                                    <small class="text-muted">{{ entry.details }}</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- حالة عدم وجود سجل -->
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-history"></i>
                        <h5>لا يوجد سجل تغييرات</h5>
                        <p class="mb-4">لم يتم تسجيل أي تغييرات على هذا الطلب بعد</p>
                        
                        <!-- سجل افتراضي -->
                        <div class="timeline">
                            <div class="timeline-item create">
                                <div class="timeline-card">
                                    <div class="timeline-header">
                                        <div class="d-flex align-items-center">
                                            <div class="action-icon create me-3">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">إنشاء الطلب</h6>
                                                <small class="text-muted">{{ order.request_date|date:"Y/m/d H:i" }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="timeline-body">
                                        <p class="mb-0">تم إنشاء الطلب #{{ order.id }} بنجاح</p>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                العميل: {{ order.custom.name|default:"غير محدد" }} | 
                                                المنتج: {{ order.products.product_name|default:"غير محدد" }} | 
                                                المبلغ: {{ order.all_price }} د.ع
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="{% url 'orders_edit' order.pk %}" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>
                                تعديل الطلب
                            </a>
                            <a href="{% url 'orders_detail' order.pk %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات السجل
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-2">
                    <i class="fas fa-clock me-1"></i>
                    يتم تسجيل جميع التغييرات تلقائياً
                </p>
                <p class="text-muted small mb-2">
                    <i class="fas fa-user me-1"></i>
                    يتم حفظ اسم المستخدم الذي قام بالتغيير
                </p>
                <p class="text-muted small mb-0">
                    <i class="fas fa-shield-alt me-1"></i>
                    لا يمكن حذف أو تعديل السجل
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="text-primary mb-0">{{ history|length|default:1 }}</h5>
                        <small class="text-muted">إجراء</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-success mb-0">1</h5>
                        <small class="text-muted">طلب</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info mb-0">{{ order.all_price }}</h5>
                        <small class="text-muted">د.ع</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
